﻿Module FormResizer

    ' هيكل لتخزين الأحجام والمواقع الأصلية
    Private Structure OriginalSizes
        Public Width As Integer
        Public Height As Integer
        Public Left As Integer
        Public Top As Integer
        Public FontSize As Single
    End Structure

    ' متغير لتخزين الحجم الأصلي للنموذج
    Private originalFormSize As Size

    ' قاموس لتخزين الأحجام الأصلية للعناصر
    Private originalControls As New Dictionary(Of Control, OriginalSizes)

    ' دالة لتهيئة الأحجام الأصلية عند تحميل النموذج
    Public Sub InitializeForm(form As Form)
        originalFormSize = form.Size
        originalControls.Clear()
        For Each ctrl As Control In form.Controls
            Dim sizes As New OriginalSizes With {
                .Width = ctrl.Width,
                .Height = ctrl.Height,
                .Left = ctrl.Left,
                .Top = ctrl.Top,
                .FontSize = ctrl.Font.Size
            }
            originalControls.Add(ctrl, sizes)
        Next
    End Sub

    ' دالة لضبط الأحجام بناءً على حجم النموذج الحالي
    Public Sub ResizeForm(form As Form)
        If originalFormSize.Width = 0 OrElse originalFormSize.Height = 0 Then Exit Sub

        Dim widthRatio As Single = form.Width / originalFormSize.Width
        Dim heightRatio As Single = form.Height / originalFormSize.Height

        For Each ctrl As Control In form.Controls
            If originalControls.ContainsKey(ctrl) Then
                Dim original As OriginalSizes = originalControls(ctrl)
                ctrl.Width = CInt(original.Width * widthRatio)
                ctrl.Height = CInt(original.Height * heightRatio)
                ctrl.Left = CInt(original.Left * widthRatio)
                ctrl.Top = CInt(original.Top * heightRatio)
                ctrl.Font = New Font(ctrl.Font.FontFamily, original.FontSize * Math.Min(widthRatio, heightRatio))
            End If
        Next
    End Sub

End Module