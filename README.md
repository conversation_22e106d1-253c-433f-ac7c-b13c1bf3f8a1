# نظام الدولية انترناشونال

نظام شامل لإدارة الموارد البشرية مبني باستخدام VB.NET، مصمم لتبسيط عمليات الموارد البشرية وإدارة الموظفين.

## 🌟 المميزات

### إدارة الموظفين
- إدارة كاملة لمعلومات الموظفين
- تتبع البيانات الشخصية (الاسم، معلومات الاتصال، العنوان)
- إدارة المستندات (بطاقات الهوية، الشهادات، العقود)
- دعم اللغات المتعددة (العربية/الإنجليزية)
- تتبع حالة الموظف
- تتبع ذوي الاحتياجات الخاصة

### عمليات الموارد البشرية
- إدارة العقود
- تتبع وإدارة التأمينات
- إدارة البطاقات الصحية
- إدارة وجدولة النوبات
- تتبع الأداء
- إدارة الأقسام والمناصب الوظيفية

### معالجة المستندات
- إنشاء المستندات آلياً
- عرض وإدارة ملفات PDF
- معالجة النماذج (نماذج S1، S6)
- تتبع انتهاء صلاحية المستندات
- وثائق التأمين

### إدارة المركبات
- تتبع مركبات الشركة
- إدارة معلومات السائقين
- إدارة تراخيص المركبات
- جدولة النقل
- إدارة نقاط الالتقاط

### نظام التقارير
- تقارير شاملة للموارد البشرية
- تقارير حالة الموظفين
- تقارير حالة التأمين
- منشئ استعلامات مخصص
- وظائف التصدير

### الأمان والتحكم في الوصول
- نظام تسجيل دخول آمن
- التحكم في الوصول القائم على الأدوار
- التحقق من صحة البيانات
- تتبع التدقيق

## 🔧 التفاصيل التقنية

### بُني باستخدام
- VB.NET
- قاعدة بيانات SQL Server
- نماذج Windows

### هيكل المشروع
- `Classes/` - تعريفات الفئات الأساسية
- `Controls/` - عناصر واجهة المستخدم المخصصة
- `Core/` - وظائف النظام الأساسية
- `Data/` - الوصول وإدارة البيانات
- `Forms/` - نماذج التطبيق وواجهة المستخدم
- `Helpers/` - الوظائف المساعدة
- `Models/` - نماذج البيانات
- `Resources/` - موارد التطبيق

## 📋 المتطلبات

- نظام تشغيل Windows
- .NET Framework
- SQL Server
- ذاكرة RAM 4GB كحد أدنى
- مساحة متاحة على القرص 500MB

## 🚀 التثبيت

1. استنساخ المستودع
2. استعادة قاعدة بيانات SQL Server
3. تحديث سلسلة الاتصال في App.config
4. بناء وتشغيل الحل

## 📝 الإعدادات

يمكن تكوين النظام من خلال:
- ملف App.config
- إعدادات قاعدة البيانات
- تفضيلات المستخدم

## 🔐 الأمان

- تخزين البيانات المشفرة
- المصادقة الآمنة
- الأذونات القائمة على الأدوار
- تسجيل التدقيق

## 📊 الأداء

يتضمن النظام:
- مراقبة الأداء
- إدارة الذاكرة
- استعلامات قاعدة البيانات المحسنة
- الاستخدام الفعال للموارد

## 🌐 التعريب

- واجهة عربية
- دعم اللغة الإنجليزية
- تنسيقات تاريخ قابلة للتخصيص
- دعم الإعدادات الإقليمية

## 👥 المساهمة

1. انسخ المستودع
2. أنشئ فرع الميزة الخاص بك
3. قم بإجراء التغييرات
4. ادفع إلى الفرع
5. أنشئ طلب سحب

## 📄 الترخيص

هذا المشروع برنامج مملوك. جميع الحقوق محفوظة.

## 📞 الدعم

للدعم والاستفسارات، يرجى الاتصال بمسؤول النظام.

---
© 2024 نظام الدولية انترناشونال. جميع الحقوق محفوظة.
