Imports System.ComponentModel.DataAnnotations

''' <summary>
''' User entity representing system users with enhanced security features
''' </summary>
Public Class User
    ''' <summary>
    ''' Unique user identifier
    ''' </summary>
    Public Property UserID As Integer

    ''' <summary>
    ''' Unique username for login
    ''' </summary>
    <Required>
    <StringLength(50)>
    Public Property Username As String

    ''' <summary>
    ''' User email address
    ''' </summary>
    <EmailAddress>
    <StringLength(100)>
    Public Property Email As String

    ''' <summary>
    ''' Hashed password
    ''' </summary>
    <Required>
    <StringLength(255)>
    Public Property PasswordHash As String

    ''' <summary>
    ''' Password salt for security
    ''' </summary>
    <Required>
    <StringLength(255)>
    Public Property PasswordSalt As String

    ''' <summary>
    ''' User's first name
    ''' </summary>
    <Required>
    <StringLength(50)>
    Public Property FirstName As String

    ''' <summary>
    ''' User's last name
    ''' </summary>
    <Required>
    <StringLength(50)>
    Public Property LastName As String

    ''' <summary>
    ''' Full name (computed property)
    ''' </summary>
    Public ReadOnly Property FullName As String
        Get
            Return $"{FirstName} {LastName}".Trim()
        End Get
    End Property

    ''' <summary>
    ''' Whether the user account is active
    ''' </summary>
    Public Property IsActive As Boolean = True

    ''' <summary>
    ''' Whether the user account is locked
    ''' </summary>
    Public Property IsLocked As Boolean = False

    ''' <summary>
    ''' Number of failed login attempts
    ''' </summary>
    Public Property FailedLoginAttempts As Integer = 0

    ''' <summary>
    ''' Last successful login date
    ''' </summary>
    Public Property LastLoginDate As DateTime?

    ''' <summary>
    ''' Date when password was last changed
    ''' </summary>
    Public Property LastPasswordChangeDate As DateTime = DateTime.Now

    ''' <summary>
    ''' Password expiry date
    ''' </summary>
    Public Property PasswordExpiryDate As DateTime?

    ''' <summary>
    ''' Whether user must change password on next login
    ''' </summary>
    Public Property MustChangePassword As Boolean = False

    ''' <summary>
    ''' Date when user account was created
    ''' </summary>
    Public Property CreatedDate As DateTime = DateTime.Now

    ''' <summary>
    ''' ID of user who created this account
    ''' </summary>
    Public Property CreatedBy As Integer?

    ''' <summary>
    ''' Date when user account was last modified
    ''' </summary>
    Public Property ModifiedDate As DateTime?

    ''' <summary>
    ''' ID of user who last modified this account
    ''' </summary>
    Public Property ModifiedBy As Integer?

    ''' <summary>
    ''' Associated employee ID (if user is an employee)
    ''' </summary>
    Public Property EmployeeID As Integer?

    ''' <summary>
    ''' Navigation property to associated employee
    ''' </summary>
    Public Property Employee As Employee

    ''' <summary>
    ''' Navigation property to user roles
    ''' </summary>
    Public Property UserRoles As List(Of UserRole) = New List(Of UserRole)()

    ''' <summary>
    ''' Navigation property to user permissions
    ''' </summary>
    Public Property UserPermissions As List(Of UserPermission) = New List(Of UserPermission)()

    ''' <summary>
    ''' Navigation property to user sessions
    ''' </summary>
    Public Property UserSessions As List(Of UserSession) = New List(Of UserSession)()

    ''' <summary>
    ''' Check if password is expired
    ''' </summary>
    ''' <returns>True if password is expired</returns>
    Public Function IsPasswordExpired() As Boolean
        Return PasswordExpiryDate.HasValue AndAlso PasswordExpiryDate.Value <= DateTime.Now
    End Function

    ''' <summary>
    ''' Check if account should be locked due to failed attempts
    ''' </summary>
    ''' <param name="maxFailedAttempts">Maximum allowed failed attempts</param>
    ''' <returns>True if account should be locked</returns>
    Public Function ShouldBeLocked(maxFailedAttempts As Integer) As Boolean
        Return FailedLoginAttempts >= maxFailedAttempts
    End Function

    ''' <summary>
    ''' Reset failed login attempts
    ''' </summary>
    Public Sub ResetFailedLoginAttempts()
        FailedLoginAttempts = 0
        IsLocked = False
    End Sub

    ''' <summary>
    ''' Increment failed login attempts
    ''' </summary>
    ''' <param name="maxFailedAttempts">Maximum allowed failed attempts</param>
    Public Sub IncrementFailedLoginAttempts(maxFailedAttempts As Integer)
        FailedLoginAttempts += 1
        If ShouldBeLocked(maxFailedAttempts) Then
            IsLocked = True
        End If
    End Sub

    ''' <summary>
    ''' Update last login date
    ''' </summary>
    Public Sub UpdateLastLogin()
        LastLoginDate = DateTime.Now
        ResetFailedLoginAttempts()
    End Sub

    ''' <summary>
    ''' Set password expiry based on policy
    ''' </summary>
    ''' <param name="expiryDays">Number of days until password expires</param>
    Public Sub SetPasswordExpiry(expiryDays As Integer)
        If expiryDays > 0 Then
            PasswordExpiryDate = DateTime.Now.AddDays(expiryDays)
        Else
            PasswordExpiryDate = Nothing
        End If
    End Sub

    ''' <summary>
    ''' Get all permissions for this user (from roles and direct assignments)
    ''' </summary>
    ''' <returns>List of permission names</returns>
    Public Function GetAllPermissions() As List(Of String)
        Dim permissions As New HashSet(Of String)()

        ' Add permissions from roles
        For Each userRole In UserRoles.Where(Function(ur) ur.IsActive AndAlso (Not ur.ExpiryDate.HasValue OrElse ur.ExpiryDate.Value > DateTime.Now))
            For Each rolePermission In userRole.Role.RolePermissions
                permissions.Add(rolePermission.Permission.PermissionName)
            Next
        Next

        ' Add direct user permissions (grants override denies)
        For Each userPermission In UserPermissions.Where(Function(up) Not up.ExpiryDate.HasValue OrElse up.ExpiryDate.Value > DateTime.Now)
            If userPermission.IsGranted Then
                permissions.Add(userPermission.Permission.PermissionName)
            Else
                permissions.Remove(userPermission.Permission.PermissionName)
            End If
        Next

        Return permissions.ToList()
    End Function

    ''' <summary>
    ''' Check if user has specific permission
    ''' </summary>
    ''' <param name="permissionName">Permission name to check</param>
    ''' <returns>True if user has permission</returns>
    Public Function HasPermission(permissionName As String) As Boolean
        Return GetAllPermissions().Contains(permissionName)
    End Function

    ''' <summary>
    ''' Get active roles for this user
    ''' </summary>
    ''' <returns>List of active roles</returns>
    Public Function GetActiveRoles() As List(Of Role)
        Return UserRoles.Where(Function(ur) ur.IsActive AndAlso (Not ur.ExpiryDate.HasValue OrElse ur.ExpiryDate.Value > DateTime.Now)) _
                       .Select(Function(ur) ur.Role) _
                       .ToList()
    End Function

    ''' <summary>
    ''' Check if user has specific role
    ''' </summary>
    ''' <param name="roleName">Role name to check</param>
    ''' <returns>True if user has role</returns>
    Public Function HasRole(roleName As String) As Boolean
        Return GetActiveRoles().Any(Function(r) r.RoleName.Equals(roleName, StringComparison.OrdinalIgnoreCase))
    End Function

    ''' <summary>
    ''' Validate user data
    ''' </summary>
    ''' <returns>List of validation errors</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)()

        If String.IsNullOrWhiteSpace(Username) Then
            errors.Add("Username is required")
        End If

        If String.IsNullOrWhiteSpace(FirstName) Then
            errors.Add("First name is required")
        End If

        If String.IsNullOrWhiteSpace(LastName) Then
            errors.Add("Last name is required")
        End If

        If Not String.IsNullOrWhiteSpace(Email) AndAlso Not IsValidEmail(Email) Then
            errors.Add("Invalid email format")
        End If

        Return errors
    End Function

    ''' <summary>
    ''' Validate email format
    ''' </summary>
    ''' <param name="email">Email to validate</param>
    ''' <returns>True if email is valid</returns>
    Private Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr As New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function

    ''' <summary>
    ''' String representation of user
    ''' </summary>
    ''' <returns>User string representation</returns>
    Public Overrides Function ToString() As String
        Return $"{FullName} ({Username})"
    End Function
End Class
