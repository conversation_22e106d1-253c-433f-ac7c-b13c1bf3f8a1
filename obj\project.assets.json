{"version": 3, "targets": {".NETFramework,Version=v4.8": {"ADGV/********": {"type": "package", "compile": {"lib/net40/AdvancedDataGridView.dll": {"related": ".XML"}}, "runtime": {"lib/net40/AdvancedDataGridView.dll": {"related": ".XML"}}, "resource": {"lib/net40/fr/AdvancedDataGridView.resources.dll": {"locale": "fr"}, "lib/net40/ru/AdvancedDataGridView.resources.dll": {"locale": "ru"}}}, "Azure.Core/1.38.0": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.ClientModel": "1.0.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.11.4": {"type": "package", "dependencies": {"Azure.Core": "1.38.0", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"related": ".xml"}}}, "ClosedXML/0.104.2": {"type": "package", "dependencies": {"ClosedXML.Parser": "[1.2.0, 2.0.0)", "DocumentFormat.OpenXml": "[3.1.1, 4.0.0)", "ExcelNumberFormat": "1.1.0", "Microsoft.Bcl.HashCode": "1.1.1", "RBush": "4.0.0", "SixLabors.Fonts": "1.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/ClosedXML.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/ClosedXML.dll": {"related": ".pdb;.xml"}}}, "ClosedXML.Parser/1.2.0": {"type": "package", "dependencies": {"System.Memory": "4.5.4"}, "compile": {"lib/netstandard2.0/ClosedXML.Parser.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/ClosedXML.Parser.dll": {"related": ".xml"}}}, "CrystalReports.Engine/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.ClientDoc": "13.0.4003", "CrystalReports.ReportAppServer.CommLayer": "13.0.4003", "CrystalReports.ReportAppServer.CommonObjectModel": "13.0.4003", "CrystalReports.ReportAppServer.Controllers": "13.0.4003", "CrystalReports.ReportAppServer.CubeDefModel": "13.0.4003", "CrystalReports.ReportAppServer.DataDefModel": "13.0.4003", "CrystalReports.ReportAppServer.DataSetConversion": "13.0.4003", "CrystalReports.ReportAppServer.ObjectFactory": "13.0.4003", "CrystalReports.ReportAppServer.Prompting": "13.0.4003", "CrystalReports.ReportAppServer.ReportDefModel": "13.0.4003", "CrystalReports.ReportAppServer.XmlSerialize": "13.0.4003", "CrystalReports.Shared": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.CrystalReports.Engine.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.CrystalReports.Engine.dll": {}}, "resource": {"lib/net40/cs/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "cs"}, "lib/net40/da/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "da"}, "lib/net40/de/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "de"}, "lib/net40/en/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "en"}, "lib/net40/es/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "es"}, "lib/net40/fi/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "fi"}, "lib/net40/fr/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "fr"}, "lib/net40/hu/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "hu"}, "lib/net40/it/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "it"}, "lib/net40/ja/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "ja"}, "lib/net40/ko/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "ko"}, "lib/net40/nb-NO/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "nb-NO"}, "lib/net40/nl/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "nl"}, "lib/net40/pl/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "pl"}, "lib/net40/pt/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "pt"}, "lib/net40/ru/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "ru"}, "lib/net40/sk/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "sk"}, "lib/net40/sv/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "sv"}, "lib/net40/th/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "th"}, "lib/net40/tr/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "tr"}, "lib/net40/zh-CHS/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "zh-CHS"}, "lib/net40/zh-CHT/CrystalDecisions.CrystalReports.Engine.resources.dll": {"locale": "zh-CHT"}}}, "CrystalReports.ReportAppServer.ClientDoc/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.CommLayer": "13.0.4003", "CrystalReports.ReportAppServer.CommonControls": "13.0.4003", "CrystalReports.ReportAppServer.CommonObjectModel": "13.0.4003", "CrystalReports.ReportAppServer.Controllers": "13.0.4003", "CrystalReports.ReportAppServer.CubeDefModel": "13.0.4003", "CrystalReports.ReportAppServer.DataDefModel": "13.0.4003", "CrystalReports.ReportAppServer.ReportDefModel": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.ReportAppServer.ClientDoc.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.ClientDoc.dll": {}}}, "CrystalReports.ReportAppServer.CommLayer/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.CommonObjectModel": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.ReportAppServer.CommLayer.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.CommLayer.dll": {}}}, "CrystalReports.ReportAppServer.CommonControls/13.0.4003": {"type": "package", "compile": {"lib/net40/CrystalDecisions.ReportAppServer.CommonControls.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.CommonControls.dll": {}}}, "CrystalReports.ReportAppServer.CommonObjectModel/13.0.4003": {"type": "package", "compile": {"lib/net40/CrystalDecisions.ReportAppServer.CommonObjectModel.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.CommonObjectModel.dll": {}}}, "CrystalReports.ReportAppServer.Controllers/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.CommLayer": "13.0.4003", "CrystalReports.ReportAppServer.CommonObjectModel": "13.0.4003", "CrystalReports.ReportAppServer.CubeDefModel": "13.0.4003", "CrystalReports.ReportAppServer.DataDefModel": "13.0.4003", "CrystalReports.ReportAppServer.ReportDefModel": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.ReportAppServer.Controllers.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.Controllers.dll": {}}}, "CrystalReports.ReportAppServer.CubeDefModel/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.CommonObjectModel": "13.0.4003", "CrystalReports.ReportAppServer.DataDefModel": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.ReportAppServer.CubeDefModel.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.CubeDefModel.dll": {}}}, "CrystalReports.ReportAppServer.DataDefModel/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.CommonObjectModel": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.ReportAppServer.DataDefModel.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.DataDefModel.dll": {}}}, "CrystalReports.ReportAppServer.DataSetConversion/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.ClientDoc": "13.0.4003", "CrystalReports.ReportAppServer.Controllers": "13.0.4003", "CrystalReports.ReportAppServer.DataDefModel": "13.0.4003", "CrystalReports.ReportAppServer.ObjectFactory": "13.0.4003", "CrystalReports.ReportAppServer.ReportDefModel": "13.0.4003", "CrystalReports.ReportAppServer.XmlSerialize": "13.0.4003", "CrystalReports.Shared": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.ReportAppServer.DataSetConversion.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.DataSetConversion.dll": {}}}, "CrystalReports.ReportAppServer.ObjectFactory/13.0.4003": {"type": "package", "compile": {"lib/net40/CrystalDecisions.ReportAppServer.ObjectFactory.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.ObjectFactory.dll": {}}}, "CrystalReports.ReportAppServer.Prompting/13.0.4003": {"type": "package", "compile": {"lib/net40/CrystalDecisions.ReportAppServer.Prompting.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.Prompting.dll": {}}}, "CrystalReports.ReportAppServer.ReportDefModel/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.CommonObjectModel": "13.0.4003", "CrystalReports.ReportAppServer.CubeDefModel": "13.0.4003", "CrystalReports.ReportAppServer.DataDefModel": "13.0.4003"}, "compile": {"lib/net40/CrystalDecisions.ReportAppServer.ReportDefModel.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.ReportDefModel.dll": {}}}, "CrystalReports.ReportAppServer.XmlSerialize/13.0.4003": {"type": "package", "compile": {"lib/net40/CrystalDecisions.ReportAppServer.XmlSerialize.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.ReportAppServer.XmlSerialize.dll": {}}}, "CrystalReports.Shared/13.0.4003": {"type": "package", "dependencies": {"CrystalReports.ReportAppServer.Controllers": "13.0.4003", "CrystalReports.ReportAppServer.DataDefModel": "13.0.4003", "log4net": "2.0.12"}, "compile": {"lib/net40/CrystalDecisions.Shared.dll": {}}, "runtime": {"lib/net40/CrystalDecisions.Shared.dll": {}}, "resource": {"lib/net40/cs/CrystalDecisions.Shared.resources.dll": {"locale": "cs"}, "lib/net40/da/CrystalDecisions.Shared.resources.dll": {"locale": "da"}, "lib/net40/de/CrystalDecisions.Shared.resources.dll": {"locale": "de"}, "lib/net40/en/CrystalDecisions.Shared.resources.dll": {"locale": "en"}, "lib/net40/es/CrystalDecisions.Shared.resources.dll": {"locale": "es"}, "lib/net40/fi/CrystalDecisions.Shared.resources.dll": {"locale": "fi"}, "lib/net40/fr/CrystalDecisions.Shared.resources.dll": {"locale": "fr"}, "lib/net40/hu/CrystalDecisions.Shared.resources.dll": {"locale": "hu"}, "lib/net40/it/CrystalDecisions.Shared.resources.dll": {"locale": "it"}, "lib/net40/ja/CrystalDecisions.Shared.resources.dll": {"locale": "ja"}, "lib/net40/ko/CrystalDecisions.Shared.resources.dll": {"locale": "ko"}, "lib/net40/nb-NO/CrystalDecisions.Shared.resources.dll": {"locale": "nb-NO"}, "lib/net40/nl/CrystalDecisions.Shared.resources.dll": {"locale": "nl"}, "lib/net40/pl/CrystalDecisions.Shared.resources.dll": {"locale": "pl"}, "lib/net40/pt/CrystalDecisions.Shared.resources.dll": {"locale": "pt"}, "lib/net40/ru/CrystalDecisions.Shared.resources.dll": {"locale": "ru"}, "lib/net40/sk/CrystalDecisions.Shared.resources.dll": {"locale": "sk"}, "lib/net40/sv/CrystalDecisions.Shared.resources.dll": {"locale": "sv"}, "lib/net40/th/CrystalDecisions.Shared.resources.dll": {"locale": "th"}, "lib/net40/tr/CrystalDecisions.Shared.resources.dll": {"locale": "tr"}, "lib/net40/zh-CHS/CrystalDecisions.Shared.resources.dll": {"locale": "zh-CHS"}, "lib/net40/zh-CHT/CrystalDecisions.Shared.resources.dll": {"locale": "zh-CHT"}}}, "DataGridView-AutoFilter/1.1.0": {"type": "package", "compile": {"lib/net20/DataGridViewAutoFilter.dll": {}}, "runtime": {"lib/net20/DataGridViewAutoFilter.dll": {}}}, "DocumentFormat.OpenXml/3.1.1": {"type": "package", "dependencies": {"DocumentFormat.OpenXml.Framework": "3.1.1"}, "frameworkAssemblies": ["System", "System.Xml", "WindowsBase"], "compile": {"lib/net46/DocumentFormat.OpenXml.dll": {"related": ".xml"}}, "runtime": {"lib/net46/DocumentFormat.OpenXml.dll": {"related": ".xml"}}}, "DocumentFormat.OpenXml.Framework/3.1.1": {"type": "package", "frameworkAssemblies": ["System", "System.Xml", "WindowsBase"], "compile": {"lib/net46/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net46/DocumentFormat.OpenXml.Framework.dll": {"related": ".xml"}}}, "ExcelDataReader/3.7.0": {"type": "package", "frameworkAssemblies": ["System.IO.Compression"], "compile": {"lib/net462/ExcelDataReader.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/ExcelDataReader.dll": {"related": ".pdb;.xml"}}}, "ExcelDataReader.DataSet/3.7.0": {"type": "package", "dependencies": {"ExcelDataReader": "3.7.0"}, "compile": {"lib/net462/ExcelDataReader.DataSet.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/ExcelDataReader.DataSet.dll": {"related": ".pdb;.xml"}}}, "ExcelNumberFormat/1.1.0": {"type": "package", "compile": {"lib/net20/ExcelNumberFormat.dll": {"related": ".xml"}}, "runtime": {"lib/net20/ExcelNumberFormat.dll": {"related": ".xml"}}}, "log4net/2.0.12": {"type": "package", "frameworkAssemblies": ["System.Configuration", "System.Web"], "compile": {"lib/net45/log4net.dll": {"related": ".xml"}}, "runtime": {"lib/net45/log4net.dll": {"related": ".xml"}}}, "MaterialSkin.2/2.3.1": {"type": "package", "frameworkAssemblies": ["System.Design"], "compile": {"lib/net48/MaterialSkin.dll": {}}, "runtime": {"lib/net48/MaterialSkin.dll": {}}}, "MetroModernUI/1.4.0": {"type": "package", "compile": {"lib/net/MetroFramework.Design.dll": {}, "lib/net/MetroFramework.Fonts.dll": {}, "lib/net/MetroFramework.dll": {}}, "runtime": {"lib/net/MetroFramework.Design.dll": {}, "lib/net/MetroFramework.Fonts.dll": {}, "lib/net/MetroFramework.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Bcl.Cryptography/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Formats.Asn1": "10.0.0-preview.1.25080.5", "System.Memory": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "compile": {"lib/net462/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/Microsoft.Bcl.HashCode.dll": {}}, "runtime": {"lib/net461/Microsoft.Bcl.HashCode.dll": {"related": ".xml"}}}, "Microsoft.Data.SqlClient/6.0.1": {"type": "package", "dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Bcl.Cryptography": "8.0.0", "Microsoft.Data.SqlClient.SNI": "6.0.2", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.5.0", "System.Buffers": "4.5.1", "System.Security.Cryptography.Pkcs": "8.0.1", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10"}, "frameworkAssemblies": ["System.Data", "mscorlib"], "compile": {"ref/net462/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "resource": {"lib/net462/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net462/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net462/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net462/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net462/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net462/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net462/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI/6.0.2": {"type": "package", "build": {"buildTransitive/net462/Microsoft.Data.SqlClient.SNI.targets": {}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net462/_._": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/_._": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/_._": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "8.0.1", "System.Memory": "4.5.5"}, "compile": {"lib/net462/_._": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System.ComponentModel.DataAnnotations"], "compile": {"lib/net462/_._": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/_._": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core", "System.Data", "System.Data.DataSetExtensions", "System.Drawing", "System.IdentityModel", "System.Net.Http", "System.Windows.Forms", "System.Xml", "System.Xml.Linq"], "compile": {"lib/net462/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"type": "package", "compile": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.5.0"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.5.0"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.5.0", "System.IdentityModel.Tokens.Jwt": "7.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["System.Net.Http"], "compile": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.5.0", "System.Memory": "4.5.5", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net48": "1.0.0"}}, "Microsoft.NETFramework.ReferenceAssemblies.net48/1.0.0": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.Office.Interop.Excel/15.0.4795.1001": {"type": "package", "compile": {"lib/net20/Microsoft.Office.Interop.Excel.dll": {}}, "runtime": {"lib/net20/Microsoft.Office.Interop.Excel.dll": {}}}, "Microsoft.Office.Interop.Word/15.0.4797.1004": {"type": "package", "compile": {"lib/net20/Microsoft.Office.Interop.Word.dll": {}}, "runtime": {"lib/net20/Microsoft.Office.Interop.Word.dll": {}}}, "Microsoft.SqlServer.SqlManagementObjects/172.64.0": {"type": "package", "dependencies": {"Microsoft.Data.SqlClient": "5.1.6", "NewtonSoft.Json": "13.0.1"}, "frameworkAssemblies": ["System.Data", "System.Runtime.InteropServices.RuntimeInformation"], "compile": {"lib/net472/Microsoft.Data.Tools.Sql.BatchParser.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.ConnectionInfo.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Dmf.Common.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Dmf.dll": {"related": ".Common.pdb;.Common.xml;.pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.Collector.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.CollectorEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.HadrData.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.HadrModel.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.RegisteredServers.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.Sdk.Sfc.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.SqlScriptPublish.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEvent.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEventDbScoped.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEventDbScopedEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEventEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.PolicyEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.RegSvrEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.ServiceBrokerEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Smo.Notebook.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Smo.dll": {"related": ".Notebook.pdb;.Notebook.xml;.pdb;.xml"}, "lib/net472/Microsoft.SqlServer.SmoExtended.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.SqlClrProvider.dll": {"related": ".pdb"}, "lib/net472/Microsoft.SqlServer.SqlEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.SqlWmiManagement.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.WmiEnum.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net472/Microsoft.Data.Tools.Sql.BatchParser.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.ConnectionInfo.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Dmf.Common.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Dmf.dll": {"related": ".Common.pdb;.Common.xml;.pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.Collector.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.CollectorEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.HadrData.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.HadrModel.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.RegisteredServers.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.Sdk.Sfc.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.SqlScriptPublish.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEvent.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEventDbScoped.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEventDbScopedEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Management.XEventEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.PolicyEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.RegSvrEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.ServiceBrokerEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Smo.Notebook.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.Smo.dll": {"related": ".Notebook.pdb;.Notebook.xml;.pdb;.xml"}, "lib/net472/Microsoft.SqlServer.SmoExtended.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.SqlClrProvider.dll": {"related": ".pdb"}, "lib/net472/Microsoft.SqlServer.SqlEnum.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.SqlWmiManagement.dll": {"related": ".pdb;.xml"}, "lib/net472/Microsoft.SqlServer.WmiEnum.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Toolkit.Uwp.Notifications/7.1.3": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": "1.0.0", "Microsoft.Windows.SDK.Contracts": "10.0.19041.1", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net461/Microsoft.Toolkit.Uwp.Notifications.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net461/Microsoft.Toolkit.Uwp.Notifications.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.Registry.AccessControl/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.AccessControl": "6.0.0"}, "compile": {"lib/net462/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Win32.SystemEvents/10.0.0-preview.1.25080.5": {"type": "package", "compile": {"lib/net462/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.Windows.Compatibility/10.0.0-preview.1.25080.4": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "5.0.0", "Microsoft.Win32.Registry.AccessControl": "10.0.0-preview.1.25080.5", "Microsoft.Win32.SystemEvents": "10.0.0-preview.1.25080.5", "System.CodeDom": "10.0.0-preview.1.25080.5", "System.ComponentModel.Composition": "10.0.0-preview.1.25080.5", "System.Configuration.ConfigurationManager": "10.0.0-preview.1.25080.5", "System.Data.DataSetExtensions": "4.5.0", "System.Data.Odbc": "10.0.0-preview.1.25080.5", "System.Data.OleDb": "10.0.0-preview.1.25080.5", "System.Data.SqlClient": "4.8.6", "System.Diagnostics.EventLog": "10.0.0-preview.1.25080.5", "System.Diagnostics.PerformanceCounter": "10.0.0-preview.1.25080.5", "System.DirectoryServices": "10.0.0-preview.1.25080.5", "System.DirectoryServices.AccountManagement": "10.0.0-preview.1.25080.5", "System.DirectoryServices.Protocols": "10.0.0-preview.1.25080.5", "System.Drawing.Common": "10.0.0-preview.1.25080.3", "System.IO.FileSystem.AccessControl": "5.0.0", "System.IO.Packaging": "10.0.0-preview.1.25080.5", "System.IO.Pipes.AccessControl": "5.0.0", "System.IO.Ports": "10.0.0-preview.1.25080.5", "System.Management": "10.0.0-preview.1.25080.5", "System.Reflection.Context": "10.0.0-preview.1.25080.5", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Runtime.Caching": "10.0.0-preview.1.25080.5", "System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Cryptography.Pkcs": "10.0.0-preview.1.25080.5", "System.Security.Cryptography.ProtectedData": "10.0.0-preview.1.25080.5", "System.Security.Cryptography.Xml": "10.0.0-preview.1.25080.5", "System.Security.Permissions": "10.0.0-preview.1.25080.5", "System.Security.Principal.Windows": "5.0.0", "System.ServiceModel.Duplex": "4.10.0", "System.ServiceModel.Http": "4.10.0", "System.ServiceModel.NetTcp": "4.10.0", "System.ServiceModel.Primitives": "4.10.0", "System.ServiceModel.Security": "4.10.0", "System.ServiceModel.Syndication": "10.0.0-preview.1.25080.5", "System.ServiceProcess.ServiceController": "10.0.0-preview.1.25080.5", "System.Speech": "10.0.0-preview.1.25080.5", "System.Text.Encoding.CodePages": "10.0.0-preview.1.25080.5", "System.Threading.AccessControl": "10.0.0-preview.1.25080.5", "System.Web.Services.Description": "4.10.0"}}, "Microsoft.Windows.SDK.Contracts/10.0.19041.1": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "compile": {"ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd": {}, "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd": {}, "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd": {}, "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd": {}, "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD": {}, "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd": {}, "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD": {}, "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd": {}, "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd": {}, "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd": {}, "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd": {}, "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd": {}, "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd": {}, "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd": {}, "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd": {}, "ref/netstandard2.0/Windows.WinMD": {}}, "build": {"build/_._": {}}}, "Newtonsoft.Json/13.0.1": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "RBush/4.0.0": {"type": "package", "compile": {"lib/net47/RBush.dll": {"related": ".xml"}}, "runtime": {"lib/net47/RBush.dll": {"related": ".xml"}}}, "SixLabors.Fonts/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.7.0"}, "compile": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/SixLabors.Fonts.dll": {"related": ".xml"}}}, "System.Buffers/4.6.0": {"type": "package", "compile": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.ClientModel/1.0.0": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "4.7.2"}, "compile": {"lib/netstandard2.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.CodeDom/10.0.0-preview.1.25080.5": {"type": "package", "compile": {"lib/net462/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.ComponentModel.Composition/10.0.0-preview.1.25080.5": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.Composition"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Configuration.ConfigurationManager/10.0.0-preview.1.25080.5": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Data.DataSetExtensions"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Data.Odbc/10.0.0-preview.1.25080.5": {"type": "package", "compile": {"lib/net462/System.Data.Odbc.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Data.Odbc.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.OleDb/10.0.0-preview.1.25080.5": {"type": "package", "compile": {"lib/net462/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Data.SqlClient/4.9.0": {"type": "package", "compile": {"lib/net462/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Data.SqlClient.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.1": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.EventLog/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net462/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Diagnostics.PerformanceCounter/10.0.0-preview.1.25080.5": {"type": "package", "compile": {"lib/net462/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.DirectoryServices/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "frameworkAssemblies": ["System.DirectoryServices"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.DirectoryServices.AccountManagement/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"Microsoft.Bcl.Cryptography": "10.0.0-preview.1.25080.5", "System.Security.Principal.Windows": "5.0.0"}, "frameworkAssemblies": ["System.DirectoryServices.AccountManagement"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.DirectoryServices.Protocols/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "frameworkAssemblies": ["System.DirectoryServices.Protocols"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Drawing.Common/10.0.0-preview.1.25080.3": {"type": "package", "compile": {"lib/net462/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net462/System.Drawing.Common.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Formats.Asn1/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Memory": "4.6.0", "System.ValueTuple": "4.5.0"}, "frameworkAssemblies": ["System.Numerics"], "compile": {"lib/net462/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.5.0", "Microsoft.IdentityModel.Tokens": "7.5.0"}, "compile": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net462/System.IO.dll": {}}, "runtime": {"lib/net462/System.IO.dll": {}}}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net461/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.FileSystem.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Packaging/10.0.0-preview.1.25080.5": {"type": "package", "frameworkAssemblies": ["WindowsBase"], "compile": {"lib/net462/System.IO.Packaging.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.IO.Packaging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.IO.Pipes.AccessControl/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net461/System.IO.Pipes.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IO.Pipes.AccessControl.dll": {}}, "runtimeTargets": {"runtimes/win/lib/net461/System.IO.Pipes.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Ports/10.0.0-preview.1.25080.5": {"type": "package", "compile": {"lib/net462/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Management/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.CodeDom": "10.0.0-preview.1.25080.5"}, "frameworkAssemblies": ["System.Management"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Memory/4.6.0": {"type": "package", "dependencies": {"System.Buffers": "4.6.0", "System.Numerics.Vectors": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "compile": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Memory.Data/1.0.2": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}, "compile": {"lib/net461/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.Data.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.6.0": {"type": "package", "frameworkAssemblies": ["System.Numerics"], "compile": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Reflection.Context/10.0.0-preview.1.25080.5": {"type": "package", "frameworkAssemblies": ["System.Reflection.Context"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Reflection.Emit/4.7.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Reflection.Emit.ILGeneration/4.7.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Reflection.Emit.Lightweight/4.7.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core", "mscorlib"], "compile": {"ref/net462/System.Runtime.dll": {}}, "runtime": {"lib/net462/System.Runtime.dll": {}}}, "System.Runtime.Caching/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "10.0.0-preview.1.25080.5"}, "frameworkAssemblies": ["System.Runtime.Caching"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"buildTransitive/net461/System.Runtime.WindowsRuntime.targets": {}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net461/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "dependencies": {"System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Algorithms/4.3.1": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net463/System.Security.Cryptography.Algorithms.dll": {}}, "runtime": {"lib/net463/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "dependencies": {"System.Security.Cryptography.Algorithms": "4.3.1"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net47/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/net47/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/System.Security.Cryptography.Encoding.dll": {}}, "runtime": {"lib/net46/System.Security.Cryptography.Encoding.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"Microsoft.Bcl.Cryptography": "10.0.0-preview.1.25080.5"}, "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/net46/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.1.25080.5": {"type": "package", "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Cryptography.Xml/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"Microsoft.Bcl.Cryptography": "10.0.0-preview.1.25080.5"}, "frameworkAssemblies": ["System.Security"], "compile": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Permissions/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0"}, "frameworkAssemblies": ["System.Configuration", "System.Data.OracleClient", "System.DirectoryServices", "System.Net", "System.Security", "System.ServiceProcess", "System.Transactions", "WindowsBase"], "compile": {"lib/net462/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net461/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceModel.Duplex/4.10.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "4.10.0"}, "frameworkAssemblies": ["System.IdentityModel", "System.ServiceModel"], "compile": {"ref/net461/System.ServiceModel.Duplex.dll": {}}, "runtime": {"lib/net461/System.ServiceModel.Duplex.dll": {"related": ".pdb"}}}, "System.ServiceModel.Http/4.10.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "4.10.0"}, "frameworkAssemblies": ["System.IdentityModel", "System.ServiceModel"], "compile": {"ref/net461/System.ServiceModel.Http.dll": {}}, "runtime": {"lib/net461/System.ServiceModel.Http.dll": {"related": ".pdb"}}}, "System.ServiceModel.NetTcp/4.10.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "4.10.0"}, "frameworkAssemblies": ["System.IdentityModel", "System.ServiceModel"], "compile": {"ref/net461/System.ServiceModel.NetTcp.dll": {}}, "runtime": {"lib/net461/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}}, "System.ServiceModel.Primitives/4.10.0": {"type": "package", "frameworkAssemblies": ["System.IdentityModel", "System.ServiceModel"], "compile": {"ref/net461/System.ServiceModel.Primitives.dll": {}}, "runtime": {"lib/net461/System.ServiceModel.Primitives.dll": {"related": ".pdb"}}}, "System.ServiceModel.Security/4.10.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "4.10.0"}, "frameworkAssemblies": ["System.IdentityModel", "System.ServiceModel"], "compile": {"ref/net461/System.ServiceModel.Security.dll": {}}, "runtime": {"lib/net461/System.ServiceModel.Security.dll": {"related": ".pdb"}}}, "System.ServiceModel.Syndication/10.0.0-preview.1.25080.5": {"type": "package", "frameworkAssemblies": ["System.ServiceModel"], "compile": {"lib/net462/System.ServiceModel.Syndication.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.ServiceModel.Syndication.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.ServiceProcess.ServiceController/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "10.0.0-preview.1.25080.5"}, "frameworkAssemblies": ["System.ServiceProcess"], "compile": {"lib/net462/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.ServiceProcess.ServiceController.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Speech/10.0.0-preview.1.25080.5": {"type": "package", "frameworkAssemblies": ["System.Speech"], "compile": {"lib/net462/_._": {}}, "runtime": {"lib/net462/_._": {}}}, "System.Text.Encoding.CodePages/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Memory": "4.6.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}, "compile": {"lib/net462/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Encodings.Web.dll": {"related": ".xml"}}}, "System.Text.Json/6.0.10": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Buffers": "4.5.1", "System.Memory": "4.5.4", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/System.Text.Json.targets": {}}}, "System.Threading.AccessControl/10.0.0-preview.1.25080.5": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"lib/net462/System.Threading.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Threading.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "System.Web.Services.Description/4.10.0": {"type": "package", "frameworkAssemblies": ["System.Web.Services", "System.Xml"], "compile": {"lib/net461/System.Web.Services.Description.dll": {"related": ".pdb"}}, "runtime": {"lib/net461/System.Web.Services.Description.dll": {"related": ".pdb"}}, "resource": {"lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "WindowsAPICodePack-Core/1.1.1": {"type": "package", "compile": {"lib/Microsoft.WindowsAPICodePack.dll": {}}, "runtime": {"lib/Microsoft.WindowsAPICodePack.dll": {}}}, "WindowsAPICodePack-Shell/1.1.1": {"type": "package", "dependencies": {"WindowsAPICodePack-Core": "1.1.1"}, "compile": {"lib/Microsoft.WindowsAPICodePack.Shell.dll": {}}, "runtime": {"lib/Microsoft.WindowsAPICodePack.Shell.dll": {}}}}}, "libraries": {"ADGV/********": {"sha512": "leJ7YvECR+X/Sx5TS4Y5QETpRL4V08DMuje28PxHmAt9s+oVfEfmjVKzDZLkVfHZGcXJBMFjVRYeN3dfFl+s9A==", "type": "package", "path": "adgv/********", "files": [".nupkg.metadata", ".signature.p7s", "adgv.********.nupkg.sha512", "adgv.nuspec", "lib/net40/AdvancedDataGridView.XML", "lib/net40/AdvancedDataGridView.dll", "lib/net40/fr/AdvancedDataGridView.resources.dll", "lib/net40/ru/AdvancedDataGridView.resources.dll", "readme.txt"]}, "Azure.Core/1.38.0": {"sha512": "IuEgCoVA0ef7E4pQtpC3+TkPbzaoQfa77HlfJDmfuaJUCVJmn7fT0izamZiryW5sYUFKizsftIxMkXKbgIcPMQ==", "type": "package", "path": "azure.core/1.38.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.38.0.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.11.4": {"sha512": "Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "type": "package", "path": "azure.identity/1.11.4", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.11.4.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "ClosedXML/0.104.2": {"sha512": "gOkSjQ152MhpKmw70cBkJV+FnaZAWzDwM36luRf/7FlWYnNeH++9XYdGTd0Y4KQlVPkKVxy948M5MMsnsGC4GQ==", "type": "package", "path": "closedxml/0.104.2", "files": [".nupkg.metadata", ".signature.p7s", "closedxml.0.104.2.nupkg.sha512", "closedxml.nuspec", "lib/netstandard2.0/ClosedXML.dll", "lib/netstandard2.0/ClosedXML.pdb", "lib/netstandard2.0/ClosedXML.xml", "lib/netstandard2.1/ClosedXML.dll", "lib/netstandard2.1/ClosedXML.pdb", "lib/netstandard2.1/ClosedXML.xml", "nuget-logo.png"]}, "ClosedXML.Parser/1.2.0": {"sha512": "w+/0tsxABS3lkSH8EUlA7IGme+mq5T/Puf3DbOiTckmSuUpAUO2LK29oXYByCcWkBv6wcRHxgWlQb1lxkwI0Tw==", "type": "package", "path": "closedxml.parser/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "closedxml.parser.1.2.0.nupkg.sha512", "closedxml.parser.nuspec", "lib/netstandard2.0/ClosedXML.Parser.dll", "lib/netstandard2.0/ClosedXML.Parser.xml", "lib/netstandard2.1/ClosedXML.Parser.dll", "lib/netstandard2.1/ClosedXML.Parser.xml"]}, "CrystalReports.Engine/13.0.4003": {"sha512": "B3LM5SQdqaOHa5p0b9pO0+CNhXZOUA24R+wFJKyTwFNVgh1a9luTqdV8Jz8ygrRvB9DX/9UpYbMQwptLxpcYdA==", "type": "package", "path": "crystalreports.engine/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.engine.13.0.4003.nupkg.sha512", "crystalreports.engine.nuspec", "lib/net40/CrystalDecisions.CrystalReports.Engine.dll", "lib/net40/cs/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/da/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/de/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/en/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/es/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/fi/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/fr/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/hu/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/it/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/ja/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/ko/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/nb-NO/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/nl/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/pl/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/pt/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/ru/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/sk/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/sv/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/th/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/tr/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/zh-CHS/CrystalDecisions.CrystalReports.Engine.resources.dll", "lib/net40/zh-CHT/CrystalDecisions.CrystalReports.Engine.resources.dll"]}, "CrystalReports.ReportAppServer.ClientDoc/13.0.4003": {"sha512": "NWqCFFTLTuPp2LqcN1HZqBQ7Vvu3dgqoKWESSt+Csbg6FfM1mhtupKWidnGxP02DccoqgOUa44H2fuih9veRbw==", "type": "package", "path": "crystalreports.reportappserver.clientdoc/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.clientdoc.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.clientdoc.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.ClientDoc.dll"]}, "CrystalReports.ReportAppServer.CommLayer/13.0.4003": {"sha512": "hBoIi0tS4i8UxM8q+QAP11QdhhR2fRURJb7nvAtPEWT4fSO9+1f9vl8EDkGUp3aletMetR5r+WjAQZRrHcLoyg==", "type": "package", "path": "crystalreports.reportappserver.commlayer/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.commlayer.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.commlayer.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.CommLayer.dll"]}, "CrystalReports.ReportAppServer.CommonControls/13.0.4003": {"sha512": "xqeaj6JuxI8TD0WwrP+VcvT/mghBvnEWZii2zlwgqd48E+eebr01MJnI0gRZrhg2LgQpcjBo90Xwc5iI9oH+tg==", "type": "package", "path": "crystalreports.reportappserver.commoncontrols/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.commoncontrols.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.commoncontrols.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.CommonControls.dll"]}, "CrystalReports.ReportAppServer.CommonObjectModel/13.0.4003": {"sha512": "dLSfzSNg+OxDTcqUFahhCLvMBxF7bKDxnh1EBKXoLl65SSHP8QBNAeI4CpKXtNimUOUJ/sW1McuGYX4CAgUmAw==", "type": "package", "path": "crystalreports.reportappserver.commonobjectmodel/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.commonobjectmodel.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.commonobjectmodel.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.CommonObjectModel.dll"]}, "CrystalReports.ReportAppServer.Controllers/13.0.4003": {"sha512": "60Tg65l9NCFX4u8MFX2QWAL7GmQhtmUXJaNhv9F/vXEmlFXHC76vbkwgBumKQcIPpoOhlZK/dqz6YcNP0sHkgA==", "type": "package", "path": "crystalreports.reportappserver.controllers/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.controllers.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.controllers.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.Controllers.dll"]}, "CrystalReports.ReportAppServer.CubeDefModel/13.0.4003": {"sha512": "ZBns1JhhuBrHudpZowKNRlhXY/GW5BWMoqyafM3eHxoZL1K4mSn/UYPEzNFseYio4abUs5Iw4+K8adCOtfinqw==", "type": "package", "path": "crystalreports.reportappserver.cubedefmodel/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.cubedefmodel.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.cubedefmodel.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.CubeDefModel.dll"]}, "CrystalReports.ReportAppServer.DataDefModel/13.0.4003": {"sha512": "8sSuETn80yMzW0Edjn6Y3Dyif85QKCf4771Q44Jk/soxTo8FcSG8VEm91M9d+CrFivFqlsH7ENPjjKhQ1kInsg==", "type": "package", "path": "crystalreports.reportappserver.datadefmodel/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.datadefmodel.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.datadefmodel.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.DataDefModel.dll"]}, "CrystalReports.ReportAppServer.DataSetConversion/13.0.4003": {"sha512": "daPXUF5uAxD94HTKemUs41exkPSwSPq9cqo5QdqaKslZR5AOZcXfgYmIXwyj9yC/h2LP4g/pC6JTS97c4GOc3w==", "type": "package", "path": "crystalreports.reportappserver.datasetconversion/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.datasetconversion.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.datasetconversion.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.DataSetConversion.dll"]}, "CrystalReports.ReportAppServer.ObjectFactory/13.0.4003": {"sha512": "D3/LsgwkMtVW7Utv7Kl0PM2IVhBPtf+yGgxo0KD++NY8RTsxot21ONXkAcZGOH9pB7Ly9YwAedC7wP/OxD1KJg==", "type": "package", "path": "crystalreports.reportappserver.objectfactory/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.objectfactory.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.objectfactory.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.ObjectFactory.dll"]}, "CrystalReports.ReportAppServer.Prompting/13.0.4003": {"sha512": "yjIja0uvk9VGxpQqm0Z2pC/KH7Sjp2IAwJpJMrdz2xNUyi2tOq8MO2NsQmMAZRTWgKm5Sep8J5kTvPYkSBfyqg==", "type": "package", "path": "crystalreports.reportappserver.prompting/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.prompting.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.prompting.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.Prompting.dll"]}, "CrystalReports.ReportAppServer.ReportDefModel/13.0.4003": {"sha512": "lEXNeJ9x2GVPL/+PmhTJrsY84uGwgj+Uum1CLBnphdpkORBwauETrU1dDSVaQbgYGahfDZgm0PtZEWVbU2MfjQ==", "type": "package", "path": "crystalreports.reportappserver.reportdefmodel/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.reportdefmodel.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.reportdefmodel.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.ReportDefModel.dll"]}, "CrystalReports.ReportAppServer.XmlSerialize/13.0.4003": {"sha512": "O00VRtSZDN6rr+RrQ7JWNEDix/wAhf7hja947J2ws3M23rfO69hAD39qp/2nOJig3e5wBJ31wNjYWi80qSsm1g==", "type": "package", "path": "crystalreports.reportappserver.xmlserialize/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.reportappserver.xmlserialize.13.0.4003.nupkg.sha512", "crystalreports.reportappserver.xmlserialize.nuspec", "lib/net40/CrystalDecisions.ReportAppServer.XmlSerialize.dll"]}, "CrystalReports.Shared/13.0.4003": {"sha512": "s+7C/lUpICrZNjDxSiPZ4v6XZD30jnF1Uqx4hbTclZ8qxUBG4k5qungapL3Uijy5y8uC1P/ZkTT808fqU89fJw==", "type": "package", "path": "crystalreports.shared/13.0.4003", "files": [".editoricon.png", ".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "LICENSE", "README.md", "crystalreports.shared.13.0.4003.nupkg.sha512", "crystalreports.shared.nuspec", "lib/net40/CrystalDecisions.Shared.dll", "lib/net40/cs/CrystalDecisions.Shared.resources.dll", "lib/net40/da/CrystalDecisions.Shared.resources.dll", "lib/net40/de/CrystalDecisions.Shared.resources.dll", "lib/net40/en/CrystalDecisions.Shared.resources.dll", "lib/net40/es/CrystalDecisions.Shared.resources.dll", "lib/net40/fi/CrystalDecisions.Shared.resources.dll", "lib/net40/fr/CrystalDecisions.Shared.resources.dll", "lib/net40/hu/CrystalDecisions.Shared.resources.dll", "lib/net40/it/CrystalDecisions.Shared.resources.dll", "lib/net40/ja/CrystalDecisions.Shared.resources.dll", "lib/net40/ko/CrystalDecisions.Shared.resources.dll", "lib/net40/nb-NO/CrystalDecisions.Shared.resources.dll", "lib/net40/nl/CrystalDecisions.Shared.resources.dll", "lib/net40/pl/CrystalDecisions.Shared.resources.dll", "lib/net40/pt/CrystalDecisions.Shared.resources.dll", "lib/net40/ru/CrystalDecisions.Shared.resources.dll", "lib/net40/sk/CrystalDecisions.Shared.resources.dll", "lib/net40/sv/CrystalDecisions.Shared.resources.dll", "lib/net40/th/CrystalDecisions.Shared.resources.dll", "lib/net40/tr/CrystalDecisions.Shared.resources.dll", "lib/net40/zh-CHS/CrystalDecisions.Shared.resources.dll", "lib/net40/zh-CHT/CrystalDecisions.Shared.resources.dll"]}, "DataGridView-AutoFilter/1.1.0": {"sha512": "QPaTjR+Krge2K2Vv5jxMBNAKR0I1Z1xX+ML2C8PyVwnckyMwPFoZnjg7Lq6oHh4iWpAIHlPFapX59EVHW0wDsg==", "type": "package", "path": "datagridview-autofilter/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "datagridview-autofilter.1.1.0.nupkg.sha512", "datagridview-autofilter.nuspec", "icon/icon.png", "lib/net20/DataGridViewAutoFilter.dll"]}, "DocumentFormat.OpenXml/3.1.1": {"sha512": "2z9QBzeTLNNKWM9SaOSDMegfQk/7hDuElOsmF77pKZMkFRP/GHA/W/4yOAQD9kn15N/FsFxHn3QVYkatuZghiA==", "type": "package", "path": "documentformat.openxml/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.3.1.1.nupkg.sha512", "documentformat.openxml.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.dll", "lib/net35/DocumentFormat.OpenXml.xml", "lib/net40/DocumentFormat.OpenXml.dll", "lib/net40/DocumentFormat.OpenXml.xml", "lib/net46/DocumentFormat.OpenXml.dll", "lib/net46/DocumentFormat.OpenXml.xml", "lib/net8.0/DocumentFormat.OpenXml.dll", "lib/net8.0/DocumentFormat.OpenXml.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.xml"]}, "DocumentFormat.OpenXml.Framework/3.1.1": {"sha512": "6APEp/ElZV58S/4v8mf4Ke3ONEDORs64MqdD64Z7wWpcHANB9oovQsGIwtqjnKihulOj7T0a6IxHIHOfMqKOng==", "type": "package", "path": "documentformat.openxml.framework/3.1.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "documentformat.openxml.framework.3.1.1.nupkg.sha512", "documentformat.openxml.framework.nuspec", "icon.png", "lib/net35/DocumentFormat.OpenXml.Framework.dll", "lib/net35/DocumentFormat.OpenXml.Framework.xml", "lib/net40/DocumentFormat.OpenXml.Framework.dll", "lib/net40/DocumentFormat.OpenXml.Framework.xml", "lib/net46/DocumentFormat.OpenXml.Framework.dll", "lib/net46/DocumentFormat.OpenXml.Framework.xml", "lib/net6.0/DocumentFormat.OpenXml.Framework.dll", "lib/net6.0/DocumentFormat.OpenXml.Framework.xml", "lib/net8.0/DocumentFormat.OpenXml.Framework.dll", "lib/net8.0/DocumentFormat.OpenXml.Framework.xml", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.dll", "lib/netstandard2.0/DocumentFormat.OpenXml.Framework.xml"]}, "ExcelDataReader/3.7.0": {"sha512": "AMv3oDETRHSRyXC17rBtKH45qIfFyo433LMeaMB3u4RNr/c9Luuc0Z+JMP6+3Cx9n4wXqFqcrEIVxrf/GgYnZg==", "type": "package", "path": "exceldatareader/3.7.0", "files": [".nupkg.metadata", ".signature.p7s", "ExcelDataReader.png", "README.md", "exceldatareader.3.7.0.nupkg.sha512", "exceldatareader.nuspec", "lib/net462/ExcelDataReader.dll", "lib/net462/ExcelDataReader.pdb", "lib/net462/ExcelDataReader.xml", "lib/netstandard2.0/ExcelDataReader.dll", "lib/netstandard2.0/ExcelDataReader.pdb", "lib/netstandard2.0/ExcelDataReader.xml", "lib/netstandard2.1/ExcelDataReader.dll", "lib/netstandard2.1/ExcelDataReader.pdb", "lib/netstandard2.1/ExcelDataReader.xml"]}, "ExcelDataReader.DataSet/3.7.0": {"sha512": "zA2/CVzbMspkNg0qf0/Zp+eU6VxYP5PtiJSErLDP46d/Y7F6of/NCcSGeXjs97KDq7UiEf6XJe+89s/92n2GYg==", "type": "package", "path": "exceldatareader.dataset/3.7.0", "files": [".nupkg.metadata", ".signature.p7s", "ExcelDataReader.png", "README.md", "exceldatareader.dataset.3.7.0.nupkg.sha512", "exceldatareader.dataset.nuspec", "lib/net462/ExcelDataReader.DataSet.dll", "lib/net462/ExcelDataReader.DataSet.pdb", "lib/net462/ExcelDataReader.DataSet.xml", "lib/netstandard2.0/ExcelDataReader.DataSet.dll", "lib/netstandard2.0/ExcelDataReader.DataSet.pdb", "lib/netstandard2.0/ExcelDataReader.DataSet.xml", "lib/netstandard2.1/ExcelDataReader.DataSet.dll", "lib/netstandard2.1/ExcelDataReader.DataSet.pdb", "lib/netstandard2.1/ExcelDataReader.DataSet.xml"]}, "ExcelNumberFormat/1.1.0": {"sha512": "R3BVHPs9O+RkExbZYTGT0+9HLbi8ZrNij1Yziyw6znd3J7P3uoIR07uwTLGOogtz1p6+0sna66eBoXu7tBiVQA==", "type": "package", "path": "excelnumberformat/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "excelnumberformat.1.1.0.nupkg.sha512", "excelnumberformat.nuspec", "icon.png", "lib/net20/ExcelNumberFormat.dll", "lib/net20/ExcelNumberFormat.xml", "lib/netstandard1.0/ExcelNumberFormat.dll", "lib/netstandard1.0/ExcelNumberFormat.xml", "lib/netstandard2.0/ExcelNumberFormat.dll", "lib/netstandard2.0/ExcelNumberFormat.xml"]}, "log4net/2.0.12": {"sha512": "9P67BCftJ7KG+B7rNOM1A9KczUwyEDed6zbAddy5Cj/73xVkzi+rEAHeOgUnW5wDqy1JFlY8+oTP0m1PgJ03Tg==", "type": "package", "path": "log4net/2.0.12", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/log4net.dll", "lib/net20/log4net.xml", "lib/net35-client/log4net.dll", "lib/net35-client/log4net.xml", "lib/net35/log4net.dll", "lib/net35/log4net.xml", "lib/net40-client/log4net.dll", "lib/net40-client/log4net.xml", "lib/net40/log4net.dll", "lib/net40/log4net.xml", "lib/net45/log4net.dll", "lib/net45/log4net.xml", "lib/netstandard1.3/log4net.dll", "lib/netstandard1.3/log4net.xml", "lib/netstandard2.0/log4net.dll", "lib/netstandard2.0/log4net.xml", "log4net.2.0.12.nupkg.sha512", "log4net.nuspec", "package-icon.png"]}, "MaterialSkin.2/2.3.1": {"sha512": "w2vFxLNk4InCDU7pXDfBqPfv55HhBtuu8HRfEqQpeS3sNTS1pUzDs/hS0b5JW2/tKvt+P1of9OINP8a9omWDBg==", "type": "package", "path": "materialskin.2/2.3.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net45/MaterialSkin.dll", "lib/net461/MaterialSkin.dll", "lib/net48/MaterialSkin.dll", "lib/net5.0-windows7.0/MaterialSkin.dll", "materialskin.2.2.3.1.nupkg.sha512", "materialskin.2.nuspec", "nugetIcon.png"]}, "MetroModernUI/1.4.0": {"sha512": "p2F3w8bF1tb0tpkDceEVO65UYMKnJxdQyNxVyl/OjrFZHighicLYZDppKkiqmvBZ10XUi2mqHiIJUzXBP3GElQ==", "type": "package", "path": "metromodernui/1.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net/MetroFramework.Design.dll", "lib/net/MetroFramework.Fonts.dll", "lib/net/MetroFramework.dll", "metromodernui.1.4.0.nupkg.sha512", "metromodernui.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.Cryptography/10.0.0-preview.1.25080.5": {"sha512": "ETZZaKPDJ4Fk2kLwY9kq8d6F1BzXl8T87fN87udwdgw2Lb3xGCtWHo9556IJkQoDmyG85xIZwuh0SflAdX391g==", "type": "package", "path": "microsoft.bcl.cryptography/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.Cryptography.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.Cryptography.targets", "lib/net10.0/Microsoft.Bcl.Cryptography.dll", "lib/net10.0/Microsoft.Bcl.Cryptography.xml", "lib/net462/Microsoft.Bcl.Cryptography.dll", "lib/net462/Microsoft.Bcl.Cryptography.xml", "lib/net8.0/Microsoft.Bcl.Cryptography.dll", "lib/net8.0/Microsoft.Bcl.Cryptography.xml", "lib/net9.0/Microsoft.Bcl.Cryptography.dll", "lib/net9.0/Microsoft.Bcl.Cryptography.xml", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.dll", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.xml", "microsoft.bcl.cryptography.10.0.0-preview.1.25080.5.nupkg.sha512", "microsoft.bcl.cryptography.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.HashCode/1.1.1": {"sha512": "MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "type": "package", "path": "microsoft.bcl.hashcode/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.HashCode.dll", "lib/net461/Microsoft.Bcl.HashCode.xml", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "lib/netcoreapp2.1/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.0/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.0/Microsoft.Bcl.HashCode.xml", "lib/netstandard2.1/Microsoft.Bcl.HashCode.dll", "lib/netstandard2.1/Microsoft.Bcl.HashCode.xml", "microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "microsoft.bcl.hashcode.nuspec", "ref/net461/Microsoft.Bcl.HashCode.dll", "ref/netcoreapp2.1/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.0/Microsoft.Bcl.HashCode.dll", "ref/netstandard2.1/Microsoft.Bcl.HashCode.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Data.SqlClient/6.0.1": {"sha512": "v7HxnYYXGGCJilxeQ4Pdks+popVuGajBpHmau0RU4ACIcbfs5qCNUnCogGpZ+CJ//8Qafhxq7vc5a8L9d6O8Eg==", "type": "package", "path": "microsoft.data.sqlclient/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/Microsoft.Data.SqlClient.dll", "lib/net8.0/Microsoft.Data.SqlClient.xml", "lib/net8.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/Microsoft.Data.SqlClient.dll", "lib/net9.0/Microsoft.Data.SqlClient.xml", "lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "microsoft.data.sqlclient.6.0.1.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net8.0/Microsoft.Data.SqlClient.dll", "ref/net8.0/Microsoft.Data.SqlClient.xml", "ref/net9.0/Microsoft.Data.SqlClient.dll", "ref/net9.0/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll"]}, "Microsoft.Data.SqlClient.SNI/6.0.2": {"sha512": "p3Pm/+7oPSn4At6vKrttRpUOVdrcer3oZln0XeYZ94DTTQirUVzQy5QmHjdMmbyIaTaYb6BYf+8N7ob5t1ctQA==", "type": "package", "path": "microsoft.data.sqlclient.sni/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "build/net462/Microsoft.Data.SqlClient.SNI.arm64.dll", "build/net462/Microsoft.Data.SqlClient.SNI.arm64.pdb", "build/net462/Microsoft.Data.SqlClient.SNI.targets", "build/net462/Microsoft.Data.SqlClient.SNI.x64.dll", "build/net462/Microsoft.Data.SqlClient.SNI.x64.pdb", "build/net462/Microsoft.Data.SqlClient.SNI.x86.dll", "build/net462/Microsoft.Data.SqlClient.SNI.x86.pdb", "buildTransitive/net462/Microsoft.Data.SqlClient.SNI.arm64.dll", "buildTransitive/net462/Microsoft.Data.SqlClient.SNI.arm64.pdb", "buildTransitive/net462/Microsoft.Data.SqlClient.SNI.targets", "buildTransitive/net462/Microsoft.Data.SqlClient.SNI.x64.dll", "buildTransitive/net462/Microsoft.Data.SqlClient.SNI.x64.pdb", "buildTransitive/net462/Microsoft.Data.SqlClient.SNI.x86.dll", "buildTransitive/net462/Microsoft.Data.SqlClient.SNI.x86.pdb", "dotnet.png", "microsoft.data.sqlclient.sni.6.0.2.nupkg.sha512", "microsoft.data.sqlclient.sni.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.1": {"sha512": "HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"sha512": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"sha512": "nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.2": {"sha512": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "type": "package", "path": "microsoft.extensions.options/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.61.3": {"sha512": "naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "type": "package", "path": "microsoft.identity.client/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net6.0-android31.0/Microsoft.Identity.Client.dll", "lib/net6.0-android31.0/Microsoft.Identity.Client.xml", "lib/net6.0-ios15.4/Microsoft.Identity.Client.dll", "lib/net6.0-ios15.4/Microsoft.Identity.Client.xml", "lib/net6.0/Microsoft.Identity.Client.dll", "lib/net6.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.61.3.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"sha512": "PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.61.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/7.5.0": {"sha512": "seOFPaBQh2K683eFujAuDsrO2XbOA+SvxRli+wu7kl+ZymuGQzjmmUKfyFHmDazpPOBnmOX1ZnjX7zFDZHyNIA==", "type": "package", "path": "microsoft.identitymodel.abstractions/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.7.5.0.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/7.5.0": {"sha512": "mfyiGptbcH+oYrzAtWWwuV+7MoM0G0si+9owaj6DGWInhq/N/KDj/pWHhq1ShdmBu332gjP+cppjgwBpsOj7Fg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.7.5.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/7.5.0": {"sha512": "3BInZEajJvnTDP/YRrmJ3Fyw8XAWWR9jG+3FkhhzRJJYItVL+BEH9qlgxSmtrxp7S7N6TOv+Y+X8BG61viiehQ==", "type": "package", "path": "microsoft.identitymodel.logging/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.7.5.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/7.5.0": {"sha512": "ugyb0Nm+I+UrHGYg28mL8oCV31xZrOEbs8fQkcShUoKvgk22HroD2odCnqEf56CoAFYTwoDExz8deXzrFC+TyA==", "type": "package", "path": "microsoft.identitymodel.protocols/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.7.5.0.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.5.0": {"sha512": "/U3I/8uutTqZr2n/zt0q08bluYklq+5VWP7ZuOGpTUR1ln5bSbrexAzdSGzrhxTxNNbHMCU8Mn2bNQvcmehAxg==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/7.5.0": {"sha512": "owe33wqe0ZbwBxM3D90I0XotxNyTdl85jud03d+OrUOJNnTiqnYePwBk3WU9yW0Rk5CYX+sfSim7frmu6jeEzQ==", "type": "package", "path": "microsoft.identitymodel.tokens/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.7.5.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.0": {"sha512": "7D2TMufjGiowmt0E941kVoTIS+GTNzaPopuzM1/1LSaJAdJdBrVP0SkZW7AgDd0a2U1DjsIeaKG1wxGVBNLDMw==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.0.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net48/1.0.0": {"sha512": "k2VyV/lct0PT9dS3Il90tBpoM2V/4dJuP5iXe1ckLbBp5FZP1XUsNbRtkuSuzcuqHm1j0OVRPdV980S4bJRe0A==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net48/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.8/Accessibility.dll", "build/.NETFramework/v4.8/Accessibility.xml", "build/.NETFramework/v4.8/CustomMarshalers.dll", "build/.NETFramework/v4.8/CustomMarshalers.xml", "build/.NETFramework/v4.8/Facades/Microsoft.Win32.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.AppContext.dll", "build/.NETFramework/v4.8/Facades/System.Collections.Concurrent.dll", "build/.NETFramework/v4.8/Facades/System.Collections.NonGeneric.dll", "build/.NETFramework/v4.8/Facades/System.Collections.Specialized.dll", "build/.NETFramework/v4.8/Facades/System.Collections.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.Annotations.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.EventBasedAsync.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.TypeConverter.dll", "build/.NETFramework/v4.8/Facades/System.ComponentModel.dll", "build/.NETFramework/v4.8/Facades/System.Console.dll", "build/.NETFramework/v4.8/Facades/System.Data.Common.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Contracts.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Debug.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.FileVersionInfo.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Process.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.StackTrace.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.TextWriterTraceListener.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.Tools.dll", "build/.NETFramework/v4.8/Facades/System.Diagnostics.TraceSource.dll", "build/.NETFramework/v4.8/Facades/System.Drawing.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Dynamic.Runtime.dll", "build/.NETFramework/v4.8/Facades/System.Globalization.Calendars.dll", "build/.NETFramework/v4.8/Facades/System.Globalization.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Globalization.dll", "build/.NETFramework/v4.8/Facades/System.IO.Compression.ZipFile.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.DriveInfo.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.Watcher.dll", "build/.NETFramework/v4.8/Facades/System.IO.FileSystem.dll", "build/.NETFramework/v4.8/Facades/System.IO.IsolatedStorage.dll", "build/.NETFramework/v4.8/Facades/System.IO.MemoryMappedFiles.dll", "build/.NETFramework/v4.8/Facades/System.IO.Pipes.dll", "build/.NETFramework/v4.8/Facades/System.IO.UnmanagedMemoryStream.dll", "build/.NETFramework/v4.8/Facades/System.IO.dll", "build/.NETFramework/v4.8/Facades/System.Linq.Expressions.dll", "build/.NETFramework/v4.8/Facades/System.Linq.Parallel.dll", "build/.NETFramework/v4.8/Facades/System.Linq.Queryable.dll", "build/.NETFramework/v4.8/Facades/System.Linq.dll", "build/.NETFramework/v4.8/Facades/System.Net.Http.Rtc.dll", "build/.NETFramework/v4.8/Facades/System.Net.NameResolution.dll", "build/.NETFramework/v4.8/Facades/System.Net.NetworkInformation.dll", "build/.NETFramework/v4.8/Facades/System.Net.Ping.dll", "build/.NETFramework/v4.8/Facades/System.Net.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Net.Requests.dll", "build/.NETFramework/v4.8/Facades/System.Net.Security.dll", "build/.NETFramework/v4.8/Facades/System.Net.Sockets.dll", "build/.NETFramework/v4.8/Facades/System.Net.WebHeaderCollection.dll", "build/.NETFramework/v4.8/Facades/System.Net.WebSockets.Client.dll", "build/.NETFramework/v4.8/Facades/System.Net.WebSockets.dll", "build/.NETFramework/v4.8/Facades/System.ObjectModel.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Emit.ILGeneration.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Emit.Lightweight.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Emit.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Reflection.dll", "build/.NETFramework/v4.8/Facades/System.Resources.Reader.dll", "build/.NETFramework/v4.8/Facades/System.Resources.ResourceManager.dll", "build/.NETFramework/v4.8/Facades/System.Resources.Writer.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.CompilerServices.VisualC.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Handles.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.InteropServices.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Numerics.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Formatters.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Json.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.Serialization.Xml.dll", "build/.NETFramework/v4.8/Facades/System.Runtime.dll", "build/.NETFramework/v4.8/Facades/System.Security.Claims.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Algorithms.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Csp.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Encoding.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.Security.Cryptography.X509Certificates.dll", "build/.NETFramework/v4.8/Facades/System.Security.Principal.dll", "build/.NETFramework/v4.8/Facades/System.Security.SecureString.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Duplex.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Http.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.NetTcp.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Primitives.dll", "build/.NETFramework/v4.8/Facades/System.ServiceModel.Security.dll", "build/.NETFramework/v4.8/Facades/System.Text.Encoding.Extensions.dll", "build/.NETFramework/v4.8/Facades/System.Text.Encoding.dll", "build/.NETFramework/v4.8/Facades/System.Text.RegularExpressions.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Overlapped.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Tasks.Parallel.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Tasks.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Thread.dll", "build/.NETFramework/v4.8/Facades/System.Threading.ThreadPool.dll", "build/.NETFramework/v4.8/Facades/System.Threading.Timer.dll", "build/.NETFramework/v4.8/Facades/System.Threading.dll", "build/.NETFramework/v4.8/Facades/System.ValueTuple.dll", "build/.NETFramework/v4.8/Facades/System.Xml.ReaderWriter.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XDocument.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XPath.XDocument.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XPath.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XmlDocument.dll", "build/.NETFramework/v4.8/Facades/System.Xml.XmlSerializer.dll", "build/.NETFramework/v4.8/Facades/netstandard.dll", "build/.NETFramework/v4.8/ISymWrapper.dll", "build/.NETFramework/v4.8/ISymWrapper.xml", "build/.NETFramework/v4.8/Microsoft.Activities.Build.dll", "build/.NETFramework/v4.8/Microsoft.Activities.Build.xml", "build/.NETFramework/v4.8/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.8/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.8/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.8/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.8/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.8/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.8/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.8/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.8/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.8/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.8/Microsoft.Build.dll", "build/.NETFramework/v4.8/Microsoft.Build.xml", "build/.NETFramework/v4.8/Microsoft.CSharp.dll", "build/.NETFramework/v4.8/Microsoft.CSharp.xml", "build/.NETFramework/v4.8/Microsoft.JScript.dll", "build/.NETFramework/v4.8/Microsoft.JScript.xml", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.8/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.8/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.8/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.8/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.8/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.8/Microsoft.VisualC.dll", "build/.NETFramework/v4.8/Microsoft.VisualC.xml", "build/.NETFramework/v4.8/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.8/PermissionSets/Internet.xml", "build/.NETFramework/v4.8/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.8/PresentationBuildTasks.dll", "build/.NETFramework/v4.8/PresentationBuildTasks.xml", "build/.NETFramework/v4.8/PresentationCore.dll", "build/.NETFramework/v4.8/PresentationCore.xml", "build/.NETFramework/v4.8/PresentationFramework.Aero.dll", "build/.NETFramework/v4.8/PresentationFramework.Aero.xml", "build/.NETFramework/v4.8/PresentationFramework.Aero2.dll", "build/.NETFramework/v4.8/PresentationFramework.Aero2.xml", "build/.NETFramework/v4.8/PresentationFramework.AeroLite.dll", "build/.NETFramework/v4.8/PresentationFramework.AeroLite.xml", "build/.NETFramework/v4.8/PresentationFramework.Classic.dll", "build/.NETFramework/v4.8/PresentationFramework.Classic.xml", "build/.NETFramework/v4.8/PresentationFramework.Luna.dll", "build/.NETFramework/v4.8/PresentationFramework.Luna.xml", "build/.NETFramework/v4.8/PresentationFramework.Royale.dll", "build/.NETFramework/v4.8/PresentationFramework.Royale.xml", "build/.NETFramework/v4.8/PresentationFramework.dll", "build/.NETFramework/v4.8/PresentationFramework.xml", "build/.NETFramework/v4.8/ReachFramework.dll", "build/.NETFramework/v4.8/ReachFramework.xml", "build/.NETFramework/v4.8/RedistList/FrameworkList.xml", "build/.NETFramework/v4.8/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.8/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.8/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.8/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.8/System.Activities.Presentation.dll", "build/.NETFramework/v4.8/System.Activities.Presentation.xml", "build/.NETFramework/v4.8/System.Activities.dll", "build/.NETFramework/v4.8/System.Activities.xml", "build/.NETFramework/v4.8/System.AddIn.Contract.dll", "build/.NETFramework/v4.8/System.AddIn.Contract.xml", "build/.NETFramework/v4.8/System.AddIn.dll", "build/.NETFramework/v4.8/System.AddIn.xml", "build/.NETFramework/v4.8/System.ComponentModel.Composition.Registration.dll", "build/.NETFramework/v4.8/System.ComponentModel.Composition.Registration.xml", "build/.NETFramework/v4.8/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.8/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.8/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.8/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.8/System.Configuration.Install.dll", "build/.NETFramework/v4.8/System.Configuration.Install.xml", "build/.NETFramework/v4.8/System.Configuration.dll", "build/.NETFramework/v4.8/System.Configuration.xml", "build/.NETFramework/v4.8/System.Core.dll", "build/.NETFramework/v4.8/System.Core.xml", "build/.NETFramework/v4.8/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.8/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.8/System.Data.Entity.Design.dll", "build/.NETFramework/v4.8/System.Data.Entity.Design.xml", "build/.NETFramework/v4.8/System.Data.Entity.dll", "build/.NETFramework/v4.8/System.Data.Entity.xml", "build/.NETFramework/v4.8/System.Data.Linq.dll", "build/.NETFramework/v4.8/System.Data.Linq.xml", "build/.NETFramework/v4.8/System.Data.OracleClient.dll", "build/.NETFramework/v4.8/System.Data.OracleClient.xml", "build/.NETFramework/v4.8/System.Data.Services.Client.dll", "build/.NETFramework/v4.8/System.Data.Services.Client.xml", "build/.NETFramework/v4.8/System.Data.Services.Design.dll", "build/.NETFramework/v4.8/System.Data.Services.Design.xml", "build/.NETFramework/v4.8/System.Data.Services.dll", "build/.NETFramework/v4.8/System.Data.Services.xml", "build/.NETFramework/v4.8/System.Data.SqlXml.dll", "build/.NETFramework/v4.8/System.Data.SqlXml.xml", "build/.NETFramework/v4.8/System.Data.dll", "build/.NETFramework/v4.8/System.Data.xml", "build/.NETFramework/v4.8/System.Deployment.dll", "build/.NETFramework/v4.8/System.Deployment.xml", "build/.NETFramework/v4.8/System.Design.dll", "build/.NETFramework/v4.8/System.Design.xml", "build/.NETFramework/v4.8/System.Device.dll", "build/.NETFramework/v4.8/System.Device.xml", "build/.NETFramework/v4.8/System.Diagnostics.Tracing.dll", "build/.NETFramework/v4.8/System.Diagnostics.Tracing.xml", "build/.NETFramework/v4.8/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.8/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.8/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.8/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.8/System.DirectoryServices.dll", "build/.NETFramework/v4.8/System.DirectoryServices.xml", "build/.NETFramework/v4.8/System.Drawing.Design.dll", "build/.NETFramework/v4.8/System.Drawing.Design.xml", "build/.NETFramework/v4.8/System.Drawing.dll", "build/.NETFramework/v4.8/System.Drawing.xml", "build/.NETFramework/v4.8/System.Dynamic.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.dll", "build/.NETFramework/v4.8/System.EnterpriseServices.xml", "build/.NETFramework/v4.8/System.IO.Compression.FileSystem.dll", "build/.NETFramework/v4.8/System.IO.Compression.FileSystem.xml", "build/.NETFramework/v4.8/System.IO.Compression.dll", "build/.NETFramework/v4.8/System.IO.Compression.xml", "build/.NETFramework/v4.8/System.IO.Log.dll", "build/.NETFramework/v4.8/System.IO.Log.xml", "build/.NETFramework/v4.8/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.8/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.8/System.IdentityModel.Services.dll", "build/.NETFramework/v4.8/System.IdentityModel.Services.xml", "build/.NETFramework/v4.8/System.IdentityModel.dll", "build/.NETFramework/v4.8/System.IdentityModel.xml", "build/.NETFramework/v4.8/System.Linq.xml", "build/.NETFramework/v4.8/System.Management.Instrumentation.dll", "build/.NETFramework/v4.8/System.Management.Instrumentation.xml", "build/.NETFramework/v4.8/System.Management.dll", "build/.NETFramework/v4.8/System.Management.xml", "build/.NETFramework/v4.8/System.Messaging.dll", "build/.NETFramework/v4.8/System.Messaging.xml", "build/.NETFramework/v4.8/System.Net.Http.WebRequest.dll", "build/.NETFramework/v4.8/System.Net.Http.WebRequest.xml", "build/.NETFramework/v4.8/System.Net.Http.dll", "build/.NETFramework/v4.8/System.Net.Http.xml", "build/.NETFramework/v4.8/System.Net.dll", "build/.NETFramework/v4.8/System.Net.xml", "build/.NETFramework/v4.8/System.Numerics.dll", "build/.NETFramework/v4.8/System.Numerics.xml", "build/.NETFramework/v4.8/System.Printing.dll", "build/.NETFramework/v4.8/System.Printing.xml", "build/.NETFramework/v4.8/System.Reflection.Context.dll", "build/.NETFramework/v4.8/System.Reflection.Context.xml", "build/.NETFramework/v4.8/System.Runtime.Caching.dll", "build/.NETFramework/v4.8/System.Runtime.Caching.xml", "build/.NETFramework/v4.8/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.8/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.8/System.Runtime.Remoting.dll", "build/.NETFramework/v4.8/System.Runtime.Remoting.xml", "build/.NETFramework/v4.8/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.8/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.8/System.Runtime.Serialization.dll", "build/.NETFramework/v4.8/System.Runtime.Serialization.xml", "build/.NETFramework/v4.8/System.Security.dll", "build/.NETFramework/v4.8/System.Security.xml", "build/.NETFramework/v4.8/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.8/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.8/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.8/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.8/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.8/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.8/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.8/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.8/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.8/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.8/System.ServiceModel.Web.dll", "build/.NETFramework/v4.8/System.ServiceModel.Web.xml", "build/.NETFramework/v4.8/System.ServiceModel.dll", "build/.NETFramework/v4.8/System.ServiceModel.xml", "build/.NETFramework/v4.8/System.ServiceProcess.dll", "build/.NETFramework/v4.8/System.ServiceProcess.xml", "build/.NETFramework/v4.8/System.Speech.dll", "build/.NETFramework/v4.8/System.Speech.xml", "build/.NETFramework/v4.8/System.Threading.Tasks.Dataflow.xml", "build/.NETFramework/v4.8/System.Transactions.dll", "build/.NETFramework/v4.8/System.Transactions.xml", "build/.NETFramework/v4.8/System.Web.Abstractions.dll", "build/.NETFramework/v4.8/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.8/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.8/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.8/System.Web.DataVisualization.dll", "build/.NETFramework/v4.8/System.Web.DataVisualization.xml", "build/.NETFramework/v4.8/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.8/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.8/System.Web.DynamicData.dll", "build/.NETFramework/v4.8/System.Web.DynamicData.xml", "build/.NETFramework/v4.8/System.Web.Entity.Design.dll", "build/.NETFramework/v4.8/System.Web.Entity.Design.xml", "build/.NETFramework/v4.8/System.Web.Entity.dll", "build/.NETFramework/v4.8/System.Web.Entity.xml", "build/.NETFramework/v4.8/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.8/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.8/System.Web.Extensions.dll", "build/.NETFramework/v4.8/System.Web.Extensions.xml", "build/.NETFramework/v4.8/System.Web.Mobile.dll", "build/.NETFramework/v4.8/System.Web.Mobile.xml", "build/.NETFramework/v4.8/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.8/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.8/System.Web.Routing.dll", "build/.NETFramework/v4.8/System.Web.Services.dll", "build/.NETFramework/v4.8/System.Web.Services.xml", "build/.NETFramework/v4.8/System.Web.dll", "build/.NETFramework/v4.8/System.Web.xml", "build/.NETFramework/v4.8/System.Windows.Controls.Ribbon.dll", "build/.NETFramework/v4.8/System.Windows.Controls.Ribbon.xml", "build/.NETFramework/v4.8/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.8/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.8/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.8/System.Windows.Forms.dll", "build/.NETFramework/v4.8/System.Windows.Forms.xml", "build/.NETFramework/v4.8/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.8/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.8/System.Windows.Presentation.dll", "build/.NETFramework/v4.8/System.Windows.Presentation.xml", "build/.NETFramework/v4.8/System.Windows.dll", "build/.NETFramework/v4.8/System.Workflow.Activities.dll", "build/.NETFramework/v4.8/System.Workflow.Activities.xml", "build/.NETFramework/v4.8/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.8/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.8/System.Workflow.Runtime.dll", "build/.NETFramework/v4.8/System.Workflow.Runtime.xml", "build/.NETFramework/v4.8/System.WorkflowServices.dll", "build/.NETFramework/v4.8/System.WorkflowServices.xml", "build/.NETFramework/v4.8/System.Xaml.dll", "build/.NETFramework/v4.8/System.Xaml.xml", "build/.NETFramework/v4.8/System.Xml.Linq.dll", "build/.NETFramework/v4.8/System.Xml.Linq.xml", "build/.NETFramework/v4.8/System.Xml.Serialization.dll", "build/.NETFramework/v4.8/System.Xml.dll", "build/.NETFramework/v4.8/System.Xml.xml", "build/.NETFramework/v4.8/System.dll", "build/.NETFramework/v4.8/System.xml", "build/.NETFramework/v4.8/UIAutomationClient.dll", "build/.NETFramework/v4.8/UIAutomationClient.xml", "build/.NETFramework/v4.8/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.8/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.8/UIAutomationProvider.dll", "build/.NETFramework/v4.8/UIAutomationProvider.xml", "build/.NETFramework/v4.8/UIAutomationTypes.dll", "build/.NETFramework/v4.8/UIAutomationTypes.xml", "build/.NETFramework/v4.8/WindowsBase.dll", "build/.NETFramework/v4.8/WindowsBase.xml", "build/.NETFramework/v4.8/WindowsFormsIntegration.dll", "build/.NETFramework/v4.8/WindowsFormsIntegration.xml", "build/.NETFramework/v4.8/XamlBuildTask.dll", "build/.NETFramework/v4.8/XamlBuildTask.xml", "build/.NETFramework/v4.8/mscorlib.dll", "build/.NETFramework/v4.8/mscorlib.xml", "build/.NETFramework/v4.8/namespaces.xml", "build/.NETFramework/v4.8/sysglobl.dll", "build/.NETFramework/v4.8/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net48.targets", "microsoft.netframework.referenceassemblies.net48.1.0.0.nupkg.sha512", "microsoft.netframework.referenceassemblies.net48.nuspec"]}, "Microsoft.Office.Interop.Excel/15.0.4795.1001": {"sha512": "cuvqi/U5MYSM0gvR2l90q0m/urRgmg69EiwP5VWp1RcaJ0YT5G26Va5LaOZ3KJFc22FNihS5CUjeePUp2YpGQA==", "type": "package", "path": "microsoft.office.interop.excel/15.0.4795.1001", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/Microsoft.Office.Interop.Excel.dll", "lib/netstandard2.0/Microsoft.Office.Interop.Excel.dll", "microsoft.office.interop.excel.15.0.4795.1001.nupkg.sha512", "microsoft.office.interop.excel.nuspec"]}, "Microsoft.Office.Interop.Word/15.0.4797.1004": {"sha512": "lavTOrMGK/o/t3+gGr7JuVrL8XDdnS0DwPLcYv/StOiysXO4L0C36IXTVtEJo1Mh9Aoxo9a8dgGcPi+faTlv0g==", "type": "package", "path": "microsoft.office.interop.word/15.0.4797.1004", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/Microsoft.Office.Interop.Word.dll", "lib/netstandard2.0/Microsoft.Office.Interop.Word.dll", "microsoft.office.interop.word.15.0.4797.1004.nupkg.sha512", "microsoft.office.interop.word.nuspec"]}, "Microsoft.SqlServer.SqlManagementObjects/172.64.0": {"sha512": "PeDuHwemW+MBzlERGp2nDz4XUdHScnJO59Q3stXJ7RPWT2+WoWV4VK2IlsIAZIQWLCvNvH3cAauJThPGtr+67g==", "type": "package", "path": "microsoft.sqlserver.sqlmanagementobjects/172.64.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net472/Microsoft.Data.Tools.Sql.BatchParser.dll", "lib/net472/Microsoft.Data.Tools.Sql.BatchParser.pdb", "lib/net472/Microsoft.Data.Tools.Sql.BatchParser.xml", "lib/net472/Microsoft.SqlServer.ConnectionInfo.dll", "lib/net472/Microsoft.SqlServer.ConnectionInfo.pdb", "lib/net472/Microsoft.SqlServer.ConnectionInfo.xml", "lib/net472/Microsoft.SqlServer.Dmf.Common.dll", "lib/net472/Microsoft.SqlServer.Dmf.Common.pdb", "lib/net472/Microsoft.SqlServer.Dmf.Common.xml", "lib/net472/Microsoft.SqlServer.Dmf.dll", "lib/net472/Microsoft.SqlServer.Dmf.pdb", "lib/net472/Microsoft.SqlServer.Dmf.xml", "lib/net472/Microsoft.SqlServer.Management.Collector.dll", "lib/net472/Microsoft.SqlServer.Management.Collector.pdb", "lib/net472/Microsoft.SqlServer.Management.Collector.xml", "lib/net472/Microsoft.SqlServer.Management.CollectorEnum.dll", "lib/net472/Microsoft.SqlServer.Management.CollectorEnum.pdb", "lib/net472/Microsoft.SqlServer.Management.CollectorEnum.xml", "lib/net472/Microsoft.SqlServer.Management.HadrData.dll", "lib/net472/Microsoft.SqlServer.Management.HadrData.pdb", "lib/net472/Microsoft.SqlServer.Management.HadrData.xml", "lib/net472/Microsoft.SqlServer.Management.HadrModel.dll", "lib/net472/Microsoft.SqlServer.Management.HadrModel.pdb", "lib/net472/Microsoft.SqlServer.Management.HadrModel.xml", "lib/net472/Microsoft.SqlServer.Management.RegisteredServers.dll", "lib/net472/Microsoft.SqlServer.Management.RegisteredServers.pdb", "lib/net472/Microsoft.SqlServer.Management.RegisteredServers.xml", "lib/net472/Microsoft.SqlServer.Management.Sdk.Sfc.dll", "lib/net472/Microsoft.SqlServer.Management.Sdk.Sfc.pdb", "lib/net472/Microsoft.SqlServer.Management.Sdk.Sfc.xml", "lib/net472/Microsoft.SqlServer.Management.SqlScriptPublish.dll", "lib/net472/Microsoft.SqlServer.Management.SqlScriptPublish.pdb", "lib/net472/Microsoft.SqlServer.Management.SqlScriptPublish.xml", "lib/net472/Microsoft.SqlServer.Management.XEvent.dll", "lib/net472/Microsoft.SqlServer.Management.XEvent.pdb", "lib/net472/Microsoft.SqlServer.Management.XEvent.xml", "lib/net472/Microsoft.SqlServer.Management.XEventDbScoped.dll", "lib/net472/Microsoft.SqlServer.Management.XEventDbScoped.pdb", "lib/net472/Microsoft.SqlServer.Management.XEventDbScoped.xml", "lib/net472/Microsoft.SqlServer.Management.XEventDbScopedEnum.dll", "lib/net472/Microsoft.SqlServer.Management.XEventDbScopedEnum.pdb", "lib/net472/Microsoft.SqlServer.Management.XEventDbScopedEnum.xml", "lib/net472/Microsoft.SqlServer.Management.XEventEnum.dll", "lib/net472/Microsoft.SqlServer.Management.XEventEnum.pdb", "lib/net472/Microsoft.SqlServer.Management.XEventEnum.xml", "lib/net472/Microsoft.SqlServer.PolicyEnum.dll", "lib/net472/Microsoft.SqlServer.PolicyEnum.pdb", "lib/net472/Microsoft.SqlServer.PolicyEnum.xml", "lib/net472/Microsoft.SqlServer.RegSvrEnum.dll", "lib/net472/Microsoft.SqlServer.RegSvrEnum.pdb", "lib/net472/Microsoft.SqlServer.RegSvrEnum.xml", "lib/net472/Microsoft.SqlServer.ServiceBrokerEnum.dll", "lib/net472/Microsoft.SqlServer.ServiceBrokerEnum.pdb", "lib/net472/Microsoft.SqlServer.ServiceBrokerEnum.xml", "lib/net472/Microsoft.SqlServer.Smo.Notebook.dll", "lib/net472/Microsoft.SqlServer.Smo.Notebook.pdb", "lib/net472/Microsoft.SqlServer.Smo.Notebook.xml", "lib/net472/Microsoft.SqlServer.Smo.dll", "lib/net472/Microsoft.SqlServer.Smo.pdb", "lib/net472/Microsoft.SqlServer.Smo.xml", "lib/net472/Microsoft.SqlServer.SmoExtended.dll", "lib/net472/Microsoft.SqlServer.SmoExtended.pdb", "lib/net472/Microsoft.SqlServer.SmoExtended.xml", "lib/net472/Microsoft.SqlServer.SqlClrProvider.dll", "lib/net472/Microsoft.SqlServer.SqlClrProvider.pdb", "lib/net472/Microsoft.SqlServer.SqlEnum.dll", "lib/net472/Microsoft.SqlServer.SqlEnum.pdb", "lib/net472/Microsoft.SqlServer.SqlEnum.xml", "lib/net472/Microsoft.SqlServer.SqlWmiManagement.dll", "lib/net472/Microsoft.SqlServer.SqlWmiManagement.pdb", "lib/net472/Microsoft.SqlServer.SqlWmiManagement.xml", "lib/net472/Microsoft.SqlServer.WmiEnum.dll", "lib/net472/Microsoft.SqlServer.WmiEnum.pdb", "lib/net472/Microsoft.SqlServer.WmiEnum.xml", "lib/net8.0/Microsoft.Data.Tools.Sql.BatchParser.dll", "lib/net8.0/Microsoft.Data.Tools.Sql.BatchParser.pdb", "lib/net8.0/Microsoft.Data.Tools.Sql.BatchParser.xml", "lib/net8.0/Microsoft.SqlServer.ConnectionInfo.dll", "lib/net8.0/Microsoft.SqlServer.ConnectionInfo.pdb", "lib/net8.0/Microsoft.SqlServer.ConnectionInfo.xml", "lib/net8.0/Microsoft.SqlServer.Dmf.Common.dll", "lib/net8.0/Microsoft.SqlServer.Dmf.Common.pdb", "lib/net8.0/Microsoft.SqlServer.Dmf.Common.xml", "lib/net8.0/Microsoft.SqlServer.Dmf.dll", "lib/net8.0/Microsoft.SqlServer.Dmf.pdb", "lib/net8.0/Microsoft.SqlServer.Dmf.xml", "lib/net8.0/Microsoft.SqlServer.Management.Collector.dll", "lib/net8.0/Microsoft.SqlServer.Management.Collector.pdb", "lib/net8.0/Microsoft.SqlServer.Management.Collector.xml", "lib/net8.0/Microsoft.SqlServer.Management.CollectorEnum.dll", "lib/net8.0/Microsoft.SqlServer.Management.CollectorEnum.pdb", "lib/net8.0/Microsoft.SqlServer.Management.CollectorEnum.xml", "lib/net8.0/Microsoft.SqlServer.Management.HadrData.dll", "lib/net8.0/Microsoft.SqlServer.Management.HadrData.pdb", "lib/net8.0/Microsoft.SqlServer.Management.HadrData.xml", "lib/net8.0/Microsoft.SqlServer.Management.HadrModel.dll", "lib/net8.0/Microsoft.SqlServer.Management.HadrModel.pdb", "lib/net8.0/Microsoft.SqlServer.Management.HadrModel.xml", "lib/net8.0/Microsoft.SqlServer.Management.RegisteredServers.dll", "lib/net8.0/Microsoft.SqlServer.Management.RegisteredServers.pdb", "lib/net8.0/Microsoft.SqlServer.Management.RegisteredServers.xml", "lib/net8.0/Microsoft.SqlServer.Management.Sdk.Sfc.dll", "lib/net8.0/Microsoft.SqlServer.Management.Sdk.Sfc.pdb", "lib/net8.0/Microsoft.SqlServer.Management.Sdk.Sfc.xml", "lib/net8.0/Microsoft.SqlServer.Management.SqlScriptPublish.dll", "lib/net8.0/Microsoft.SqlServer.Management.SqlScriptPublish.pdb", "lib/net8.0/Microsoft.SqlServer.Management.SqlScriptPublish.xml", "lib/net8.0/Microsoft.SqlServer.Management.XEvent.dll", "lib/net8.0/Microsoft.SqlServer.Management.XEvent.pdb", "lib/net8.0/Microsoft.SqlServer.Management.XEvent.xml", "lib/net8.0/Microsoft.SqlServer.Management.XEventDbScoped.dll", "lib/net8.0/Microsoft.SqlServer.Management.XEventDbScoped.pdb", "lib/net8.0/Microsoft.SqlServer.Management.XEventDbScoped.xml", "lib/net8.0/Microsoft.SqlServer.Management.XEventDbScopedEnum.dll", "lib/net8.0/Microsoft.SqlServer.Management.XEventDbScopedEnum.pdb", "lib/net8.0/Microsoft.SqlServer.Management.XEventDbScopedEnum.xml", "lib/net8.0/Microsoft.SqlServer.Management.XEventEnum.dll", "lib/net8.0/Microsoft.SqlServer.Management.XEventEnum.pdb", "lib/net8.0/Microsoft.SqlServer.Management.XEventEnum.xml", "lib/net8.0/Microsoft.SqlServer.PolicyEnum.dll", "lib/net8.0/Microsoft.SqlServer.PolicyEnum.pdb", "lib/net8.0/Microsoft.SqlServer.PolicyEnum.xml", "lib/net8.0/Microsoft.SqlServer.ServiceBrokerEnum.dll", "lib/net8.0/Microsoft.SqlServer.ServiceBrokerEnum.pdb", "lib/net8.0/Microsoft.SqlServer.ServiceBrokerEnum.xml", "lib/net8.0/Microsoft.SqlServer.Smo.Notebook.dll", "lib/net8.0/Microsoft.SqlServer.Smo.Notebook.pdb", "lib/net8.0/Microsoft.SqlServer.Smo.Notebook.xml", "lib/net8.0/Microsoft.SqlServer.Smo.dll", "lib/net8.0/Microsoft.SqlServer.Smo.pdb", "lib/net8.0/Microsoft.SqlServer.Smo.xml", "lib/net8.0/Microsoft.SqlServer.SmoExtended.dll", "lib/net8.0/Microsoft.SqlServer.SmoExtended.pdb", "lib/net8.0/Microsoft.SqlServer.SmoExtended.xml", "lib/net8.0/Microsoft.SqlServer.SqlEnum.dll", "lib/net8.0/Microsoft.SqlServer.SqlEnum.pdb", "lib/net8.0/Microsoft.SqlServer.SqlEnum.xml", "lib/net8.0/Microsoft.SqlServer.SqlWmiManagement.dll", "lib/net8.0/Microsoft.SqlServer.SqlWmiManagement.pdb", "lib/net8.0/Microsoft.SqlServer.SqlWmiManagement.xml", "lib/net8.0/Microsoft.SqlServer.WmiEnum.dll", "lib/net8.0/Microsoft.SqlServer.WmiEnum.pdb", "lib/net8.0/Microsoft.SqlServer.WmiEnum.xml", "microsoft.sqlserver.sqlmanagementobjects.172.64.0.nupkg.sha512", "microsoft.sqlserver.sqlmanagementobjects.nuspec", "nuspecicon.png"]}, "Microsoft.Toolkit.Uwp.Notifications/7.1.3": {"sha512": "A1dglAzb24gjehmb7DwGd07mfyZ1gacAK7ObE0KwDlRc3mayH2QW7cSOy3TkkyELjLg19OQBuhPOj4SpXET9lg==", "type": "package", "path": "microsoft.toolkit.uwp.notifications/7.1.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "build/native/Microsoft.Toolkit.Uwp.Notifications.targets", "lib/native/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/native/Microsoft.Toolkit.Uwp.Notifications.pri", "lib/native/Microsoft.Toolkit.Uwp.Notifications.winmd", "lib/native/Microsoft.Toolkit.Uwp.Notifications.xml", "lib/net461/Microsoft.Toolkit.Uwp.Notifications.dll", "lib/net461/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/net461/Microsoft.Toolkit.Uwp.Notifications.xml", "lib/net5.0-windows10.0.17763/Microsoft.Toolkit.Uwp.Notifications.dll", "lib/net5.0-windows10.0.17763/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/net5.0-windows10.0.17763/Microsoft.Toolkit.Uwp.Notifications.xml", "lib/net5.0/Microsoft.Toolkit.Uwp.Notifications.dll", "lib/net5.0/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/net5.0/Microsoft.Toolkit.Uwp.Notifications.xml", "lib/netcoreapp3.1/Microsoft.Toolkit.Uwp.Notifications.dll", "lib/netcoreapp3.1/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/netcoreapp3.1/Microsoft.Toolkit.Uwp.Notifications.xml", "lib/netstandard1.4/Microsoft.Toolkit.Uwp.Notifications.dll", "lib/netstandard1.4/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/netstandard1.4/Microsoft.Toolkit.Uwp.Notifications.xml", "lib/uap10.0.10240/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/uap10.0.10240/Microsoft.Toolkit.Uwp.Notifications.pri", "lib/uap10.0.10240/Microsoft.Toolkit.Uwp.Notifications.winmd", "lib/uap10.0.10240/Microsoft.Toolkit.Uwp.Notifications.xml", "lib/uap10.0.16299/Microsoft.Toolkit.Uwp.Notifications.dll", "lib/uap10.0.16299/Microsoft.Toolkit.Uwp.Notifications.pdb", "lib/uap10.0.16299/Microsoft.Toolkit.Uwp.Notifications.pri", "lib/uap10.0.16299/Microsoft.Toolkit.Uwp.Notifications.xml", "microsoft.toolkit.uwp.notifications.7.1.3.nupkg.sha512", "microsoft.toolkit.uwp.notifications.nuspec"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry.AccessControl/10.0.0-preview.1.25080.5": {"sha512": "qsl+VRImibYBmbf7pJxDDLRwg9G20J+HGML1vxRAFKwiNKucp1CFJz+2J1oG1RK+Ddhv3HuX8aUHC7Fzc+k66A==", "type": "package", "path": "microsoft.win32.registry.accesscontrol/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.Registry.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.Registry.AccessControl.targets", "lib/net10.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net10.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/net462/Microsoft.Win32.Registry.AccessControl.dll", "lib/net462/Microsoft.Win32.Registry.AccessControl.xml", "lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net8.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/net9.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net9.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.xml", "microsoft.win32.registry.accesscontrol.10.0.0-preview.1.25080.5.nupkg.sha512", "microsoft.win32.registry.accesscontrol.nuspec", "runtimes/win/lib/net10.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net10.0/Microsoft.Win32.Registry.AccessControl.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.xml", "runtimes/win/lib/net9.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net9.0/Microsoft.Win32.Registry.AccessControl.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Win32.SystemEvents/10.0.0-preview.1.25080.5": {"sha512": "5WIKddfHK+qhwqoobdGuKRBcwNO4W1iENnZO5f6KM9Ph0F2Q/NgH4NaV2HOeFevSZuFAl5A/evA4VNEOT9bOfg==", "type": "package", "path": "microsoft.win32.systemevents/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.SystemEvents.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.SystemEvents.targets", "lib/net10.0/Microsoft.Win32.SystemEvents.dll", "lib/net10.0/Microsoft.Win32.SystemEvents.xml", "lib/net462/Microsoft.Win32.SystemEvents.dll", "lib/net462/Microsoft.Win32.SystemEvents.xml", "lib/net8.0/Microsoft.Win32.SystemEvents.dll", "lib/net8.0/Microsoft.Win32.SystemEvents.xml", "lib/net9.0/Microsoft.Win32.SystemEvents.dll", "lib/net9.0/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.10.0.0-preview.1.25080.5.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "runtimes/win/lib/net10.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net10.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/net9.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Windows.Compatibility/10.0.0-preview.1.25080.4": {"sha512": "j+h/K6hcmQvGkyzsYPn1MAs3rAlfk9Uq0J6NXQGk4AF0XZ1kr2HEu/x/2p4FGZ5n37j/4JJJJszBPUVdQ7IzCg==", "type": "package", "path": "microsoft.windows.compatibility/10.0.0-preview.1.25080.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "microsoft.windows.compatibility.10.0.0-preview.1.25080.4.nupkg.sha512", "microsoft.windows.compatibility.nuspec"]}, "Microsoft.Windows.SDK.Contracts/10.0.19041.1": {"sha512": "sgDwuoyubbLFNJR/BINbvfSNRiglF91D+Q0uEAkU4ZTO5Hgbnu8+gA4TCc65S56e1kK7gvR1+H4kphkDTr+9bw==", "type": "package", "path": "microsoft.windows.sdk.contracts/10.0.19041.1", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.Windows.SDK.Contracts.props", "build/Microsoft.Windows.SDK.Contracts.targets", "c/Catalogs/cat353be8f91891a6a5761b9ac157fa2ff1.cat", "c/Catalogs/cat4ec14c5368b7642563c070cd168960a8.cat", "c/Catalogs/cate59830bab4961666e8d8c2af1e5fa771.cat", "c/Catalogs/catf105a73f98cfc88c7b64d8f7b39a474c.cat", "microsoft.windows.sdk.contracts.10.0.19041.1.nupkg.sha512", "microsoft.windows.sdk.contracts.nuspec", "ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd", "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd", "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd", "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd", "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd", "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd", "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd", "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd", "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd", "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd", "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd", "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd", "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd", "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd", "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd", "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd", "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd", "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd", "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd", "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd", "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd", "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd", "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD", "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd", "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd", "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd", "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd", "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD", "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd", "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd", "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd", "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd", "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd", "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd", "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd", "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd", "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd", "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd", "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd", "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd", "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd", "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd", "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd", "ref/netstandard2.0/Windows.WinMD", "ref/netstandard2.0/en/Windows.AI.MachineLearning.MachineLearningContract.xml", "ref/netstandard2.0/en/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsPhoneContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsVoipContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.LockScreenCallContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.FullTrustAppContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.Core.SearchCoreContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.SearchContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.SocialInfo.SocialInfoContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.StartupTaskContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Wallet.WalletContract.xml", "ref/netstandard2.0/en/Windows.Devices.Custom.CustomDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.DevicesLowLevelContract.xml", "ref/netstandard2.0/en/Windows.Devices.Portable.PortableDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.Extensions.ExtensionsContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.PrintersContract.xml", "ref/netstandard2.0/en/Windows.Devices.Scanners.ScannerDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardEmulatorContract.xml", "ref/netstandard2.0/en/Windows.Devices.Sms.LegacySmsApiContract.xml", "ref/netstandard2.0/en/Windows.Foundation.FoundationContract.xml", "ref/netstandard2.0/en/Windows.Foundation.UniversalApiContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Input.GamingInputPreviewContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Preview.GamesEnumerationContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GameChatOverlayContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GamingUIProviderContract.xml", "ref/netstandard2.0/en/Windows.Gaming.XboxLive.StorageApiContract.xml", "ref/netstandard2.0/en/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.xml", "ref/netstandard2.0/en/Windows.Graphics.Printing3D.Printing3DContract.xml", "ref/netstandard2.0/en/Windows.Management.Deployment.Preview.DeploymentPreviewContract.xml", "ref/netstandard2.0/en/Windows.Management.Workplace.WorkplaceSettingsContract.xml", "ref/netstandard2.0/en/Windows.Media.AppBroadcasting.AppBroadcastingContract.xml", "ref/netstandard2.0/en/Windows.Media.AppRecording.AppRecordingContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppBroadcastContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureMetadataContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.CameraCaptureUIContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.GameBarContract.xml", "ref/netstandard2.0/en/Windows.Media.Devices.CallControlContract.xml", "ref/netstandard2.0/en/Windows.Media.MediaControlContract.xml", "ref/netstandard2.0/en/Windows.Media.Playlists.PlaylistsContract.xml", "ref/netstandard2.0/en/Windows.Media.Protection.ProtectionRenewalContract.xml", "ref/netstandard2.0/en/Windows.Networking.Connectivity.WwanContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.xml", "ref/netstandard2.0/en/Windows.Networking.Sockets.ControlChannelTriggerContract.xml", "ref/netstandard2.0/en/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.xml", "ref/netstandard2.0/en/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.xml", "ref/netstandard2.0/en/Windows.Phone.PhoneContract.xml", "ref/netstandard2.0/en/Windows.Phone.StartScreen.DualSimTileContract.xml", "ref/netstandard2.0/en/Windows.Security.EnterpriseData.EnterpriseDataContract.xml", "ref/netstandard2.0/en/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.GuidanceContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.LocalSearchContract.xml", "ref/netstandard2.0/en/Windows.Services.Store.StoreContract.xml", "ref/netstandard2.0/en/Windows.Services.TargetedContent.TargetedContentContract.xml", "ref/netstandard2.0/en/Windows.Storage.Provider.CloudFilesContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileHardwareTokenContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileRetailInfoContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileSharedModeContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.xml", "ref/netstandard2.0/en/Windows.System.SystemManagementContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileLockScreenContract.xml", "ref/netstandard2.0/en/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.CoreWindowDialogsContract.xml", "ref/netstandard2.0/en/Windows.UI.Shell.SecurityAppManagerContract.xml", "ref/netstandard2.0/en/Windows.UI.ViewManagement.ViewManagementViewScalingContract.xml", "ref/netstandard2.0/en/Windows.UI.WebUI.Core.WebUICommandBarContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Core.Direct.XamlDirectContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Hosting.HostingContract.xml", "ref/netstandard2.0/en/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.xml"]}, "Newtonsoft.Json/13.0.1": {"sha512": "ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "type": "package", "path": "newtonsoft.json/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.1.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "RBush/4.0.0": {"sha512": "j3GeRxxLUQdc+UrZnvythdQxi3bd8ayn87VDjfGXrvfodF550n9wR6SgQvpo+YiAv3GJezsu6lK0l47rRqnbdg==", "type": "package", "path": "rbush/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net47/RBush.dll", "lib/net47/RBush.xml", "lib/net8.0/RBush.dll", "lib/net8.0/RBush.xml", "lib/netstandard2.0/RBush.dll", "lib/netstandard2.0/RBush.xml", "rbush.4.0.0.nupkg.sha512", "rbush.nuspec", "readme.md"]}, "SixLabors.Fonts/1.0.0": {"sha512": "LFQsCZlV0xlUyXAOMUo5kkSl+8zAQXXbbdwWchtk0B4o7zotZhQsQOcJUELGHdfPfm/xDAsz6hONAuV25bJaAg==", "type": "package", "path": "sixlabors.fonts/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/SixLabors.Fonts.dll", "lib/netcoreapp3.1/SixLabors.Fonts.xml", "lib/netstandard2.0/SixLabors.Fonts.dll", "lib/netstandard2.0/SixLabors.Fonts.xml", "lib/netstandard2.1/SixLabors.Fonts.dll", "lib/netstandard2.1/SixLabors.Fonts.xml", "sixlabors.fonts.1.0.0.nupkg.sha512", "sixlabors.fonts.128.png", "sixlabors.fonts.nuspec"]}, "System.Buffers/4.6.0": {"sha512": "lN6tZi7Q46zFzAbRYXTIvfXcyvQQgxnY7Xm6C6xQ9784dEL1amjM6S6Iw4ZpsvesAKnRVsM4scrDQaDqSClkjA==", "type": "package", "path": "system.buffers/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Buffers.targets", "buildTransitive/net462/_._", "lib/net462/System.Buffers.dll", "lib/net462/System.Buffers.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "system.buffers.4.6.0.nupkg.sha512", "system.buffers.nuspec"]}, "System.ClientModel/1.0.0": {"sha512": "I3CVkvxeqFYjIVEP59DnjbeoGNfo/+SZrCLpRz2v/g0gpCHaEMPtWSY0s9k/7jR1rAsLNg2z2u1JRB76tPjnIw==", "type": "package", "path": "system.clientmodel/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.0.0.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.CodeDom/10.0.0-preview.1.25080.5": {"sha512": "73fcla4sj2CYSsshJ4lMxqltxgK7zEHYFRQ4lsttkTI/NCeIyG42wO9H0Quhrvjd1i95f3RGe5lJNVma14vqZQ==", "type": "package", "path": "system.codedom/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net10.0/System.CodeDom.dll", "lib/net10.0/System.CodeDom.xml", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/net9.0/System.CodeDom.dll", "lib/net9.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.10.0.0-preview.1.25080.5.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.ComponentModel.Composition/10.0.0-preview.1.25080.5": {"sha512": "/tc1reWHaQMDxW2latxzfF1/oB3qskxmMHZPmBdq7pa40CRwQ1ExBiCB+/9YM9eGqgqGdV9XPFDmzmMhU7pzbQ==", "type": "package", "path": "system.componentmodel.composition/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.ComponentModel.Composition.targets", "lib/net10.0/System.ComponentModel.Composition.dll", "lib/net10.0/System.ComponentModel.Composition.xml", "lib/net462/_._", "lib/net8.0/System.ComponentModel.Composition.dll", "lib/net8.0/System.ComponentModel.Composition.xml", "lib/net9.0/System.ComponentModel.Composition.dll", "lib/net9.0/System.ComponentModel.Composition.xml", "lib/netstandard2.0/System.ComponentModel.Composition.dll", "lib/netstandard2.0/System.ComponentModel.Composition.xml", "system.componentmodel.composition.10.0.0-preview.1.25080.5.nupkg.sha512", "system.componentmodel.composition.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/10.0.0-preview.1.25080.5": {"sha512": "LFWkl3RmlH2fKV+j7/mC5miiAaDMwLpkMucuvPYx4aIAUdUCBWBPNaNvV+gWlPe1uhxZPECgAUvSs3isE4tfXA==", "type": "package", "path": "system.configuration.configurationmanager/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net10.0/System.Configuration.ConfigurationManager.dll", "lib/net10.0/System.Configuration.ConfigurationManager.xml", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/net9.0/System.Configuration.ConfigurationManager.dll", "lib/net9.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.10.0.0-preview.1.25080.5.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.DataSetExtensions/4.5.0": {"sha512": "221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "type": "package", "path": "system.data.datasetextensions/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/_._", "lib/netstandard2.0/System.Data.DataSetExtensions.dll", "ref/net45/_._", "ref/netstandard2.0/System.Data.DataSetExtensions.dll", "system.data.datasetextensions.4.5.0.nupkg.sha512", "system.data.datasetextensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Data.Odbc/10.0.0-preview.1.25080.5": {"sha512": "ATnmnfJgKB1UvE4zly5Btlm9E+oRedkC3AmaBF/C4KARlP4eFZwTizLCP7odFhc7x50Dlhs43xhUxoaWNa0qkQ==", "type": "package", "path": "system.data.odbc/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.Odbc.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Data.Odbc.targets", "lib/net10.0/System.Data.Odbc.dll", "lib/net10.0/System.Data.Odbc.xml", "lib/net462/System.Data.Odbc.dll", "lib/net462/System.Data.Odbc.xml", "lib/net8.0/System.Data.Odbc.dll", "lib/net8.0/System.Data.Odbc.xml", "lib/net9.0/System.Data.Odbc.dll", "lib/net9.0/System.Data.Odbc.xml", "lib/netstandard2.0/System.Data.Odbc.dll", "lib/netstandard2.0/System.Data.Odbc.xml", "runtimes/unix/lib/net10.0/System.Data.Odbc.dll", "runtimes/unix/lib/net10.0/System.Data.Odbc.xml", "runtimes/unix/lib/net8.0/System.Data.Odbc.dll", "runtimes/unix/lib/net8.0/System.Data.Odbc.xml", "runtimes/unix/lib/net9.0/System.Data.Odbc.dll", "runtimes/unix/lib/net9.0/System.Data.Odbc.xml", "runtimes/win/lib/net10.0/System.Data.Odbc.dll", "runtimes/win/lib/net10.0/System.Data.Odbc.xml", "runtimes/win/lib/net8.0/System.Data.Odbc.dll", "runtimes/win/lib/net8.0/System.Data.Odbc.xml", "runtimes/win/lib/net9.0/System.Data.Odbc.dll", "runtimes/win/lib/net9.0/System.Data.Odbc.xml", "system.data.odbc.10.0.0-preview.1.25080.5.nupkg.sha512", "system.data.odbc.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/10.0.0-preview.1.25080.5": {"sha512": "y14UQBOMjLJPnQ9nc6x4ZCEHRwQ//zcJUynUjmaJdK096lGmaCnPFUZWixqiV8EegXxeA86DN0P9U1U8W33ZPQ==", "type": "package", "path": "system.data.oledb/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.OleDb.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "lib/net10.0/System.Data.OleDb.dll", "lib/net10.0/System.Data.OleDb.xml", "lib/net462/System.Data.OleDb.dll", "lib/net462/System.Data.OleDb.xml", "lib/net8.0/System.Data.OleDb.dll", "lib/net8.0/System.Data.OleDb.xml", "lib/net9.0/System.Data.OleDb.dll", "lib/net9.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net10.0/System.Data.OleDb.dll", "runtimes/win/lib/net10.0/System.Data.OleDb.xml", "runtimes/win/lib/net8.0/System.Data.OleDb.dll", "runtimes/win/lib/net8.0/System.Data.OleDb.xml", "runtimes/win/lib/net9.0/System.Data.OleDb.dll", "runtimes/win/lib/net9.0/System.Data.OleDb.xml", "system.data.oledb.10.0.0-preview.1.25080.5.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.9.0": {"sha512": "j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "type": "package", "path": "system.data.sqlclient/4.9.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Data.SqlClient.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.SqlClient.targets", "lib/net462/System.Data.SqlClient.dll", "lib/net462/System.Data.SqlClient.xml", "lib/net6.0/System.Data.SqlClient.dll", "lib/net6.0/System.Data.SqlClient.xml", "lib/net8.0/System.Data.SqlClient.dll", "lib/net8.0/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/unix/lib/net6.0/System.Data.SqlClient.dll", "runtimes/unix/lib/net6.0/System.Data.SqlClient.xml", "runtimes/unix/lib/net8.0/System.Data.SqlClient.dll", "runtimes/unix/lib/net8.0/System.Data.SqlClient.xml", "runtimes/win/lib/net6.0/System.Data.SqlClient.dll", "runtimes/win/lib/net6.0/System.Data.SqlClient.xml", "runtimes/win/lib/net8.0/System.Data.SqlClient.dll", "runtimes/win/lib/net8.0/System.Data.SqlClient.xml", "system.data.sqlclient.4.9.0.nupkg.sha512", "system.data.sqlclient.nuspec"]}, "System.Diagnostics.DiagnosticSource/8.0.1": {"sha512": "vaoWjvkG1aenR2XdjaVivlCV9fADfgyhW5bZtXT23qaEea0lWiUljdQuze4E31vKM7ZWJaSUsbYIKE3rnzfZUg==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/10.0.0-preview.1.25080.5": {"sha512": "pWFD5tWdZbMQxJWDCtBWRLEjyhCO7n3E8welA15fTIZ1P5DfAaV6XBxKE3g9uOoSTUH7dLp4J8ihRY8gi4QmpA==", "type": "package", "path": "system.diagnostics.eventlog/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net10.0/System.Diagnostics.EventLog.dll", "lib/net10.0/System.Diagnostics.EventLog.xml", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net10.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.10.0.0-preview.1.25080.5.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/10.0.0-preview.1.25080.5": {"sha512": "UazAVHuDDsn0h3Uffa1Ww/0NLnWlTzrJ7TDMjDifhSk5ty/076aWdaBjqahKdmbmhD+U4TMQyMKw3b7E65KhMw==", "type": "package", "path": "system.diagnostics.performancecounter/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net10.0/System.Diagnostics.PerformanceCounter.dll", "lib/net10.0/System.Diagnostics.PerformanceCounter.xml", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/net9.0/System.Diagnostics.PerformanceCounter.dll", "lib/net9.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net10.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net10.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net9.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.10.0.0-preview.1.25080.5.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices/10.0.0-preview.1.25080.5": {"sha512": "D54IesY9PobTDb3ESpUNKwHxMM2c+n1BBuyjM2wbvtPQJZ3wb2PC1tfWqjRB9t56VPvOPK9YSG7efPTJ3xLC0Q==", "type": "package", "path": "system.directoryservices/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.targets", "lib/net10.0/System.DirectoryServices.dll", "lib/net10.0/System.DirectoryServices.xml", "lib/net462/_._", "lib/net8.0/System.DirectoryServices.dll", "lib/net8.0/System.DirectoryServices.xml", "lib/net9.0/System.DirectoryServices.dll", "lib/net9.0/System.DirectoryServices.xml", "lib/netstandard2.0/System.DirectoryServices.dll", "lib/netstandard2.0/System.DirectoryServices.xml", "runtimes/win/lib/net10.0/System.DirectoryServices.dll", "runtimes/win/lib/net10.0/System.DirectoryServices.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.xml", "runtimes/win/lib/net9.0/System.DirectoryServices.dll", "runtimes/win/lib/net9.0/System.DirectoryServices.xml", "system.directoryservices.10.0.0-preview.1.25080.5.nupkg.sha512", "system.directoryservices.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.AccountManagement/10.0.0-preview.1.25080.5": {"sha512": "4BGYDkf6pMC04UKIPWR6fbZGHM8/eO3tIHb0TJWFpH/w5l2M11hmX6gMJgwjMt37czcO3UhU30o3SAywvcGmxw==", "type": "package", "path": "system.directoryservices.accountmanagement/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.AccountManagement.targets", "lib/net10.0/System.DirectoryServices.AccountManagement.dll", "lib/net10.0/System.DirectoryServices.AccountManagement.xml", "lib/net462/_._", "lib/net8.0/System.DirectoryServices.AccountManagement.dll", "lib/net8.0/System.DirectoryServices.AccountManagement.xml", "lib/net9.0/System.DirectoryServices.AccountManagement.dll", "lib/net9.0/System.DirectoryServices.AccountManagement.xml", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.dll", "lib/netstandard2.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net10.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net10.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.AccountManagement.xml", "runtimes/win/lib/net9.0/System.DirectoryServices.AccountManagement.dll", "runtimes/win/lib/net9.0/System.DirectoryServices.AccountManagement.xml", "system.directoryservices.accountmanagement.10.0.0-preview.1.25080.5.nupkg.sha512", "system.directoryservices.accountmanagement.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices.Protocols/10.0.0-preview.1.25080.5": {"sha512": "SFF0UV+Kqy56mM5VkzsD/N9eSOb0GzxxUqfEeeDIKw0mVgEbCzEP9iWORUPcGggreTDE3xzeAhNcXzKN3Eii0w==", "type": "package", "path": "system.directoryservices.protocols/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.Protocols.targets", "lib/net10.0/System.DirectoryServices.Protocols.dll", "lib/net10.0/System.DirectoryServices.Protocols.xml", "lib/net462/_._", "lib/net8.0/System.DirectoryServices.Protocols.dll", "lib/net8.0/System.DirectoryServices.Protocols.xml", "lib/net9.0/System.DirectoryServices.Protocols.dll", "lib/net9.0/System.DirectoryServices.Protocols.xml", "lib/netstandard2.0/System.DirectoryServices.Protocols.dll", "lib/netstandard2.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net10.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net10.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/linux/lib/net9.0/System.DirectoryServices.Protocols.dll", "runtimes/linux/lib/net9.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net10.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net10.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/osx/lib/net9.0/System.DirectoryServices.Protocols.dll", "runtimes/osx/lib/net9.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net10.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net10.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.Protocols.xml", "runtimes/win/lib/net9.0/System.DirectoryServices.Protocols.dll", "runtimes/win/lib/net9.0/System.DirectoryServices.Protocols.xml", "system.directoryservices.protocols.10.0.0-preview.1.25080.5.nupkg.sha512", "system.directoryservices.protocols.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/10.0.0-preview.1.25080.3": {"sha512": "YVKpms8SaTmHucvMZ8IovsF4cQEyUOF9fhDY2kPrs/NIYkrldGa0WYlZmVcMq8h0/35hubNl66dqWqZOLJcB4g==", "type": "package", "path": "system.drawing.common/10.0.0-preview.1.25080.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Drawing.Common.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Drawing.Common.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net10.0/System.Drawing.Common.dll", "lib/net10.0/System.Drawing.Common.pdb", "lib/net10.0/System.Drawing.Common.xml", "lib/net10.0/System.Private.Windows.Core.dll", "lib/net10.0/System.Private.Windows.Core.xml", "lib/net10.0/System.Private.Windows.GdiPlus.dll", "lib/net10.0/System.Private.Windows.GdiPlus.xml", "lib/net462/System.Drawing.Common.dll", "lib/net462/System.Drawing.Common.pdb", "lib/net462/System.Drawing.Common.xml", "lib/net8.0/System.Drawing.Common.dll", "lib/net8.0/System.Drawing.Common.pdb", "lib/net8.0/System.Drawing.Common.xml", "lib/net8.0/System.Private.Windows.Core.dll", "lib/net8.0/System.Private.Windows.Core.xml", "lib/net8.0/System.Private.Windows.GdiPlus.dll", "lib/net8.0/System.Private.Windows.GdiPlus.xml", "lib/net9.0/System.Drawing.Common.dll", "lib/net9.0/System.Drawing.Common.pdb", "lib/net9.0/System.Drawing.Common.xml", "lib/net9.0/System.Private.Windows.Core.dll", "lib/net9.0/System.Private.Windows.Core.xml", "lib/net9.0/System.Private.Windows.GdiPlus.dll", "lib/net9.0/System.Private.Windows.GdiPlus.xml", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.pdb", "lib/netstandard2.0/System.Drawing.Common.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.drawing.common.10.0.0-preview.1.25080.3.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/10.0.0-preview.1.25080.5": {"sha512": "WEJ2jW5pM9+Z562jLL8hjUrdfte+LDNx0jd1DjkisPlqZ4QMBVVnpivcyCB5bENPGiz5N5erTay0QvV9mrw3ng==", "type": "package", "path": "system.formats.asn1/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net10.0/System.Formats.Asn1.dll", "lib/net10.0/System.Formats.Asn1.xml", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/net9.0/System.Formats.Asn1.dll", "lib/net9.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.10.0.0-preview.1.25080.5.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/7.5.0": {"sha512": "D0TtrWOfoPdyYSlvOGaU9F1QR+qrbgJ/4eiEsQkIz7YQKIKkGXQldXukn6cYG9OahSq5UVMvyAIObECpH6Wglg==", "type": "package", "path": "system.identitymodel.tokens.jwt/7.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.FileSystem.AccessControl/5.0.0": {"sha512": "SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "type": "package", "path": "system.io.filesystem.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.dll", "lib/net461/System.IO.FileSystem.AccessControl.xml", "lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "ref/net46/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.dll", "ref/net461/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "ref/netstandard1.3/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/de/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/es/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/it/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.AccessControl.xml", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "ref/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/net46/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/net461/System.IO.FileSystem.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.IO.FileSystem.AccessControl.xml", "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "system.io.filesystem.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Packaging/10.0.0-preview.1.25080.5": {"sha512": "zhBfwTWmXGAl57+JrCl+suG+b08zTX9rIUkh4ElHUNIItfu8qZXZMzuQXQHEKUPCVUHktXBpzEH38hsDOIIQ+w==", "type": "package", "path": "system.io.packaging/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Packaging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Packaging.targets", "lib/net10.0/System.IO.Packaging.dll", "lib/net10.0/System.IO.Packaging.xml", "lib/net462/System.IO.Packaging.dll", "lib/net462/System.IO.Packaging.xml", "lib/net8.0/System.IO.Packaging.dll", "lib/net8.0/System.IO.Packaging.xml", "lib/net9.0/System.IO.Packaging.dll", "lib/net9.0/System.IO.Packaging.xml", "lib/netstandard2.0/System.IO.Packaging.dll", "lib/netstandard2.0/System.IO.Packaging.xml", "system.io.packaging.10.0.0-preview.1.25080.5.nupkg.sha512", "system.io.packaging.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Pipes.AccessControl/5.0.0": {"sha512": "P0FIsXSFNL1AXlHO9zpJ9atRUzVyoPZCkcbkYGZfXXMx9xlGA2H3HOGBwIhpKhB+h0eL3hry/z0UcfJZ+yb2kQ==", "type": "package", "path": "system.io.pipes.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.IO.Pipes.AccessControl.dll", "lib/net461/System.IO.Pipes.AccessControl.dll", "lib/net5.0/System.IO.Pipes.AccessControl.dll", "lib/net5.0/System.IO.Pipes.AccessControl.xml", "lib/netstandard1.3/System.IO.Pipes.AccessControl.dll", "lib/netstandard2.0/System.IO.Pipes.AccessControl.dll", "ref/net46/System.IO.Pipes.AccessControl.dll", "ref/net461/System.IO.Pipes.AccessControl.dll", "ref/net461/System.IO.Pipes.AccessControl.xml", "ref/net5.0/System.IO.Pipes.AccessControl.dll", "ref/net5.0/System.IO.Pipes.AccessControl.xml", "ref/netstandard1.3/System.IO.Pipes.AccessControl.dll", "ref/netstandard2.0/System.IO.Pipes.AccessControl.dll", "ref/netstandard2.0/System.IO.Pipes.AccessControl.xml", "runtimes/win/lib/net46/System.IO.Pipes.AccessControl.dll", "runtimes/win/lib/net461/System.IO.Pipes.AccessControl.dll", "runtimes/win/lib/net5.0/System.IO.Pipes.AccessControl.dll", "runtimes/win/lib/net5.0/System.IO.Pipes.AccessControl.xml", "runtimes/win/lib/netcoreapp2.1/System.IO.Pipes.AccessControl.dll", "system.io.pipes.accesscontrol.5.0.0.nupkg.sha512", "system.io.pipes.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Ports/10.0.0-preview.1.25080.5": {"sha512": "zFyJKFaKZtr2zmBMnMT12VLgGBZ7YWF39RsQjz8w8UqLGJA4QnfnrcaFwqG15erzTzVwvD78jKAENH4EH4caKA==", "type": "package", "path": "system.io.ports/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net10.0/System.IO.Ports.dll", "lib/net10.0/System.IO.Ports.xml", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net8.0/System.IO.Ports.dll", "lib/net8.0/System.IO.Ports.xml", "lib/net9.0/System.IO.Ports.dll", "lib/net9.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net10.0/System.IO.Ports.dll", "runtimes/unix/lib/net10.0/System.IO.Ports.xml", "runtimes/unix/lib/net8.0/System.IO.Ports.dll", "runtimes/unix/lib/net8.0/System.IO.Ports.xml", "runtimes/unix/lib/net9.0/System.IO.Ports.dll", "runtimes/unix/lib/net9.0/System.IO.Ports.xml", "runtimes/win/lib/net10.0/System.IO.Ports.dll", "runtimes/win/lib/net10.0/System.IO.Ports.xml", "runtimes/win/lib/net8.0/System.IO.Ports.dll", "runtimes/win/lib/net8.0/System.IO.Ports.xml", "runtimes/win/lib/net9.0/System.IO.Ports.dll", "runtimes/win/lib/net9.0/System.IO.Ports.xml", "system.io.ports.10.0.0-preview.1.25080.5.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/10.0.0-preview.1.25080.5": {"sha512": "woNcGYbbcFx8VZBhtOr26stFG2Xk0i1QwgZYnItc0AcnuvPDpt4Vd7zTh4ZEFh+mnW7kFARxMoAikAe6QqNzvw==", "type": "package", "path": "system.management/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net10.0/System.Management.dll", "lib/net10.0/System.Management.xml", "lib/net462/_._", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/net9.0/System.Management.dll", "lib/net9.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net10.0/System.Management.dll", "runtimes/win/lib/net10.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "runtimes/win/lib/net9.0/System.Management.dll", "runtimes/win/lib/net9.0/System.Management.xml", "system.management.10.0.0-preview.1.25080.5.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.6.0": {"sha512": "OEkbBQoklHngJ8UD8ez2AERSk2g+/qpAaSWWCBFbpH727HxDq5ydVkuncBaKcKfwRqXGWx64dS6G1SUScMsitg==", "type": "package", "path": "system.memory/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Memory.targets", "buildTransitive/net462/_._", "lib/net462/System.Memory.dll", "lib/net462/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "system.memory.4.6.0.nupkg.sha512", "system.memory.nuspec"]}, "System.Memory.Data/1.0.2": {"sha512": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "type": "package", "path": "system.memory.data/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.1.0.2.nupkg.sha512", "system.memory.data.nuspec"]}, "System.Numerics.Vectors/4.6.0": {"sha512": "t+SoieZsRuEyiw/J+qXUbolyO219tKQQI0+2/YI+Qv7YdGValA6WiuokrNKqjrTNsy5ABWU11bdKOzUdheteXg==", "type": "package", "path": "system.numerics.vectors/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Numerics.Vectors.targets", "buildTransitive/net462/_._", "lib/net462/System.Numerics.Vectors.dll", "lib/net462/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "system.numerics.vectors.4.6.0.nupkg.sha512", "system.numerics.vectors.nuspec"]}, "System.Reflection.Context/10.0.0-preview.1.25080.5": {"sha512": "5u0IaGwaC1hSVi8dDts2PB1RQx+BbJMg6GLEFDvGDHVaV/N5zxbJQyvwNqUgK4Yw8IuuLngCzBzDWyL0C5Gd0A==", "type": "package", "path": "system.reflection.context/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Reflection.Context.targets", "lib/net10.0/System.Reflection.Context.dll", "lib/net10.0/System.Reflection.Context.xml", "lib/net462/_._", "lib/net8.0/System.Reflection.Context.dll", "lib/net8.0/System.Reflection.Context.xml", "lib/net9.0/System.Reflection.Context.dll", "lib/net9.0/System.Reflection.Context.xml", "lib/netstandard2.0/System.Reflection.Context.dll", "lib/netstandard2.0/System.Reflection.Context.xml", "lib/netstandard2.1/System.Reflection.Context.dll", "lib/netstandard2.1/System.Reflection.Context.xml", "system.reflection.context.10.0.0-preview.1.25080.5.nupkg.sha512", "system.reflection.context.nuspec", "useSharedDesignerContext.txt"]}, "System.Reflection.Emit/4.7.0": {"sha512": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "type": "package", "path": "system.reflection.emit/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Reflection.Emit.dll", "lib/netstandard1.1/System.Reflection.Emit.xml", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.dll", "lib/netstandard2.0/System.Reflection.Emit.xml", "lib/netstandard2.1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/netstandard2.0/System.Reflection.Emit.dll", "ref/netstandard2.0/System.Reflection.Emit.xml", "ref/netstandard2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.xml", "system.reflection.emit.4.7.0.nupkg.sha512", "system.reflection.emit.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.ILGeneration/4.7.0": {"sha512": "AucBYo3DSI0IDxdUjKksBcQJXPHyoPyrCXYURW1WDsLI4M65Ar/goSHjdnHOAY9MiYDNKqDlIgaYm+zL2hA1KA==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard2.0/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard2.0/System.Reflection.Emit.ILGeneration.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard2.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard2.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.ILGeneration.xml", "system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Emit.Lightweight/4.7.0": {"sha512": "a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA==", "type": "package", "path": "system.reflection.emit.lightweight/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "lib/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "lib/netstandard2.1/_._", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard2.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard2.1/_._", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.dll", "runtimes/aot/lib/netcore50/System.Reflection.Emit.Lightweight.xml", "system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Caching/10.0.0-preview.1.25080.5": {"sha512": "2fvWa5VRn3sleQpkyzaShGq5f/2fbGnE4Xxrb7msPydgPqaTMuXVIkRO6anjinnnlX42kDEIaxyc8O8lZh3S0A==", "type": "package", "path": "system.runtime.caching/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.Caching.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net10.0/System.Runtime.Caching.dll", "lib/net10.0/System.Runtime.Caching.xml", "lib/net462/_._", "lib/net8.0/System.Runtime.Caching.dll", "lib/net8.0/System.Runtime.Caching.xml", "lib/net9.0/System.Runtime.Caching.dll", "lib/net9.0/System.Runtime.Caching.xml", "lib/netstandard2.0/System.Runtime.Caching.dll", "lib/netstandard2.0/System.Runtime.Caching.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net10.0/System.Runtime.Caching.dll", "runtimes/win/lib/net10.0/System.Runtime.Caching.xml", "runtimes/win/lib/net8.0/System.Runtime.Caching.dll", "runtimes/win/lib/net8.0/System.Runtime.Caching.xml", "runtimes/win/lib/net9.0/System.Runtime.Caching.dll", "runtimes/win/lib/net9.0/System.Runtime.Caching.xml", "system.runtime.caching.10.0.0-preview.1.25080.5.nupkg.sha512", "system.runtime.caching.nuspec", "useSharedDesignerContext.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.1.0": {"sha512": "5o/HZxx6RVqYlhKSq8/zronDkALJZUT2Vz0hx43f0gwe8mwlM0y2nYlqdBwLMzr262Bwvpikeb/yEwkAa5PADg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "lib/net462/System.Runtime.CompilerServices.Unsafe.dll", "lib/net462/System.Runtime.CompilerServices.Unsafe.xml", "lib/net7.0/_._", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec"]}, "System.Runtime.WindowsRuntime/4.6.0": {"sha512": "IWrs1TmbxP65ZZjIglNyvDkFNoV5q2Pofg5WO7I8RKQOpLdFprQSh3xesOoClBqR4JHr4nEB1Xk1MqLPW1jPuQ==", "type": "package", "path": "system.runtime.windowsruntime/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.targets", "build/net451/System.Runtime.WindowsRuntime.targets", "build/net461/System.Runtime.WindowsRuntime.targets", "buildTransitive/net45/System.Runtime.WindowsRuntime.targets", "buildTransitive/net451/System.Runtime.WindowsRuntime.targets", "buildTransitive/net461/System.Runtime.WindowsRuntime.targets", "lib/net45/_._", "lib/netstandard1.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.0/System.Runtime.WindowsRuntime.xml", "lib/netstandard1.2/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.2/System.Runtime.WindowsRuntime.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.xml", "lib/portable-win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.dll", "ref/netcore50/System.Runtime.WindowsRuntime.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.0/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.2/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.xml", "ref/portable-win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"sha512": "r4tNw5v5kqRJ9HikWpcyNf3suGw7DjX93svj9iBjtdeLqL8jt9Z+7f+s4wrKZJr84u8IMsrIjt8K6jYvkRqMSg==", "type": "package", "path": "system.runtime.windowsruntime.ui.xaml/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets", "build/net461/System.Runtime.WindowsRuntime.UI.Xaml.targets", "lib/net45/_._", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/portable-win8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/portable-win8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.ui.xaml.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.ui.xaml.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Algorithms/4.3.1": {"sha512": "DVUblnRfnarrI5olEC2B/OCsJQd0anjVaObQMndHSc43efbc88/RMOlDyg/EyY0ix5ecyZMXS8zMksb5ukebZA==", "type": "package", "path": "system.security.cryptography.algorithms/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.3.1.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Cng/5.0.0": {"sha512": "jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "type": "package", "path": "system.security.cryptography.cng/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.xml", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.xml", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.xml", "lib/netstandard2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard2.1/System.Security.Cryptography.Cng.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/netstandard2.1/System.Security.Cryptography.Cng.dll", "ref/netstandard2.1/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.5.0.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Encoding/4.3.0": {"sha512": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "type": "package", "path": "system.security.cryptography.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.3.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.Pkcs/10.0.0-preview.1.25080.5": {"sha512": "1lKoT1eJA5uWiwzTPa7lTNYFfATHnw2IlbcJ7Zh6BJOQeZB1CArvQkJe/5dOMrdsL8fjiXwXWjaRqbBZK+ZQFQ==", "type": "package", "path": "system.security.cryptography.pkcs/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net10.0/System.Security.Cryptography.Pkcs.dll", "lib/net10.0/System.Security.Cryptography.Pkcs.xml", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/net9.0/System.Security.Cryptography.Pkcs.dll", "lib/net9.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net10.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net10.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.10.0.0-preview.1.25080.5.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Primitives/4.3.0": {"sha512": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "type": "package", "path": "system.security.cryptography.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.3.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Security.Cryptography.ProtectedData/10.0.0-preview.1.25080.5": {"sha512": "3PKCdPA51DgDsSwjwyLUpQQrjqkAz0Z1cLrm46dcDoXSYyF2k5XEcQn8Xj4gMl2Qu8+f+1aDc4J7HXBuLvjIAg==", "type": "package", "path": "system.security.cryptography.protecteddata/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net10.0/System.Security.Cryptography.ProtectedData.dll", "lib/net10.0/System.Security.Cryptography.ProtectedData.xml", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/net9.0/System.Security.Cryptography.ProtectedData.dll", "lib/net9.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.10.0.0-preview.1.25080.5.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/10.0.0-preview.1.25080.5": {"sha512": "79GUiR0wmxsD9LU0nk2PIARsugt86aNYptQHzKES18bcrjEf1FCSATn9ScSGWPPDlAXeWkX4kXq3CkDwyPOC2w==", "type": "package", "path": "system.security.cryptography.xml/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Xml.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "lib/net10.0/System.Security.Cryptography.Xml.dll", "lib/net10.0/System.Security.Cryptography.Xml.xml", "lib/net462/System.Security.Cryptography.Xml.dll", "lib/net462/System.Security.Cryptography.Xml.xml", "lib/net8.0/System.Security.Cryptography.Xml.dll", "lib/net8.0/System.Security.Cryptography.Xml.xml", "lib/net9.0/System.Security.Cryptography.Xml.dll", "lib/net9.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.10.0.0-preview.1.25080.5.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/10.0.0-preview.1.25080.5": {"sha512": "XnJtZB8q2qjBVVG2MTd/EXLM4yNpdNa1bW7r0E/WftaG6Gq2/+ueu/sJ46oBJ2RMdkZ8Ir7UH/THIRzMsKAgwA==", "type": "package", "path": "system.security.permissions/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Permissions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "lib/net10.0/System.Security.Permissions.dll", "lib/net10.0/System.Security.Permissions.xml", "lib/net462/System.Security.Permissions.dll", "lib/net462/System.Security.Permissions.xml", "lib/net8.0/System.Security.Permissions.dll", "lib/net8.0/System.Security.Permissions.xml", "lib/net9.0/System.Security.Permissions.dll", "lib/net9.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.10.0.0-preview.1.25080.5.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceModel.Duplex/4.10.0": {"sha512": "4TiHY9zNCyU5++0hzgQQY8Lg2iUxBndRbo/xVWxljqekBiPSK037QASLD4ZZCKc/JcA4cpHUFDXZjzrdVVn6aw==", "type": "package", "path": "system.servicemodel.duplex/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Duplex.dll", "lib/net461/System.ServiceModel.Duplex.pdb", "lib/net6.0/System.ServiceModel.Duplex.dll", "lib/net6.0/System.ServiceModel.Duplex.pdb", "lib/netcore50/System.ServiceModel.Duplex.dll", "lib/netstandard1.3/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.dll", "lib/netstandard2.0/System.ServiceModel.Duplex.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Duplex.dll", "ref/net6.0/System.ServiceModel.Duplex.dll", "ref/netcore50/System.ServiceModel.Duplex.dll", "ref/netstandard1.1/System.ServiceModel.Duplex.dll", "ref/netstandard2.0/System.ServiceModel.Duplex.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.duplex.4.10.0.nupkg.sha512", "system.servicemodel.duplex.nuspec"]}, "System.ServiceModel.Http/4.10.0": {"sha512": "/PbmNSEwTQ7Vizor3F/Zp8bzR6L9YZNGIwGr1Tyc//ZZuAYDhiwiMbNpX3EnPZM63qD2bJmR/FWH9S5Ffp8K6g==", "type": "package", "path": "system.servicemodel.http/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.dll", "lib/net461/System.ServiceModel.Http.pdb", "lib/net6.0/System.ServiceModel.Http.dll", "lib/net6.0/System.ServiceModel.Http.pdb", "lib/netcore50/System.ServiceModel.Http.dll", "lib/netstandard1.3/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.dll", "lib/netstandard2.0/System.ServiceModel.Http.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Http.dll", "ref/net461/System.ServiceModel.Http.dll", "ref/net6.0/System.ServiceModel.Http.dll", "ref/netcore50/System.ServiceModel.Http.dll", "ref/netstandard1.0/System.ServiceModel.Http.dll", "ref/netstandard1.1/System.ServiceModel.Http.dll", "ref/netstandard1.3/System.ServiceModel.Http.dll", "ref/netstandard2.0/System.ServiceModel.Http.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.http.4.10.0.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.NetTcp/4.10.0": {"sha512": "tG69H0sRdzEuOcdGzsZwbmPk54Akb3t1Db4SSXN6hSTOc2ZBFu1jLt5wJA6ATbIjJ5WqXA8beRNLhO77lBNIdA==", "type": "package", "path": "system.servicemodel.nettcp/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.dll", "lib/net461/System.ServiceModel.NetTcp.pdb", "lib/net6.0/System.ServiceModel.NetTcp.dll", "lib/net6.0/System.ServiceModel.NetTcp.pdb", "lib/netcore50/System.ServiceModel.NetTcp.dll", "lib/netstandard1.3/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.dll", "lib/netstandard2.0/System.ServiceModel.NetTcp.pdb", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.NetTcp.dll", "ref/net461/System.ServiceModel.NetTcp.dll", "ref/net6.0/System.ServiceModel.NetTcp.dll", "ref/netcore50/System.ServiceModel.NetTcp.dll", "ref/netstandard1.1/System.ServiceModel.NetTcp.dll", "ref/netstandard1.3/System.ServiceModel.NetTcp.dll", "ref/netstandard2.0/System.ServiceModel.NetTcp.dll", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.nettcp.4.10.0.nupkg.sha512", "system.servicemodel.nettcp.nuspec"]}, "System.ServiceModel.Primitives/4.10.0": {"sha512": "BtrvvpgU2HolcC0tUf1g+n4Fk5kLhfbIBgRibcGe7TDHXcy6zTfkyXxR88rl2tO4KEPLkJXxWf/HW/LJmsI0Ew==", "type": "package", "path": "system.servicemodel.primitives/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.dll", "lib/net461/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.Primitives.dll", "lib/net6.0/System.ServiceModel.Primitives.pdb", "lib/net6.0/System.ServiceModel.dll", "lib/netcore50/System.ServiceModel.Primitives.dll", "lib/netcoreapp3.1/System.ServiceModel.Primitives.dll", "lib/netcoreapp3.1/System.ServiceModel.Primitives.pdb", "lib/netcoreapp3.1/System.ServiceModel.dll", "lib/netstandard1.3/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.dll", "lib/netstandard2.0/System.ServiceModel.Primitives.pdb", "lib/netstandard2.0/System.ServiceModel.dll", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.ServiceModel.Primitives.dll", "ref/net461/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.Primitives.dll", "ref/net6.0/System.ServiceModel.dll", "ref/netcore50/System.ServiceModel.Primitives.dll", "ref/netcoreapp3.1/System.ServiceModel.Primitives.dll", "ref/netcoreapp3.1/System.ServiceModel.dll", "ref/netstandard1.0/System.ServiceModel.Primitives.dll", "ref/netstandard1.1/System.ServiceModel.Primitives.dll", "ref/netstandard1.3/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.Primitives.dll", "ref/netstandard2.0/System.ServiceModel.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.primitives.4.10.0.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}, "System.ServiceModel.Security/4.10.0": {"sha512": "/COEfB7QqKW37DOfmzJG6rd0apH0uMMCYNDUir9ZVDQR/ulQHx12T/5jMTo25YgUUk++i0SfGDbzutMH3w/nQg==", "type": "package", "path": "system.servicemodel.security/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ServiceModel.Security.dll", "lib/net461/System.ServiceModel.Security.pdb", "lib/net6.0/System.ServiceModel.Security.dll", "lib/net6.0/System.ServiceModel.Security.pdb", "lib/netcore50/System.ServiceModel.Security.dll", "lib/netstandard1.3/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.dll", "lib/netstandard2.0/System.ServiceModel.Security.pdb", "lib/portable-net45+win8+wp8/_._", "lib/win8/_._", "lib/wp8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ServiceModel.Security.dll", "ref/net6.0/System.ServiceModel.Security.dll", "ref/netcore50/System.ServiceModel.Security.dll", "ref/netstandard1.0/System.ServiceModel.Security.dll", "ref/netstandard1.1/System.ServiceModel.Security.dll", "ref/netstandard2.0/System.ServiceModel.Security.dll", "ref/portable-net45+win8+wp8/_._", "ref/win8/_._", "ref/wp8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.servicemodel.security.4.10.0.nupkg.sha512", "system.servicemodel.security.nuspec"]}, "System.ServiceModel.Syndication/10.0.0-preview.1.25080.5": {"sha512": "ctl1VT9Fw73NxYWmLcZSiNfDvHt8cwpPPeboeQ3/t9J1FnJ4UBlpzq19JWczdnatpEINrn4g+lb08cKvsQCgaA==", "type": "package", "path": "system.servicemodel.syndication/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.ServiceModel.Syndication.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.ServiceModel.Syndication.targets", "lib/net10.0/System.ServiceModel.Syndication.dll", "lib/net10.0/System.ServiceModel.Syndication.xml", "lib/net462/System.ServiceModel.Syndication.dll", "lib/net462/System.ServiceModel.Syndication.xml", "lib/net8.0/System.ServiceModel.Syndication.dll", "lib/net8.0/System.ServiceModel.Syndication.xml", "lib/net9.0/System.ServiceModel.Syndication.dll", "lib/net9.0/System.ServiceModel.Syndication.xml", "lib/netstandard2.0/System.ServiceModel.Syndication.dll", "lib/netstandard2.0/System.ServiceModel.Syndication.xml", "system.servicemodel.syndication.10.0.0-preview.1.25080.5.nupkg.sha512", "system.servicemodel.syndication.nuspec", "useSharedDesignerContext.txt"]}, "System.ServiceProcess.ServiceController/10.0.0-preview.1.25080.5": {"sha512": "FQEiq4Bit8Aev3U9hzUjYq+QH+muoBqO1G4aQ6O8fxSz1m9OZAhvGWcj5tck7+lLOfRx4BXa3rbNhx949EMB6w==", "type": "package", "path": "system.serviceprocess.servicecontroller/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.ServiceProcess.ServiceController.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.ServiceProcess.ServiceController.targets", "lib/net10.0/System.ServiceProcess.ServiceController.dll", "lib/net10.0/System.ServiceProcess.ServiceController.xml", "lib/net462/System.ServiceProcess.ServiceController.dll", "lib/net462/System.ServiceProcess.ServiceController.xml", "lib/net8.0/System.ServiceProcess.ServiceController.dll", "lib/net8.0/System.ServiceProcess.ServiceController.xml", "lib/net9.0/System.ServiceProcess.ServiceController.dll", "lib/net9.0/System.ServiceProcess.ServiceController.xml", "lib/netstandard2.0/System.ServiceProcess.ServiceController.dll", "lib/netstandard2.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net10.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net10.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net8.0/System.ServiceProcess.ServiceController.xml", "runtimes/win/lib/net9.0/System.ServiceProcess.ServiceController.dll", "runtimes/win/lib/net9.0/System.ServiceProcess.ServiceController.xml", "system.serviceprocess.servicecontroller.10.0.0-preview.1.25080.5.nupkg.sha512", "system.serviceprocess.servicecontroller.nuspec", "useSharedDesignerContext.txt"]}, "System.Speech/10.0.0-preview.1.25080.5": {"sha512": "HQWxlM1axxsZiez42H9kzD1MSA/svATNjyoZZ9gBsrUaLOqlb6xwOVIzNx6p6QYsngNmf/+UYDyWZk0ivhNg+Q==", "type": "package", "path": "system.speech/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Speech.targets", "lib/net10.0/System.Speech.dll", "lib/net10.0/System.Speech.xml", "lib/net462/_._", "lib/net8.0/System.Speech.dll", "lib/net8.0/System.Speech.xml", "lib/net9.0/System.Speech.dll", "lib/net9.0/System.Speech.xml", "lib/netstandard2.0/System.Speech.dll", "lib/netstandard2.0/System.Speech.xml", "runtimes/win/lib/net10.0/System.Speech.dll", "runtimes/win/lib/net10.0/System.Speech.xml", "runtimes/win/lib/net8.0/System.Speech.dll", "runtimes/win/lib/net8.0/System.Speech.xml", "runtimes/win/lib/net9.0/System.Speech.dll", "runtimes/win/lib/net9.0/System.Speech.xml", "system.speech.10.0.0-preview.1.25080.5.nupkg.sha512", "system.speech.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/10.0.0-preview.1.25080.5": {"sha512": "MrVHjfa1+qXO/ZKMoM+msk8miOoyZr/k4sVe1YNcLny4A5hCf96Apk6cT9QF10IeRWzbZlEGvCMv6c7oLJJb1g==", "type": "package", "path": "system.text.encoding.codepages/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encoding.CodePages.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net10.0/System.Text.Encoding.CodePages.dll", "lib/net10.0/System.Text.Encoding.CodePages.xml", "lib/net462/System.Text.Encoding.CodePages.dll", "lib/net462/System.Text.Encoding.CodePages.xml", "lib/net8.0/System.Text.Encoding.CodePages.dll", "lib/net8.0/System.Text.Encoding.CodePages.xml", "lib/net9.0/System.Text.Encoding.CodePages.dll", "lib/net9.0/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net10.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net10.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net9.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net9.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.10.0.0-preview.1.25080.5.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/6.0.0": {"sha512": "Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "type": "package", "path": "system.text.encodings.web/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/netcoreapp3.1/System.Text.Encodings.Web.dll", "lib/netcoreapp3.1/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.6.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/6.0.10": {"sha512": "NSB0kDipxn2ychp88NXWfFRFlmi1bst/xynOutbnpEfRCT9JZkZ7KOmF/I/hNKo2dILiMGnqblm+j1sggdLB9g==", "type": "package", "path": "system.text.json/6.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netcoreapp3.1/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net461/System.Text.Json.dll", "lib/net461/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/netcoreapp3.1/System.Text.Json.dll", "lib/netcoreapp3.1/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.6.0.10.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.AccessControl/10.0.0-preview.1.25080.5": {"sha512": "5h+Vnlv74C6Y4wAsl3zm9FlOci2vGViWlFgx+nwLYFIM7RUyoTeZ8TgMEcre/gNvW1rprdnCVpjLTtsBuJWJ4g==", "type": "package", "path": "system.threading.accesscontrol/10.0.0-preview.1.25080.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Threading.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Threading.AccessControl.targets", "lib/net10.0/System.Threading.AccessControl.dll", "lib/net10.0/System.Threading.AccessControl.xml", "lib/net462/System.Threading.AccessControl.dll", "lib/net462/System.Threading.AccessControl.xml", "lib/net8.0/System.Threading.AccessControl.dll", "lib/net8.0/System.Threading.AccessControl.xml", "lib/net9.0/System.Threading.AccessControl.dll", "lib/net9.0/System.Threading.AccessControl.xml", "lib/netstandard2.0/System.Threading.AccessControl.dll", "lib/netstandard2.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net10.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net10.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net8.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net8.0/System.Threading.AccessControl.xml", "runtimes/win/lib/net9.0/System.Threading.AccessControl.dll", "runtimes/win/lib/net9.0/System.Threading.AccessControl.xml", "system.threading.accesscontrol.10.0.0-preview.1.25080.5.nupkg.sha512", "system.threading.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Web.Services.Description/4.10.0": {"sha512": "Dwr64geRujAwnI+wPMJP1rf4pFaYRITrAS7EIGd0GVMwQ8OayM6ypwmnAPzQG4YTyN84w6KD5Rv8LJywYK+vUA==", "type": "package", "path": "system.web.services.description/4.10.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Web.Services.Description.dll", "lib/net461/System.Web.Services.Description.pdb", "lib/netstandard2.0/System.Web.Services.Description.dll", "lib/netstandard2.0/System.Web.Services.Description.pdb", "lib/netstandard2.0/cs/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/de/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/es/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/fr/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/it/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ja/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ko/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/pl/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/pt-BR/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/ru/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/tr/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/zh-Hans/System.Web.Services.Description.resources.dll", "lib/netstandard2.0/zh-Hant/System.Web.Services.Description.resources.dll", "system.web.services.description.4.10.0.nupkg.sha512", "system.web.services.description.nuspec"]}, "WindowsAPICodePack-Core/1.1.1": {"sha512": "GoURoQuE7ea7B7q24YRTBfZbsNO7ZtwSyBHbM8zO0ynwfjAGC0bbAmVrDvZ6HbWtqqOt6QTzdmYnj2WJglb5JQ==", "type": "package", "path": "windowsapicodepack-core/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/Microsoft.WindowsAPICodePack.dll", "lib/Microsoft.WindowsAPICodePack.xml", "windowsapicodepack-core.1.1.1.nupkg.sha512", "windowsapicodepack-core.nuspec"]}, "WindowsAPICodePack-Shell/1.1.1": {"sha512": "CPFamhL3jPo8O6+ErGsLS4LK3DWP5ncxqhbzpPnYkIW9rjatunUij35iTjprbr9mlYR/5Pbguw21MwSmTOKR4g==", "type": "package", "path": "windowsapicodepack-shell/1.1.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/Microsoft.WindowsAPICodePack.Shell.dll", "lib/Microsoft.WindowsAPICodePack.Shell.xml", "windowsapicodepack-shell.1.1.1.nupkg.sha512", "windowsapicodepack-shell.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["ADGV >= ********", "ClosedXML >= 0.104.2", "CrystalReports.Engine >= 13.0.4003", "DataGridView-AutoFilter >= 1.1.0", "ExcelDataReader >= 3.7.0", "ExcelDataReader.DataSet >= 3.7.0", "MaterialSkin.2 >= 2.3.1", "MetroModernUI >= 1.4.0", "Microsoft.Data.SqlClient >= 6.0.1", "Microsoft.Office.Interop.Excel >= 15.0.4795.1001", "Microsoft.Office.Interop.Word >= 15.0.4797.1004", "Microsoft.SqlServer.SqlManagementObjects >= 172.64.0", "Microsoft.Toolkit.Uwp.Notifications >= 7.1.3", "Microsoft.Windows.Compatibility >= 10.0.0-preview.1.25080.4", "System.Data.OleDb >= 10.0.0-preview.1.25080.5", "System.Data.SqlClient >= 4.9.0", "WindowsAPICodePack-Shell >= 1.1.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj", "projectName": "El Dawliya International System", "projectPath": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"ADGV": {"target": "Package", "version": "[********, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.2, )"}, "CrystalReports.Engine": {"target": "Package", "version": "[13.0.4003, )"}, "DataGridView-AutoFilter": {"target": "Package", "version": "[1.1.0, )"}, "ExcelDataReader": {"target": "Package", "version": "[3.7.0, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.7.0, )"}, "MaterialSkin.2": {"target": "Package", "version": "[2.3.1, )"}, "MetroModernUI": {"target": "Package", "version": "[1.4.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Office.Interop.Excel": {"target": "Package", "version": "[15.0.4795.1001, )"}, "Microsoft.Office.Interop.Word": {"target": "Package", "version": "[15.0.4797.1004, )"}, "Microsoft.SqlServer.SqlManagementObjects": {"target": "Package", "version": "[172.64.0, )"}, "Microsoft.Toolkit.Uwp.Notifications": {"target": "Package", "version": "[7.1.3, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[10.0.0-preview.1.25080.4, )"}, "System.Data.OleDb": {"target": "Package", "version": "[10.0.0-preview.1.25080.5, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "WindowsAPICodePack-Shell": {"target": "Package", "version": "[1.1.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1900", "level": "Warning", "warningLevel": 1, "message": "Error occurred while getting package vulnerability data: Unable to load the service index for source https://api.nuget.org/v3/index.json."}]}