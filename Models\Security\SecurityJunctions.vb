''' <summary>
''' User-Role assignment entity with time-based access control
''' </summary>
Public Class UserRole
    ''' <summary>
    ''' Unique user-role assignment identifier
    ''' </summary>
    Public Property UserRoleID As Integer

    ''' <summary>
    ''' User identifier
    ''' </summary>
    Public Property UserID As Integer

    ''' <summary>
    ''' Role identifier
    ''' </summary>
    Public Property RoleID As Integer

    ''' <summary>
    ''' Date when role was assigned
    ''' </summary>
    Public Property AssignedDate As DateTime = DateTime.Now

    ''' <summary>
    ''' Date when role assignment expires
    ''' </summary>
    Public Property ExpiryDate As DateTime?

    ''' <summary>
    ''' Whether the assignment is active
    ''' </summary>
    Public Property IsActive As Boolean = True

    ''' <summary>
    ''' ID of user who assigned this role
    ''' </summary>
    Public Property AssignedBy As Integer?

    ''' <summary>
    ''' Navigation property to user
    ''' </summary>
    Public Property User As User

    ''' <summary>
    ''' Navigation property to role
    ''' </summary>
    Public Property Role As Role

    ''' <summary>
    ''' Check if assignment is currently valid
    ''' </summary>
    ''' <returns>True if assignment is valid</returns>
    Public Function IsValid() As Boolean
        Return IsActive AndAlso (Not ExpiryDate.HasValue OrElse ExpiryDate.Value > DateTime.Now)
    End Function

    ''' <summary>
    ''' Check if assignment is expired
    ''' </summary>
    ''' <returns>True if assignment is expired</returns>
    Public Function IsExpired() As Boolean
        Return ExpiryDate.HasValue AndAlso ExpiryDate.Value <= DateTime.Now
    End Function

    ''' <summary>
    ''' Extend assignment expiry date
    ''' </summary>
    ''' <param name="days">Number of days to extend</param>
    Public Sub ExtendExpiry(days As Integer)
        If ExpiryDate.HasValue Then
            ExpiryDate = ExpiryDate.Value.AddDays(days)
        Else
            ExpiryDate = DateTime.Now.AddDays(days)
        End If
    End Sub

    ''' <summary>
    ''' Deactivate assignment
    ''' </summary>
    Public Sub Deactivate()
        IsActive = False
        ExpiryDate = DateTime.Now
    End Sub
End Class

''' <summary>
''' Role-Permission assignment entity
''' </summary>
Public Class RolePermission
    ''' <summary>
    ''' Unique role-permission assignment identifier
    ''' </summary>
    Public Property RolePermissionID As Integer

    ''' <summary>
    ''' Role identifier
    ''' </summary>
    Public Property RoleID As Integer

    ''' <summary>
    ''' Permission identifier
    ''' </summary>
    Public Property PermissionID As Integer

    ''' <summary>
    ''' Date when permission was granted to role
    ''' </summary>
    Public Property GrantedDate As DateTime = DateTime.Now

    ''' <summary>
    ''' ID of user who granted this permission
    ''' </summary>
    Public Property GrantedBy As Integer?

    ''' <summary>
    ''' Navigation property to role
    ''' </summary>
    Public Property Role As Role

    ''' <summary>
    ''' Navigation property to permission
    ''' </summary>
    Public Property Permission As Permission
End Class

''' <summary>
''' User-Permission assignment entity for permission overrides
''' </summary>
Public Class UserPermission
    ''' <summary>
    ''' Unique user-permission assignment identifier
    ''' </summary>
    Public Property UserPermissionID As Integer

    ''' <summary>
    ''' User identifier
    ''' </summary>
    Public Property UserID As Integer

    ''' <summary>
    ''' Permission identifier
    ''' </summary>
    Public Property PermissionID As Integer

    ''' <summary>
    ''' Whether permission is granted (True) or denied (False)
    ''' </summary>
    Public Property IsGranted As Boolean

    ''' <summary>
    ''' Date when permission was granted/denied
    ''' </summary>
    Public Property GrantedDate As DateTime = DateTime.Now

    ''' <summary>
    ''' Date when permission assignment expires
    ''' </summary>
    Public Property ExpiryDate As DateTime?

    ''' <summary>
    ''' ID of user who granted/denied this permission
    ''' </summary>
    Public Property GrantedBy As Integer?

    ''' <summary>
    ''' Reason for permission override
    ''' </summary>
    Public Property Reason As String

    ''' <summary>
    ''' Navigation property to user
    ''' </summary>
    Public Property User As User

    ''' <summary>
    ''' Navigation property to permission
    ''' </summary>
    Public Property Permission As Permission

    ''' <summary>
    ''' Check if permission assignment is currently valid
    ''' </summary>
    ''' <returns>True if assignment is valid</returns>
    Public Function IsValid() As Boolean
        Return Not ExpiryDate.HasValue OrElse ExpiryDate.Value > DateTime.Now
    End Function

    ''' <summary>
    ''' Check if permission assignment is expired
    ''' </summary>
    ''' <returns>True if assignment is expired</returns>
    Public Function IsExpired() As Boolean
        Return ExpiryDate.HasValue AndAlso ExpiryDate.Value <= DateTime.Now
    End Function

    ''' <summary>
    ''' Extend permission expiry date
    ''' </summary>
    ''' <param name="days">Number of days to extend</param>
    Public Sub ExtendExpiry(days As Integer)
        If ExpiryDate.HasValue Then
            ExpiryDate = ExpiryDate.Value.AddDays(days)
        Else
            ExpiryDate = DateTime.Now.AddDays(days)
        End If
    End Sub
End Class

''' <summary>
''' User session entity for tracking active sessions
''' </summary>
Public Class UserSession
    ''' <summary>
    ''' Unique session identifier
    ''' </summary>
    Public Property SessionID As Guid = Guid.NewGuid()

    ''' <summary>
    ''' User identifier
    ''' </summary>
    Public Property UserID As Integer

    ''' <summary>
    ''' Login timestamp
    ''' </summary>
    Public Property LoginTime As DateTime = DateTime.Now

    ''' <summary>
    ''' Logout timestamp
    ''' </summary>
    Public Property LogoutTime As DateTime?

    ''' <summary>
    ''' Client IP address
    ''' </summary>
    Public Property IPAddress As String

    ''' <summary>
    ''' User agent string
    ''' </summary>
    Public Property UserAgent As String

    ''' <summary>
    ''' Device name
    ''' </summary>
    Public Property DeviceName As String

    ''' <summary>
    ''' Whether session is active
    ''' </summary>
    Public Property IsActive As Boolean = True

    ''' <summary>
    ''' Last activity timestamp
    ''' </summary>
    Public Property LastActivityTime As DateTime = DateTime.Now

    ''' <summary>
    ''' Navigation property to user
    ''' </summary>
    Public Property User As User

    ''' <summary>
    ''' Session duration in minutes
    ''' </summary>
    Public ReadOnly Property Duration As Integer
        Get
            Dim endTime As DateTime = If(LogoutTime, DateTime.Now)
            Return CInt((endTime - LoginTime).TotalMinutes)
        End Get
    End Property

    ''' <summary>
    ''' Check if session is expired based on timeout
    ''' </summary>
    ''' <param name="timeoutMinutes">Session timeout in minutes</param>
    ''' <returns>True if session is expired</returns>
    Public Function IsExpired(timeoutMinutes As Integer) As Boolean
        If Not IsActive OrElse LogoutTime.HasValue Then
            Return True
        End If

        Return DateTime.Now.Subtract(LastActivityTime).TotalMinutes > timeoutMinutes
    End Function

    ''' <summary>
    ''' Update last activity time
    ''' </summary>
    Public Sub UpdateActivity()
        LastActivityTime = DateTime.Now
    End Sub

    ''' <summary>
    ''' End session
    ''' </summary>
    Public Sub EndSession()
        LogoutTime = DateTime.Now
        IsActive = False
    End Sub

    ''' <summary>
    ''' Get session info summary
    ''' </summary>
    ''' <returns>Session summary string</returns>
    Public Function GetSummary() As String
        Dim status As String = If(IsActive, "Active", "Ended")
        Dim duration As String = $"{Me.Duration} minutes"
        Return $"{status} - {duration} - {IPAddress}"
    End Function
End Class

''' <summary>
''' Password history entity for password policy enforcement
''' </summary>
Public Class PasswordHistory
    ''' <summary>
    ''' Unique password history identifier
    ''' </summary>
    Public Property PasswordHistoryID As Integer

    ''' <summary>
    ''' User identifier
    ''' </summary>
    Public Property UserID As Integer

    ''' <summary>
    ''' Historical password hash
    ''' </summary>
    Public Property PasswordHash As String

    ''' <summary>
    ''' Historical password salt
    ''' </summary>
    Public Property PasswordSalt As String

    ''' <summary>
    ''' Date when password was created
    ''' </summary>
    Public Property CreatedDate As DateTime = DateTime.Now

    ''' <summary>
    ''' Navigation property to user
    ''' </summary>
    Public Property User As User

    ''' <summary>
    ''' Check if password matches this history entry
    ''' </summary>
    ''' <param name="passwordHash">Password hash to check</param>
    ''' <returns>True if password matches</returns>
    Public Function MatchesPassword(passwordHash As String) As Boolean
        Return String.Equals(Me.PasswordHash, passwordHash, StringComparison.Ordinal)
    End Function
End Class
