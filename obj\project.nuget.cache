{"version": 2, "dgSpecHash": "deZNTZ1ziAk=", "success": true, "projectFilePath": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\adgv\\0.1.0.10\\adgv.0.1.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\closedxml\\0.104.2\\closedxml.0.104.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\closedxml.parser\\1.2.0\\closedxml.parser.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.engine\\13.0.4003\\crystalreports.engine.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.clientdoc\\13.0.4003\\crystalreports.reportappserver.clientdoc.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.commlayer\\13.0.4003\\crystalreports.reportappserver.commlayer.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.commoncontrols\\13.0.4003\\crystalreports.reportappserver.commoncontrols.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.commonobjectmodel\\13.0.4003\\crystalreports.reportappserver.commonobjectmodel.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.controllers\\13.0.4003\\crystalreports.reportappserver.controllers.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.cubedefmodel\\13.0.4003\\crystalreports.reportappserver.cubedefmodel.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.datadefmodel\\13.0.4003\\crystalreports.reportappserver.datadefmodel.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.datasetconversion\\13.0.4003\\crystalreports.reportappserver.datasetconversion.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.objectfactory\\13.0.4003\\crystalreports.reportappserver.objectfactory.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.prompting\\13.0.4003\\crystalreports.reportappserver.prompting.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.reportdefmodel\\13.0.4003\\crystalreports.reportappserver.reportdefmodel.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.reportappserver.xmlserialize\\13.0.4003\\crystalreports.reportappserver.xmlserialize.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\crystalreports.shared\\13.0.4003\\crystalreports.shared.13.0.4003.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\datagridview-autofilter\\1.1.0\\datagridview-autofilter.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\documentformat.openxml\\3.1.1\\documentformat.openxml.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\documentformat.openxml.framework\\3.1.1\\documentformat.openxml.framework.3.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\exceldatareader\\3.7.0\\exceldatareader.3.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\exceldatareader.dataset\\3.7.0\\exceldatareader.dataset.3.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\excelnumberformat\\1.1.0\\excelnumberformat.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\log4net\\2.0.12\\log4net.2.0.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialskin.2\\2.3.1\\materialskin.2.2.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\metromodernui\\1.4.0\\metromodernui.1.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.cryptography\\10.0.0-preview.1.25080.5\\microsoft.bcl.cryptography.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.hashcode\\1.1.1\\microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\6.0.1\\microsoft.data.sqlclient.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni\\6.0.2\\microsoft.data.sqlclient.sni.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.1\\microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.2\\microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.2\\microsoft.extensions.options.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.5.0\\microsoft.identitymodel.abstractions.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.5.0\\microsoft.identitymodel.jsonwebtokens.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.5.0\\microsoft.identitymodel.logging.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.5.0\\microsoft.identitymodel.protocols.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.5.0\\microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.5.0\\microsoft.identitymodel.tokens.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netframework.referenceassemblies\\1.0.0\\microsoft.netframework.referenceassemblies.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netframework.referenceassemblies.net48\\1.0.0\\microsoft.netframework.referenceassemblies.net48.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.office.interop.excel\\15.0.4795.1001\\microsoft.office.interop.excel.15.0.4795.1001.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.office.interop.word\\15.0.4797.1004\\microsoft.office.interop.word.15.0.4797.1004.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.sqlmanagementobjects\\172.64.0\\microsoft.sqlserver.sqlmanagementobjects.172.64.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.toolkit.uwp.notifications\\7.1.3\\microsoft.toolkit.uwp.notifications.7.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry.accesscontrol\\10.0.0-preview.1.25080.5\\microsoft.win32.registry.accesscontrol.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\10.0.0-preview.1.25080.5\\microsoft.win32.systemevents.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.compatibility\\10.0.0-preview.1.25080.4\\microsoft.windows.compatibility.10.0.0-preview.1.25080.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.contracts\\10.0.19041.1\\microsoft.windows.sdk.contracts.10.0.19041.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rbush\\4.0.0\\rbush.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.fonts\\1.0.0\\sixlabors.fonts.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.6.0\\system.buffers.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\10.0.0-preview.1.25080.5\\system.codedom.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.componentmodel.composition\\10.0.0-preview.1.25080.5\\system.componentmodel.composition.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\10.0.0-preview.1.25080.5\\system.configuration.configurationmanager.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.datasetextensions\\4.5.0\\system.data.datasetextensions.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.odbc\\10.0.0-preview.1.25080.5\\system.data.odbc.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\10.0.0-preview.1.25080.5\\system.data.oledb.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.9.0\\system.data.sqlclient.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\8.0.1\\system.diagnostics.diagnosticsource.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\10.0.0-preview.1.25080.5\\system.diagnostics.eventlog.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\10.0.0-preview.1.25080.5\\system.diagnostics.performancecounter.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices\\10.0.0-preview.1.25080.5\\system.directoryservices.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.accountmanagement\\10.0.0-preview.1.25080.5\\system.directoryservices.accountmanagement.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.directoryservices.protocols\\10.0.0-preview.1.25080.5\\system.directoryservices.protocols.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\10.0.0-preview.1.25080.3\\system.drawing.common.10.0.0-preview.1.25080.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\10.0.0-preview.1.25080.5\\system.formats.asn1.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.5.0\\system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.filesystem.accesscontrol\\5.0.0\\system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.packaging\\10.0.0-preview.1.25080.5\\system.io.packaging.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipes.accesscontrol\\5.0.0\\system.io.pipes.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.ports\\10.0.0-preview.1.25080.5\\system.io.ports.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\10.0.0-preview.1.25080.5\\system.management.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.6.0\\system.memory.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.6.0\\system.numerics.vectors.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.context\\10.0.0-preview.1.25080.5\\system.reflection.context.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.7.0\\system.reflection.emit.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.7.0\\system.reflection.emit.ilgeneration.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.7.0\\system.reflection.emit.lightweight.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\10.0.0-preview.1.25080.5\\system.runtime.caching.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.1.0\\system.runtime.compilerservices.unsafe.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.windowsruntime\\4.6.0\\system.runtime.windowsruntime.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.windowsruntime.ui.xaml\\4.6.0\\system.runtime.windowsruntime.ui.xaml.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.algorithms\\4.3.1\\system.security.cryptography.algorithms.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\10.0.0-preview.1.25080.5\\system.security.cryptography.pkcs.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\10.0.0-preview.1.25080.5\\system.security.cryptography.protecteddata.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\10.0.0-preview.1.25080.5\\system.security.cryptography.xml.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.permissions\\10.0.0-preview.1.25080.5\\system.security.permissions.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.duplex\\4.10.0\\system.servicemodel.duplex.4.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\4.10.0\\system.servicemodel.http.4.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\4.10.0\\system.servicemodel.nettcp.4.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\4.10.0\\system.servicemodel.primitives.4.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.security\\4.10.0\\system.servicemodel.security.4.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.syndication\\10.0.0-preview.1.25080.5\\system.servicemodel.syndication.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.serviceprocess.servicecontroller\\10.0.0-preview.1.25080.5\\system.serviceprocess.servicecontroller.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.speech\\10.0.0-preview.1.25080.5\\system.speech.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\10.0.0-preview.1.25080.5\\system.text.encoding.codepages.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\6.0.10\\system.text.json.6.0.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.accesscontrol\\10.0.0-preview.1.25080.5\\system.threading.accesscontrol.10.0.0-preview.1.25080.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.web.services.description\\4.10.0\\system.web.services.description.4.10.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\windowsapicodepack-core\\1.1.1\\windowsapicodepack-core.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\windowsapicodepack-shell\\1.1.1\\windowsapicodepack-shell.1.1.1.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "message": "Error occurred while getting package vulnerability data: Unable to load the service index for source https://api.nuget.org/v3/index.json.", "projectPath": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj", "warningLevel": 1, "filePath": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj", "targetGraphs": []}]}