﻿Imports System.Data.SqlClient
Imports System.Reflection.Emit

Public Class Frm_Add_Employee_Shifts

    Private Shared _instance As PerformanceManager
    Private ReadOnly _performanceCounters As New Dictionary(Of String, Stopwatch)
    Private ReadOnly _memoryThreshold As Long = 500 * 1024 * 1024 ' 500 MB
    Private ReadOnly _logger As Logger = Logger.GetInstance() ' إضافة تهيئة للمتغير _logger
    Dim pm As PerformanceManager = PerformanceManager.GetInstance()

    Public ConStr As String = GetConnectionString()
    Public Con As New SqlConnection(ConStr)
    Public dt_Tbl_Employee As New DataTable
    Dim ds As New DataSet
    Private columnsToExport As Object

    Public Sub HideColumns(dgv As DataGridView, ParamArray columnNames() As String)
        For Each columnName As String In columnNames
            If dgv.Columns.Contains(columnName) Then
                dgv.Columns(columnName).Visible = False
            End If
        Next
    End Sub

    Private Function GetColumnHeaderName(columnName As String) As String
        Select Case columnName
            Case "Emp_ID" : Return "الكود"
            Case "Emp_First_Name" : Return "الاسم الأول"
            Case "Emp_Second_Name" : Return "الاسم الثاني"
            Case "Emp_Full_Name" : Return "اسم الموظف"
            Case "Emp_Name_English" : Return "الاسم باللغة الإنجليزية"
            Case "Emp_Phone1" : Return "رقم التليفون الأول"
            Case "Emp_Phone2" : Return "رقم التليفون الثاني"
            Case "Emp_Address" : Return "العنوان"
            Case "Emp_Marital_Status" : Return "الحالة الاجتماعية"
            Case "Emp_Nationality" : Return "الجنسية"
            Case "People_With_Special_Needs" : Return "أشخاص ذوي احتياجات خاصة"
            Case "National_ID" : Return "الرقم القومي"
            Case "Date_Birth" : Return "تاريخ الميلاد"
            Case "Place_Birth" : Return "مكان الميلاد"
            Case "Emp_Image" : Return "صورة الموظف"
            Case "Emp_Type" : Return "نوع الموظف"
            Case "Working_Condition" : Return "حالة العمل"
            Case "Dept_Code" : Return "كود القسم"
            Case "Dept_Name" : Return "اسم القسم"
            Case "Jop_Code" : Return "كود الوظيفة"
            Case "Jop_Name" : Return "اسم الوظيفة"
            Case "Emp_Date_Hiring" : Return "تاريخ التعيين"
            Case "Emp_Car" : Return "السيارة"
            Case "Car_Ride_Time" : Return "وقت ركوب السيارة"
            Case "Car_Pick_Up_Point" : Return "نقطة ركوب السيارة"
            Case "Insurance_Status" : Return "حالة التأمين"
            Case "Jop_Code_insurance" : Return "كود المهنة في التأمين"
            Case "Jop_Name_insurance" : Return "اسم المهنة في التأمين"
            Case "Health_Card" : Return "بطاقة الصحة"
            Case "Health_Card_Number" : Return "رقم بطاقة الصحة"
            Case "Health_Card_Start_Date" : Return "تاريخ بدء بطاقة الصحة"
            Case "Health_Card_Renewal_Date" : Return "تاريخ تجديد البطاقة الصحية"
            Case "Health_Card_Expiration_Date" : Return "تاريخ انتهاء البطاقة الصحية"
            Case "Number_Insurance" : Return "رقم التأمين"
            Case "Date_Insurance_Start" : Return "تاريخ بدء التأمين"
            Case "Insurance_Salary" : Return "راتب التأمين"
            Case "Percentage_Insurance_Payable" : Return "نسبة التأمين المدفوعة"
            Case "Due_Insurance_Amount" : Return "المبلغ المستحق للتأمين"
            Case "Form_S1" : Return "نموذج S1"
            Case "Confirmation_Insurance_Entry" : Return "تأكيد دخول التأمين"
            Case "Delivery_Date_S1" : Return "تاريخ تسليم نموذج S1"
            Case "Receive_Date_S1" : Return "تاريخ استلام نموذج S1"
            Case "Form_S6" : Return "نموذج S6"
            Case "Delivery_Date_S6" : Return "تاريخ تسليم نموذج S6"
            Case "Receive_Date_S6" : Return "تاريخ استلام نموذج S6"
            Case "Hiring_Date_Health_Card" : Return "تاريخ التعيين للبطاقة الصحية"
            Case "Skill_level_measurement_certificate" : Return "شهادة قياس مستوى المهارة"
            Case "The_health_card_remains_expire" : Return "بتقى على انتهاء البطاقة الصحية"
            Case "End_date_probationary_period" : Return "تاريخ انتهاء فترة الاختبار"
            Case "CurrentWeekShift" : Return "وردية الأسبوع الحالي"
            Case "NextWeekShift" : Return "وردية الأسبوع القادم"
            Case "Friday_Operation" : Return "تشغيل يوم الجمعة"
            Case "Shift_Type" : Return "نوع الوردية"
            Case "Entrance_Date_S1" : Return "تاريخ دخول نموذج S1"
            Case "Entrance_Number_S1" : Return "رقم دخول نموذج S1"
            Case "Remaining_Contract_Renewal" : Return "تجديد العقد المتبقي"
            Case "Medical_Exam_Form_Submission" : Return "تقديم نموذج الفحص الطبي"
            Case "Years_Since_Contract_Start" : Return "يعمل فى الشركة منذ"
            Case "Contract_Renewal_Date" : Return "تاريخ تجديد العقد"
            Case "Contract_Expiry_Date" : Return "تاريخ انتهاء العقد"
            Case "Insurance_Code" : Return "كود التأمين"
            Case "Personal_ID_Expiry_Date" : Return "تاريخ انتهاء بطاقة الهوية الشخصية"
            Case "Contract_Renewal_Month" : Return "شهر تجديد العقد"
            Case "Military_Service_Certificate" : Return "شهادة الخدمة العسكرية"
            Case "Qualification_Certificate" : Return "شهادة التأهيل"
            Case "Birth_Certificate" : Return "شهادة الميلاد"
            Case "Insurance_Printout" : Return "طباعة التأمين"
            Case "ID_Card_Photo" : Return "صورة بطاقة الهوية"
            Case "Personal_Photos" : Return "صور شخصية"
            Case "Employment_Contract" : Return "عقد العمل"
            Case "Medical_Exam_Form" : Return "نموذج الفحص الطبي"
            Case "Criminal_Record_Check" : Return "فحص السجل الجنائي"
            Case "Social_Status_Report" : Return "تقرير الحالة الاجتماعية"
            Case "Work_Heel" : Return "العمل على الكعب"
            Case "Heel_Work_Number" : Return "رقم العمل على الكعب"
            Case "Heel_Work_Registration_Date" : Return "اخر تاريخ لتسجيل كعب العمل"
            Case "Heel_Work_Recipient" : Return "مستلم العمل على الكعب"
            Case "Heel_Work_Recipient_Address" : Return "عنوان مستلم العمل على الكعب"
            Case "Entrance_Number_S6" : Return "رقم دخول نموذج S6"
            Case "Entrance_Date_S6" : Return "تاريخ دخول نموذج S6"
            Case "Shift_paper" : Return "ورق الورديات"
            Case "Age" : Return "العمر"
            Case "Date_Resignation" : Return "تاريخ الاستقالة"
            Case "Reason_Resignation" : Return "سبب الاستقالة"
            Case "Mother_Name" : Return "اسم الأم"
            Case "Confirm_Exit_Insurance" : Return "تأكيد الخروج من التأمينات"
            Case "Governorate" : Return "المحافظة"
            Case Else : Return columnName
        End Select
    End Function

    Public Async Function SelectAll_Tbl_EmployeeAsync(dgv As DataGridView) As Task
        pm.StartMeasurement("SelectAll_Tbl_Employee")

        Try
            ' 1. استخدام Using لإدارة الموارد تلقائيًا
            Using connection As New SqlConnection(ConStr)
                Using da As New SqlDataAdapter("Select_All_Employee_WithSequence", connection)
                    da.SelectCommand.CommandType = CommandType.StoredProcedure

                    ' 4. استخدام Async/Await للعمليات غير المتزامنة
                    Await connection.OpenAsync()

                    ' 2. إنشاء DataTable جديد بدلًا من استخدام واحد عام
                    Dim tempTable As New DataTable()

                    ' 3. استخدام FillAsync بدلًا من Fill
                    Await Task.Run(Sub() da.Fill(tempTable))

                    ' تحديث واجهة المستخدم في سياقها الأصلي
                    dgv.Invoke(Sub()
                                   dt_Tbl_Employee = tempTable
                                   dgv.DataSource = dt_Tbl_Employee

                                   HideColumns(dgv, "مسلسل", "Emp_First_Name", "Emp_Second_Name", "Emp_Name_English", "Emp_Phone1", "Emp_Phone2",
                                           "Emp_Address", "Emp_Marital_Status", "Emp_Nationality", "People_With_Special_Needs", "National_ID", "Date_Birth",
                                           "Place_Birth", "Emp_Image", "Emp_Type", "Working_Condition", "Dept_Code", "Dept_Name", "Jop_Code", "Jop_Name",
                                           "Emp_Date_Hiring", "Emp_Car", "Car_Ride_Time", "Car_Pick_Up_Point", "Insurance_Status", "Jop_Code_insurance",
                                           "Jop_Name_insurance", "Health_Card", "Health_Card_Number", "Health_Card_Start_Date", "Health_Card_Renewal_Date", "Health_Card_Expiration_Date",
                                           "Number_Insurance", "Date_Insurance_Start", "Insurance_Salary", "Percentage_Insurance_Payable", "Due_Insurance_Amount",
                                           "Form_S1", "Confirmation_Insurance_Entry", "Delivery_Date_S1", "Receive_Date_S1", "Form_S6", "Delivery_Date_S6",
                                           "Receive_Date_S6", "Hiring_Date_Health_Card", "Skill_level_measurement_certificate", "The_health_card_remains_expire",
                                           "End_date_probationary_period", "CurrentWeekShift", "NextWeekShift", "Friday_Operation", "Shift_Type",
                                           "Entrance_Date_S1", "Entrance_Number_S1", "Remaining_Contract_Renewal", "Medical_Exam_Form_Submission", "Years_Since_Contract_Start",
                                           "Contract_Renewal_Date", "Contract_Expiry_Date", "Insurance_Code", "Personal_ID_Expiry_Date", "Contract_Renewal_Month",
                                           "Military_Service_Certificate", "Qualification_Certificate", "Birth_Certificate", "Insurance_Printout", "ID_Card_Photo",
                                           "Personal_Photos", "Employment_Contract", "Medical_Exam_Form", "Criminal_Record_Check", "Social_Status_Report",
                                           "Work_Heel", "Heel_Work_Number", "Heel_Work_Registration_Date", "Heel_Work_Recipient", "Heel_Work_Recipient_Address",
                                           "Entrance_Number_S6", "Entrance_Date_S6", "Shift_paper", "Age", "Date_Resignation", "Reason_Resignation", "Mother_Name", "Confirm_Exit_Insurance", "Governorate", "Direct_Manager")

                                   For Each column As DataGridViewColumn In dgv.Columns
                                       column.HeaderText = GetColumnHeaderName(column.Name)
                                   Next

                                   ' تكبير العمود المطلوب
                                   dgv.Columns("Emp_Full_Name").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                               End Sub)
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            '_logger.LogError(ex.ToString(), "فشل في تحميل بيانات الموظفين")
        Finally
            Dim elapsed = pm.StopMeasurement("SelectAll_Tbl_Employee")
            'Dim value = _logger.LogInformation($"تم تنفيذ الإجراء في {elapsed.TotalMilliseconds:F2} مللي ثانية")
        End Try
    End Function

    Private Sub Emp_ID_KeyDown(sender As Object, e As KeyEventArgs) Handles Emp_ID.KeyDown
        ' التأكد من أن المفتاح المضغوط هو مفتاح Enter
        If e.KeyCode = Keys.Enter Then
            ' تنفيذ الكود لاسترداد بيانات الموظف
            RetrieveEmployeeData(Emp_ID.Text)
            Emp_ID.SelectAll()
        End If
    End Sub

    Private Sub ExecuteStoredProcedure(ByVal procedureName As String, ByVal connection As SqlConnection)
        Using command As New SqlCommand(procedureName, connection)
            command.CommandType = CommandType.StoredProcedure
            command.ExecuteNonQuery()
        End Using
    End Sub

    Private Sub TextBoxArbic_Enter(ByVal sender As Object, ByVal e As EventArgs) Handles Description_Note.Enter, Txt_Search.Enter
        Arabic(DirectCast(sender, TextBox))
    End Sub


    Private Sub Txt_Search_TextChanged(sender As Object, e As EventArgs) Handles Txt_Search.TextChanged
        Try
            Dim dv As DataView = dt_Tbl_Employee.DefaultView
            Select Case ComboSearch.Text
                Case "اسم الموظف"
                    dv.RowFilter = " Emp_Full_Name LIKE '%" & Txt_Search.Text & "%' "
                Case "رقم التليفون"
                    dv.RowFilter = " Emp_Phone1 LIKE '%" & Txt_Search.Text & "%' "
                Case "القسم"
                    dv.RowFilter = " Dept_Name LIKE '%" & Txt_Search.Text & "%' "
                Case "الوظيفة"
                    dv.RowFilter = " Jop_Name LIKE '%" & Txt_Search.Text & "%' "
                Case "النوع"
                    dv.RowFilter = " Emp_Type LIKE '%" & Txt_Search.Text & "%' "
                Case "السيارة"
                    dv.RowFilter = " Emp_Car LIKE '%" & Txt_Search.Text & "%' "
                Case "نقطة تجمع السيارة"
                    dv.RowFilter = " Car_Pick_Up_Point LIKE '%" & Txt_Search.Text & "%' "
                Case "الوردية للاسبوع الحالى"
                    dv.RowFilter = " CurrentWeekShift LIKE '%" & Txt_Search.Text & "%' "
            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Sub Frm_Add_Employee_Shifts_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Emp_ID.Focus()

        Try
            If Con.State = ConnectionState.Closed Then
                Con.Open()
            End If
            Dim da As New SqlDataAdapter
            da = New SqlDataAdapter("Select_All_Employee", Con)
            da.SelectCommand.CommandType = CommandType.StoredProcedure
            da.Fill(ds, "ds_Tbl_Employee")
            SelectAll_Tbl_EmployeeAsync(dgv_Tbl_Employee)
            dgv_Tbl_Employee.DataSource = ds.Tables("ds_Tbl_Employee")
            BindingSource1.DataSource = dt_Tbl_Employee.DefaultView
            Con.Close()
        Catch ex As Exception
            MessageBox.Show("خطأ: " & ex.Message)
        End Try
        Con.Close()
        Note_ID.Text = Max_Note_Employee() + 1
        txt_Company_Name.Text = My.Settings.Company_Name.ToString()
    End Sub

    Public Sub ClearControls()
        Me.Emp_ID.Text = vbNullString
        Me.Emp_Full_Name.Text = vbNullString
        Me.CurrentWeekShift.Text = vbNullString
        Me.NextWeekShift.Text = vbNullString
        Me.Friday_Operation.Text = vbNullString
        Me.Emp_Car.Text = vbNullString
        Me.Description_Note.Text = vbNullString
        Me.Note_Date.Value = Today

        Note_ID.Text = Max_Note_Employee() + 1
        Me.Emp_ID.Focus()
    End Sub

    Public Sub ClearControls_Without_EmpID()
        Me.Emp_Car.Text = vbNullString
        Me.Emp_Full_Name.Text = vbNullString
        Me.CurrentWeekShift.Text = vbNullString
        Me.NextWeekShift.Text = vbNullString
        Me.Friday_Operation.Text = vbNullString
        Me.Emp_Car.Text = vbNullString
        Me.Description_Note.Text = vbNullString
        Me.Note_Date.Value = Today
        Note_ID.Text = Max_Note_Employee() + 1
        Me.Emp_ID.Focus()
    End Sub

    Private Sub RetrieveEmployeeData(ByVal empID As String)
        PerformanceManager.GetInstance().StartMeasurement("RetrieveEmployeeData")
        ClearControls_Without_EmpID()
        Using command As New SqlCommand("GetEmployee_BY_EmpID", Con)
            command.CommandType = CommandType.StoredProcedure
            command.Parameters.AddWithValue("@EmpID", empID)
            If Con.State = 1 Then Con.Close()
            Con.Open()
            PerformanceManager.GetInstance().MonitorMemoryUsage()
            Try
                Dim reader As SqlDataReader = command.ExecuteReader()
                If reader.HasRows Then
                    While reader.Read()
                        Emp_Full_Name.Text = reader("Emp_Full_Name").ToString()
                        CurrentWeekShift.Text = reader("CurrentWeekShift").ToString()
                        NextWeekShift.Text = reader("NextWeekShift").ToString()
                        Friday_Operation.Text = reader("Friday_Operation").ToString()
                        Emp_Car.Text = reader("Emp_Car").ToString()
                    End While
                Else
                    MessageBox.Show("لم يتم العثور على معلومات لهذا الموظف")
                End If
            Catch ex As Exception
                MessageBox.Show("خطأ:  " & ex.Message)
            End Try
        End Using
        PerformanceManager.GetInstance().MonitorMemoryUsage()
        Dim elapsed As TimeSpan = PerformanceManager.GetInstance().StopMeasurement("RetrieveEmployeeData")
        Con.Close()
    End Sub

    Public Sub Update_CurrentWeekShift(ByVal CurrentWeekShift As String, ByVal Friday_Operation As String, ByVal Emp_IDW As Int32)
        If Con.State = 1 Then Con.Close()
        Con.Open()
        Dim Cmd As New SqlCommand
        With Cmd
            .Connection = Con
            .CommandType = CommandType.StoredProcedure
            .CommandText = "Update_Shift_Employee_Productions" ' اسم الإجراء المخزن هنا
            .Parameters.Clear()
            .Parameters.AddWithValue("@CurrentWeekShift", CurrentWeekShift)
            .Parameters.AddWithValue("@Friday_Operation", Friday_Operation)
            .Parameters.AddWithValue("@Emp_ID", Emp_IDW)
        End With
        Cmd.ExecuteNonQuery()
        Con.Close()
        MsgBox("تم تعديل السجل بنجاح", MsgBoxStyle.Information, "تعديل")
        Cmd = Nothing
    End Sub

    Private Sub Btn_ShiftChange_ForEmp_Click(sender As Object, e As EventArgs) Handles Btn_ShiftChange_ForEmp.Click
        Dim DeviceName As String = Environ$("computername")
        Dim MovementDescription As String = " تم تعديل الوردية إلى  " + Emp_Full_Name.Text + "  بتاريخ  " + Date.Now.ToString("dd/MM/yyyy") + "  بواسطة  " + My.Settings.User_Name.ToString() + "  من خلال الجهاز  " + DeviceName

        Update_CurrentWeekShift(CurrentWeekShift.Text, Friday_Operation.Text, Int(Emp_ID.Text))
        Insert_MovementHistory(MovementDescription, DateTime.Now, loggedInUserName, DeviceName, Me.Name, "تغيير الوردية عن طريق الانتاج")
        RetrieveEmployeeData(Emp_ID.Text)
    End Sub

    Private Sub Emp_IDlbl_Click(sender As Object, e As EventArgs) Handles Emp_IDlbl.Click
        ClearControls()
    End Sub

    Private Sub PopulateTextBox(textBox As TextBox, columnName As String)
        Dim value = dgv_Tbl_Employee.CurrentRow.Cells(columnName).Value
        textBox.Text = If(Not IsDBNull(value), value.ToString(), "")
    End Sub

    Private Sub dgv_Tbl_Employee_CellContentClick_1(sender As Object, e As DataGridViewCellEventArgs) Handles dgv_Tbl_Employee.CellContentClick
        Dim CurrentRow As DataGridViewRow = dgv_Tbl_Employee.CurrentRow
        If Con.State = 1 Then Con.Close()
        Con.Open()
        Try
            PopulateTextBox(Me.Emp_ID, "Emp_ID")
            RetrieveEmployeeData(Emp_ID.Text)
        Catch ex As Exception
            MessageBox.Show(ex.Message)
            'Con.Close()
        End Try
        Con.Close()
    End Sub

    Private Sub dgv_Tbl_Employee_FilterStringChanged(sender As Object, e As EventArgs) Handles dgv_Tbl_Employee.FilterStringChanged
        BindingSource1.Filter = dgv_Tbl_Employee.FilterString
        dgv_Tbl_Employee.DataSource = BindingSource1
    End Sub

    Private Sub dgv_Tbl_Employee_SortStringChanged(sender As Object, e As EventArgs) Handles dgv_Tbl_Employee.SortStringChanged
        BindingSource1.Sort = dgv_Tbl_Employee.SortString
        dgv_Tbl_Employee.DataSource = BindingSource1
    End Sub




    Private Sub Btn_First_Record_Click(sender As Object, e As EventArgs) Handles Btn_First_Record.Click
        ds.Clear()
        Dim da As New SqlDataAdapter
        da = New SqlDataAdapter("Select_All_Employee", Con)
        da.SelectCommand.CommandType = CommandType.StoredProcedure
        da.Fill(ds, "ds_Tbl_Employee")
        SelectAll_Tbl_EmployeeAsync(dgv_Tbl_Employee)
        Dim i As Integer
        i = 0
        Emp_ID.Text = ds.Tables("ds_Tbl_Employee").Rows(i)(0).ToString()
        RetrieveEmployeeData(Emp_ID.Text)
    End Sub


    Private Sub Btn_Next_Record_Click(sender As Object, e As EventArgs) Handles Btn_Next_Record.Click
        If String.IsNullOrEmpty(Emp_ID.Text) Then
            ' إذا كانت قيمة "Emp_ID" فارغة، فجلب السجل الأول
            Emp_ID.Text = ds.Tables("ds_Tbl_Employee").Rows(0)(0).ToString()
        Else
            ' إذا كانت "Emp_ID" تحتوي على قيمة، قم بالبحث عن هذه القيمة في العمود الأول من "ds_Tbl_Employee"
            Dim rowIndex As Integer = -1
            For Each row As DataRow In ds.Tables("ds_Tbl_Employee").Rows
                If row(0).ToString() = Emp_ID.Text Then
                    ' إذا تم العثور على القيمة، احفظ موقع السطر
                    rowIndex = ds.Tables("ds_Tbl_Employee").Rows.IndexOf(row)
                    Exit For
                End If
            Next
            If rowIndex <> -1 AndAlso rowIndex < ds.Tables("ds_Tbl_Employee").Rows.Count - 1 Then
                ' إذا تم العثور على القيمة وليس السطر الأخير، جلب السطر التالي
                Emp_ID.Text = ds.Tables("ds_Tbl_Employee").Rows(rowIndex + 1)(0).ToString()
            End If
        End If
        RetrieveEmployeeData(Emp_ID.Text)
    End Sub

    Private Sub Btn_Previous_Record_Click(sender As Object, e As EventArgs) Handles Btn_Previous_Record.Click
        If String.IsNullOrEmpty(Emp_ID.Text) Then
            ' إذا كانت قيمة "Emp_ID" فارغة، فجلب السجل الأول
            'Emp_ID.Text = ds.Tables("ds_Tbl_Employee").Rows(0)(0).ToString()
            Emp_ID.Text = ds.Tables("ds_Tbl_Employee").Rows(ds.Tables("ds_Tbl_Employee").Rows.Count - 1)(0).ToString()

        Else
            ' إذا كانت "Emp_ID" تحتوي على قيمة، قم بالبحث عن هذه القيمة في العمود الأول من "ds_Tbl_Employee"
            Dim rowIndex As Integer = -1
            For Each row As DataRow In ds.Tables("ds_Tbl_Employee").Rows
                If row(0).ToString() = Emp_ID.Text Then
                    ' إذا تم العثور على القيمة، احفظ موقع السطر
                    rowIndex = ds.Tables("ds_Tbl_Employee").Rows.IndexOf(row)
                    Exit For
                End If
            Next

            If rowIndex > 0 Then
                ' إذا تم العثور على القيمة وليس السطر الأول، جلب السطر السابق
                Emp_ID.Text = ds.Tables("ds_Tbl_Employee").Rows(rowIndex - 1)(0).ToString()
            End If
        End If
        RetrieveEmployeeData(Emp_ID.Text)
    End Sub

    Private Sub Btn_Last_Record_Click(sender As Object, e As EventArgs) Handles Btn_Last_Record.Click
        ds.Clear()
        Dim da As New SqlDataAdapter
        da = New SqlDataAdapter("Select_All_Employee", Con)
        da.SelectCommand.CommandType = CommandType.StoredProcedure
        da.Fill(ds, "ds_Tbl_Employee")
        SelectAll_Tbl_EmployeeAsync(dgv_Tbl_Employee)
        Dim i As Integer
        i = ds.Tables("ds_Tbl_Employee").Rows.Count - 1
        Emp_ID.Text = ds.Tables("ds_Tbl_Employee").Rows(i)(0).ToString()
        RetrieveEmployeeData(Emp_ID.Text)

    End Sub



    '------------------------------------------------------------------------------------------------------------------------------------------------
    '********************************************************** خاص باضافة ملاحظة عن الموظف **********************************************************
    '------------------------------------------------------------------------------------------------------------------------------------------------
    Public Sub Insert_Note_Employee(ByVal Note_ID As Int32, ByVal Note_Emp_ID As Int32, ByVal Note_Employee_Name As String, ByVal Description_Note As String, ByVal Note_Date As Date, ByVal Created_By As String, ByVal Created_Date As Date)
        Try
            Dim Cmd As New SqlCommand
            With Cmd
                .Connection = Con
                .CommandType = CommandType.Text
                .CommandText = "Insert Into Note_Employee ( Note_ID,Note_Emp_ID,Note_Employee_Name,Description_Note,Note_Date,Created_By,Created_Date)values(@Note_ID,@Note_Emp_ID,@Note_Employee_Name,@Description_Note,@Note_Date,@Created_By,@Created_Date)"
                .Parameters.Clear()
                .Parameters.AddWithValue("@Note_ID", SqlDbType.Int).Value = Note_ID
                .Parameters.AddWithValue("@Note_Emp_ID", SqlDbType.Int).Value = Note_Emp_ID
                .Parameters.AddWithValue("@Note_Employee_Name", SqlDbType.VarChar).Value = Note_Employee_Name
                .Parameters.AddWithValue("@Description_Note", SqlDbType.VarChar).Value = Description_Note
                .Parameters.AddWithValue("@Note_Date", SqlDbType.Date).Value = Note_Date
                .Parameters.AddWithValue("@Created_By", SqlDbType.VarChar).Value = My.Settings.User_Name.ToString()
                .Parameters.AddWithValue("@Created_Date", SqlDbType.DateTime).Value = Date.Now
            End With
            If Con.State = 1 Then Con.Close()
            Con.Open()
            Cmd.ExecuteNonQuery()
            Con.Close()
            MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
            Cmd = Nothing
        Catch ex As Exception
            MessageBox.Show(ex.Message)
        End Try
    End Sub



    Public Function Max_Note_Employee()
        Dim Number As Integer
        Try
            Dim cmd As New SqlCommand("Select Max(Note_ID) From Note_Employee ", Con)
            If Con.State = 1 Then Con.Close()
            Con.Open()
            Number = cmd.ExecuteScalar
            Con.Close()
        Catch ex As Exception
            Number = 0
            Con.Close()
        End Try
        Return Number
    End Function

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        Dim DeviceName As String = Environ$("computername")
        Dim MovementDescription As String = " تم إضافة ملاحظة جديدة عن طريق مشرفين الصالة إلى   " + Emp_Full_Name.Text + "  بتاريخ  " + Date.Now.ToString("dd/MM/yyyy") + "  بواسطة  " + My.Settings.User_Name.ToString() + "  من خلال الجهاز  " + DeviceName


        If Emp_ID.Text = vbNullString Or Emp_Full_Name.Text = vbNullString Or Description_Note.Text = vbNullString Then
            MessageBox.Show("عفواً ، قم بتعبئة كل الحقول", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
            Exit Sub
        End If
        Insert_Note_Employee(Note_ID.Text, Emp_ID.Text, Emp_Full_Name.Text, Description_Note.Text, Note_Date.Value, My.Settings.User_Name.ToString(), Date.Now.ToString("dd/MM/yyyy"))
        Insert_MovementHistory(MovementDescription, DateTime.Now, loggedInUserName, DeviceName, Me.Name, "اضافة ملاحظة عن طريق الانتاج")
        ClearControls()
        'SelectAll_Note_Employee(dgv_Tbl_Employee)
        'BtnSave.Enabled = True
    End Sub

End Class