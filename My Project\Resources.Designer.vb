﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("El_Dawliya_International_System.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property _1469967782_icon_111_search() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("1469967782_icon-111-search", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property _1469967782_icon_111_search1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("1469967782_icon-111-search1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property _15() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("15", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property about_32px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("about_32px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property add_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property add_24px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add_24px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property add_file_30px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("add_file_30px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add_Male_User_Group_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add Male User Group_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Add2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Add2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Binoculars() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Binoculars", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Binoculars1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Binoculars1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Binoculars2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Binoculars2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property budget_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("budget_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property bus_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bus_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property calculator_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("calculator_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property calculator_80px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("calculator_80px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Cancel() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Cancel", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Cancel1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Cancel1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property car_30px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("car_30px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property chat_message_64px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("chat_message_64px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Check_All() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Check All", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property checkout_60px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("checkout_60px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property checkout_80px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("checkout_80px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property checkout_80px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("checkout_80px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property chevron_left_52px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("chevron_left_52px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property chevron_right_52px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("chevron_right_52px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Clock() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Clock", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property data_backup_40px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("data_backup_40px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property data_quality_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("data_quality_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property delete_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("delete_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property department_40px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("department_40px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Dollar_Coin_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Dollar Coin_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Dollar_Coin_24px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Dollar Coin_24px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property done_26px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("done_26px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Done_New() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Done_New", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property double_left_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("double_left_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property double_right_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("double_right_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property e_commerce_32px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("e-commerce_32px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Edit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Edit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property edit_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("edit_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property edit_32px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("edit_32px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Gear() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Gear", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property GoodNotes() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GoodNotes", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property GoodNotes1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GoodNotes1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property google_alerts_64px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("google_alerts_64px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Hand_Cursor() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Hand Cursor", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property hide_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("hide_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_add_image_96() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8-add-image-96", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_add_shopping_cart_80() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8-add-shopping-cart-80", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_add_shopping_cart_801() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8-add-shopping-cart-801", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_customer_80px_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8_customer_80px_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_Notification_128px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8_Notification_128px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_Office_Phone_48() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8_Office_Phone_48", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_printer_96px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8_printer_96px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property icons8_printer_96px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons8_printer_96px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property images1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("images1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property job_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("job_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property keyhole() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("keyhole", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property lg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("lg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property map_pinpoint_64px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("map_pinpoint_64px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property meeting_room_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("meeting_room_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property meeting_room_96px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("meeting_room_96px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Menu() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Menu", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Menu1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Menu1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Menu2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Menu2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property message_exchange_100px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("message_exchange_100px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property message_exchange_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("message_exchange_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Microsoft_Excel_240px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Microsoft Excel_240px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Microsoft_Excel_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Microsoft Excel_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Microsoft_Excel_96px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Microsoft Excel_96px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Microsoft_Excel_96px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Microsoft Excel_96px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property microsoft_word_96px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("microsoft_word_96px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property new_message_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_message_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property No_Image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("No Image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property No_Picture() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("No_Picture", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property note_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("note_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Notification_128px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Notification_128px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property notification_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("notification_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property notification_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("notification_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Notification_64px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Notification_64px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Ok() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Ok", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Padlock() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Padlock", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Pencil() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Pencil", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Photo_Gallery() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Photo Gallery", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Print3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Print3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property process_64px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("process_64px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Protect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Protect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Racism() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Racism", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Refresh() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Refresh", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property refresh_32px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("refresh_32px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Refresh1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Refresh1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Refresh2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Refresh2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Refresh3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Refresh3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Refresh4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Refresh4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Refresh5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Refresh5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Remove() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Remove", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Remove1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Remove1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Reply() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Reply", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Restart() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Restart", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Restart1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Restart1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Save() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Save", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Save_as() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Save as", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property scales_40px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("scales_40px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property search_contacts_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("search_contacts_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search_More() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search More", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search_More1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search More1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search_More2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search More2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search_More3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search More3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search_More4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search More4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Search_More5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Search More5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property settings_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("settings_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Share() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Share", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_down_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_down_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_down_50px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_down_50px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_down1_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_down1_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_down1_24px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_down1_24px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_down1_24px2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_down1_24px2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_left_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_left_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_left_50px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_left_50px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_left1_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_left1_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_left1_24px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_left1_24px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_right_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_right_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_right_50px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_right_50px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_right_64px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_right_64px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_right1_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_right1_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_up_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_up_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_up_50px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_up_50px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_up1_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_up1_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sort_up1_24px1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sort_up1_24px1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sorting_32px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sorting_32px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Sorting_Arrows_Horizontal_64px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Sorting Arrows Horizontal_64px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property sound_50px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("sound_50px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Stop_Sign() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Stop Sign", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property supplier_32px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("supplier_32px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property supplier_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("supplier_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property system_task_52px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("system_task_52px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property task_50px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("task_50px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property tasklist_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tasklist_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Time_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Time_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property txt_100px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("txt_100px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property txt_50px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("txt_50px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Unavailable() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Unavailable", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property user_rights_48px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("user_rights_48px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property view_24px() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("view_24px", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property Warehouse() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Warehouse", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
