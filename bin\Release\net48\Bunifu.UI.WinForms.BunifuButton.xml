<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Bunifu.UI.WinForms.BunifuButton</name>
    </assembly>
    <members>
        <member name="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton">
            <summary>
            Provides a highly customizable button with improved 
            styling options and great feature improvements.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.#ctor">
            <summary>
            Creates a new <see cref="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton"/> control.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderStyles">
            <summary>
            Provides various styles for customizing the border.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderStyles.Solid">
            <summary>
            Defaults to a solid border style.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderStyles.Dash">
            <summary>
            Defaults to a dash border style.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderStyles.Dot">
            <summary>
            Defaults to a dotted border style.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ButtonStates">
            <summary>
            Defines various states in a Button's activity cycle.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ButtonStates.Idle">
            <summary>
            Denotes the Button's idle state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ButtonStates.Hover">
            <summary>
            Denotes the Button's mouse-hover state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ButtonStates.Pressed">
            <summary>
            Denotes the Button's mouse-press or click state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ButtonStates.Disabled">
            <summary>
            Denotes the Button's disabled state.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.state">
            <summary>
            [Internal] Defines various states in a control's activity cycle.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.state.idle">
            <summary>
            There is no activity.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.state.hover">
            <summary>
            The control is in mouse-hover state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.state.pressed">
            <summary>
            The control is in mouse-click or press state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.state.disabled">
            <summary>
            The control is disabled.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AllowToggling">
            <summary>
            Gets or sets a value indicating whether 
            the button will be automatically toggled 
            to receive or release focus when clicked.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AllowAnimations">
            <summary>
            Gets or sets a value indicating whether animations
            will be allowed when the button is active.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AllowMouseEffects">
            <summary>
            Gets or sets a value indicating whether mouse effects will 
            be allowed whenever mouse-events are being captured.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AutoGenerateColors">
            <summary>
            When set to true, the button's <see cref="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleFillColor"/> 
            and <see cref="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleBorderColor"/> will be used to generate 
            colors for the various states supported.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AutoRoundBorders">
            <summary>
            Gets or sets a value indicating whether 
            the borders will be automatically rounded.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AutoSize">
            <summary>
            Specifies whether the Button will automatically 
            size it's with to fit its contents. Use <see cref="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.TextMarginLeft"/> 
            property to set the <see cref="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Text"/> padding (edges' distance).
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AutoSizeLeftIcon">
            <summary>
            When set to true, the left icon will 
            be autosized on resizing the button.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AutoSizeRightIcon">
            <summary>
            When set to true, the right icon will 
            be autosized on resizing the button.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IndicateFocus">
            <summary>
            Gets or sets a value indicating whether the 
            Button will provide a visual cue when focused.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AnimationSpeed">
            <summary>
            Gets or sets the Button's animation speed (in milliseconds) 
            when moving from one state to another.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconPadding">
            <summary>
            Gets or sets the Button's padding for
            both the left and the right icon.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconSize">
            <summary>
            Gets or sets the left and right icon size.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleBorderRadius">
            <summary>
            Gets or sets the Button's border radius when idle.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleBorderThickness">
            <summary>
            Gets or sets the button's border thickness.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ColorContrastOnHover">
            <summary>
            Sets how dark or light the button's color will 
            be whenever a mouse-hover event has occurred.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ColorContrastOnClick">
            <summary>
            Sets how dark or light the button's color will 
            be whenever a mouse-click event has occurred.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Text">
            <summary>
            Gets or sets the text associated with this control.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleBorderColor">
            <summary>
            Gets or sets the Button's border color when idle/inactive.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleFillColor">
            <summary>
            Gets or sets the Button's background/fill color when idle.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ForeColor">
            <summary>
            Gets or sets the Button's foreground color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconRightCursor">
            <summary>
            Gets or sets the Button's right icon cursor.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconLeftCursor">
            <summary>
            Gets or sets the Button's left icon cursor.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleIconLeftImage">
            <summary>
            Gets or sets the Button's left icon when idle.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleIconRightImage">
            <summary>
            Gets or sets the Button's right icon when idle.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Font">
            <summary>
            Gets or sets the Button's default font.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.OnIdleState">
            <summary>
            Gets or sets the Button's idle state design.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.onHoverState">
            <summary>
            Gets or sets the Button's hover state design.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.OnPressedState">
            <summary>
            Gets or sets the Button's pressed state design.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.OnDisabledState">
            <summary>
            Gets or sets the Button's disabled state design.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.TextAlign">
            <summary>
            Gets or sets the Button's text alignment.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconLeftAlign">
            <summary>
            Gets or sets the Button's left icon alignment.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconRightAlign">
            <summary>
            Gets or sets the Button's right icon alignment.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderStyle">
            <summary>
            Gets or sets the Button's border style.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Padding">
            <summary>
            Gets or sets the default text padding.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.TextPadding">
            <summary>
            Gets or sets the default text padding.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconLeftPadding">
            <summary>
            Gets or sets the left icon's padding.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconRightPadding">
            <summary>
            Gets or sets the right icon's padding.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.FocusState">
            <summary>
            Gets or sets the state to use when the Button 
            contains focus while the cursor is away.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.CustomizableEdges">
            <summary>
            Gets or sets the list of border edges that will 
            be customized whenever the border radius is applied.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.DialogResult">
            <summary>
            Gets or sets the <see cref="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.DialogResult"/> returned by the Button.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.UseDefaultRadiusAndThickness">
            <summary>
            Gets or sets a value indicating whether other states will automatically 
            use the idle state's border-radius and border-thickness.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IsDefault">
            <summary>
            Gets or sets a value indicating whether the 
            Bunifu button control is the default button.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Toggled">
            <summary>
            Gets a value indicating whether  
            the button has been clicked.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconMarginLeft">
            <summary>
            Gets or sets the Button's left icon margin.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ButtonTextMarginLeft">
            <summary>
            Gets or sets the Button's left text margin.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.TextMarginLeft">
            <summary>
            Gets or sets the Button's left text margin.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.PreferredHeight">
            <summary>
            Gets the preferred Button's height.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.DisabledBorderColor">
            <summary>
            Gets or sets the Button's border color when disabled.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.DisabledFillColor">
            <summary>
            Gets or sets the Button's background color when disabled.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.DisabledForecolor">
            <summary>
            Gets or sets the Button's foreground color when disabled.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ButtonText">
            <summary>
            Gets or sets the text associated with this control.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BackColor">
            <summary>
            Gets or sets the Button's background color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BackColor1">
            <summary>
            [Internal] Gets or sets the initial Button's background color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.TextAlignment">
            <summary>
            Gets or sets the Button's text alignment.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BackgroundImage">
            <summary>
            Gets or sets the Button's background image.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.LeftIcon">
            <summary>
            Gets the left icon.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.RightIcon">
            <summary>
            Gets the right icon.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties">
            <summary>
            Provides options for use when creating a 
            list of properties for use when defining a  
            control's various activity states.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.BorderRadius">
            <summary>
            Gets or sets the control's border radius.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.BorderThickness">
            <summary>
            Gets or sets the control's border thickness.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.ForeColor">
            <summary>
            Gets or sets the control's foreground color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.FillColor">
            <summary>
            Gets or sets the control's background/fill color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.BorderColor">
            <summary>
            Gets or sets the control's border color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.IconRightImage">
            <summary>
            Gets or sets the control's right icon-image.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.IconLeftImage">
            <summary>
            Gets or sets the control's left icon-image.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.BorderStyle">
            <summary>
            Gets or sets the control's border style.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties.ToString">
            <summary>
            Returns a string containing the object's 
            properties and their values.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges">
            <summary>
            Includes the list of available border edges or dimensions 
            that can be customized or excluded when styling controls.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges.#ctor">
            <summary>
            Creates a new instance of the <see cref="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges"/> class.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges.TopLeft">
            <summary>
             Gets or sets a value indicating whether 
             the top-left edge will be included.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges.TopRight">
            <summary>
             Gets or sets a value indicating whether 
             the top-right edge will be included.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges.BottomLeft">
            <summary>
             Gets or sets a value indicating whether 
             the bottom-left edge will be included.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges.BottomRight">
            <summary>
             Gets or sets a value indicating whether 
             the bottom-right edge will be included.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BorderEdges.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> containing the 
            list of properties and their applied values.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.CreateParams">
            <summary>
            Gets the list of created control parameters.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Refresh">
            <summary>
            Redraws the control's layout.
            </summary>
            <param name="useTimer">Loop redraws using a timer?</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Reset">
            <summary>
            Resets the control's temporary values to their defaults.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Reset(Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties)">
            <summary>
            Resets the button's current visual state to another state.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ResetColors">
            <summary>
            Resets the control's temporary color values to their defaults.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.PerformClick">
            <summary>
            Generates a 'Click' event for the button.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AlignText">
            <summary>
            Aligns the button text.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AlignLefticon">
            <summary>
            Aligns the button's left icon.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.AlignRighticon">
            <summary>
            Aligns the button's left icon.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.NotifyDefault(System.Boolean)">
            <summary>
            Notifies the Button whether it is the default button 
            so that it can adjust its appearance accordingly.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.GetCurrentState">
            <summary>
            Gets the Button's current activity state.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.ApplyState(Bunifu.UI.WinForms.BunifuButton.BunifuButton.StateProperties)">
            <summary>
            Sets a specified state to the button.
            </summary>
            <param name="state">
            The state to apply.
            </param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.GenerateColors(System.Boolean)">
            <summary>
            Generates colors for the the various states supported using 
            the button's <see cref="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleFillColor"/> and 
            <see cref="P:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IdleBorderColor"/> properties.
            </summary>
            <param name="invalidate">Redraw the control's surface?</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.DrawRoundedRectangle(System.Drawing.Graphics,System.Drawing.Rectangle,System.Int32,System.Drawing.Pen,System.Drawing.Color)">
            <summary>
            Draws a rounded rectangle.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconLeftClick">
            <summary>
            Occurs when the Button's left icon is clicked.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuButton.BunifuButton.IconRightClick">
            <summary>
            Occurs when the Button's right icon is clicked.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BunifuButtonActionList">
            <summary>
            Initializes a new instance of the <see cref="T:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BunifuButtonActionList"/> class.
            </summary>
            <seealso cref="T:System.ComponentModel.Design.DesignerActionList" />
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.BunifuButtonActionList.GetSortedActionItems">
            <summary>
            Implementation of this abstract method creates Smart Tag items,
            associates their targets, and collects them into a list.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuButton.BunifuButton.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuButton.BunifuButton.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Extensions.ColorExtensions">
            <summary>
            Provides a collection of methods that extend the .NET Color class.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.IManagedType">
            <summary>
            Interface for all types we can perform transitions on. 
            Each type (e.g. int, double, Color) that we can perform a transition on 
            needs to have its own class that implements this interface. These classes 
            tell the transition system how to act on objects of that type.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.IManagedType.getManagedType">
            <summary>
            Returns the Type that the instance is managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.IManagedType.copy(System.Object)">
            <summary>
            Returns a deep copy of the object passed in. (In particular this is 
            needed for types that are objects.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.IManagedType.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns an object holding the value between the start and end corresponding
            to the percentage passed in. (Note: the percentage can be less than 0% or
            greater than 100%.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ITransitionType.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Called by the Transition framework when its timer ticks to pass in the
            time (in ms) since the transition started. 
            
            You should return (in an out parameter) the percentage movement towards 
            the destination value for the time passed in. Note: this does not need to
            be a smooth transition from 0% to 100%. You can overshoot with values
            greater than 100% or undershoot if you need to (for example, to have some
            form of "elasticity").
            
            The percentage should be returned as (for example) 0.1 for 10%.
            
            You should return (in an out parameter) whether the transition has completed.
            (This may not be at the same time as the percentage has moved to 100%.)
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.ManagedType_Color">
            <summary>
            Class that manages transitions for Color properties. For these we
            need to transition the R, G, B and A sub-properties independently.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Color.getManagedType">
            <summary>
            Returns the type we are managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Color.copy(System.Object)">
            <summary>
            Returns a copy of the color object passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Color.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Creates an intermediate value for the colors depending on the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.ManagedType_Double">
            <summary>
            Manages transitions for double properties.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Double.getManagedType">
            <summary>
             Returns the type managed by this class.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Double.copy(System.Object)">
            <summary>
            Returns a copy of the double passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Double.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the value between start and end for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Float.getManagedType">
            <summary>
            Returns the type we're managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Float.copy(System.Object)">
            <summary>
            Returns a copy of the float passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Float.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the interpolated value for the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.ManagedType_Int">
            <summary>
            Manages transitions for int properties.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Int.getManagedType">
            <summary>
            Returns the type we are managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Int.copy(System.Object)">
            <summary>
            Returns a copy of the int passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_Int.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the value between the start and end for the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.ManagedType_String">
            <summary>
            Manages transitions for strings. This doesn't make as much sense as transitions
            on other types, but I like the way it looks!
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_String.getManagedType">
            <summary>
            Returns the type we're managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_String.copy(System.Object)">
            <summary>
            Returns a copy of the string passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.ManagedType_String.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns an "interpolated" string.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.Transition">
            <summary>
            Lets you perform animated transitions of properties on arbitrary objects. These 
            will often be transitions of UI properties, for example an animated fade-in of 
            a UI object, or an animated move of a UI object from one position to another.
            
            Each transition can simulataneously change multiple properties, including properties
            across multiple objects.
            
            Example transition
            ------------------
            a.      Transition t = new Transition(new TransitionMethod_Linear(500));
            b.      t.add(form1, "Width", 500);
            c.      t.add(form1, "BackColor", Color.Red);
            d.      t.run();
              
            Line a:         Creates a new transition. You specify the transition method.
                            
            Lines b. and c: Set the destination values of the properties you are animating.
            
            Line d:         Starts the transition.
            
            Transition methods
            ------------------
            TransitionMethod objects specify how the transition is made. Examples include
            linear transition, ease-in-ease-out and so on. Different transition methods may
            need different parameters.
            
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.#cctor">
            <summary>
            You should register all managed-types here.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.Transition.Args">
            <summary>
            Args passed with the TransitionCompletedEvent.
            </summary>
        </member>
        <member name="E:Utilities.BunifuButton.Transitions.Transition.TransitionCompletedEvent">
            <summary>
            Event raised when the transition hass completed.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.run(System.Object,System.String,System.Object,Utilities.BunifuButton.Transitions.ITransitionType)">
            <summary>
            Creates and immediately runs a transition on the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.run(System.Object,System.String,System.Object,System.Object,Utilities.BunifuButton.Transitions.ITransitionType)">
            <summary>
            Sets the property passed in to the initial value passed in, then creates and 
            immediately runs a transition on it.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.runChain(Utilities.BunifuButton.Transitions.Transition[])">
            <summary>
            Creates a TransitionChain and runs it.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.#ctor(Utilities.BunifuButton.Transitions.ITransitionType)">
            <summary>
            Constructor. You pass in the object that holds the properties 
            that you are performing transitions on.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.add(System.Object,System.String,System.Object)">
            <summary>
            Adds a property that should be animated as part of this transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.run">
            <summary>
            Starts the transition.
            </summary>
        </member>
        <member name="P:Utilities.BunifuButton.Transitions.Transition.TransitionedProperties">
            <summary>
            Property that returns a list of information about each property managed
            by this transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.removeProperty(Utilities.BunifuButton.Transitions.Transition.TransitionedPropertyInfo)">
            <summary>
            We remove the property with the info passed in from the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.onTimer">
            <summary>
            Called when the transition timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.setProperty(System.Object,Utilities.BunifuButton.Transitions.Transition.PropertyUpdateArgs)">
            <summary>
            Sets a property on the object passed in to the value passed in. This method
            invokes itself on the GUI thread if the property is being invoked on a GUI 
            object.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.isDisposed(System.Object)">
            <summary>
            Returns true if the object passed in is a Control and is disposed
            or in the process of disposing. (If this is the case, we don't want
            to make any changes to its properties.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Transition.registerType(Utilities.BunifuButton.Transitions.IManagedType)">
            <summary>
            Registers a transition-type. We hold them in a map.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionChain.runNextTransition">
            <summary>
            Runs the next transition in the list.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionChain.onTransitionCompleted(System.Object,Utilities.BunifuButton.Transitions.Transition.Args)">
            <summary>
            Called when the transition we have just run has completed.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionElement.#ctor(System.Double,System.Double,Utilities.BunifuButton.Transitions.InterpolationMethod)">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="P:Utilities.BunifuButton.Transitions.TransitionElement.EndTime">
            <summary>
            The percentage of elapsed time, expressed as (for example) 75 for 75%.
            </summary>
        </member>
        <member name="P:Utilities.BunifuButton.Transitions.TransitionElement.EndValue">
            <summary>
            The value of the animated properties at the EndTime. This is the percentage 
            movement of the properties between their start and end values. This should
            be expressed as (for example) 75 for 75%.
            </summary>
        </member>
        <member name="P:Utilities.BunifuButton.Transitions.TransitionElement.InterpolationMethod">
            <summary>
            The interpolation method to use when moving between the previous value
            and the current one.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionManager">
            <summary>
            This class is responsible for running transitions. It holds the timer that
            triggers transaction animation. 
            </summary><remarks>
            This class is a singleton.
            
            We manage the transaction timer here so that we can have a single timer
            across all transactions. If each transaction has its own timer, this creates
            one thread for each transaction, and this can lead to too many threads in
            an application.
            
            This class essentially just manages the timer for the transitions. It calls 
            back into the running transitions, which do the actual work of the transition.
            
            </remarks>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionManager.getInstance">
            <summary>
            Singleton's getInstance method.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionManager.register(Utilities.BunifuButton.Transitions.Transition)">
            <summary>
            You register a transition with the manager here. This will start to run
            the transition as the manager's timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionManager.removeDuplicates(Utilities.BunifuButton.Transitions.Transition)">
            <summary>
            Checks if any existing transitions are acting on the same properties as the
            transition passed in. If so, we remove the duplicated properties from the 
            older transitions.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionManager.removeDuplicates(Utilities.BunifuButton.Transitions.Transition,Utilities.BunifuButton.Transitions.Transition)">
            <summary>
            Finds any properties in the old-transition that are also in the new one,
            and removes them from the old one.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionManager.#ctor">
            <summary>
            Private constructor (for singleton).
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionManager.onTimerElapsed(System.Object,System.Timers.ElapsedEventArgs)">
            <summary>
            Called when the timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionManager.onTransitionCompleted(System.Object,Utilities.BunifuButton.Transitions.Transition.Args)">
            <summary>
            Called when a transition has completed. 
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_Acceleration">
            <summary>
            Manages transitions under constant acceleration from a standing start.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Acceleration.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Acceleration.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            The initial velocity is 0, and the acceleration to get to 1.0
            at t=1.0 is 2, so the formula just becomes:
              s = t^2
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_Bounce">
            <summary>
            This transition bounces the property to a destination value and back to the
            original value. It is accelerated to the destination and then decelerated back
            as if being dropped with gravity and bouncing back against gravity.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Bounce.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the total time taken for the bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_CriticalDamping">
            <summary>
            This transition animates with an exponential decay. This has a damping effect
            similar to the motion of a needle on an electomagnetically controlled dial.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_CriticalDamping.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_CriticalDamping.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_Deceleration">
            <summary>
            Manages a transition starting from a high speed and decelerating to zero by
            the end of the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Deceleration.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Deceleration.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            The initial velocity is 2, and the acceleration to get to 1.0
            at t=1.0 is -2, so the formula becomes:
              s = t(2-t)
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_EaseInEaseOut">
            <summary>
            Manages an ease-in-ease-out transition. This accelerates during the first 
            half of the transition, and then decelerates during the second half.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_EaseInEaseOut.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_EaseInEaseOut.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            We accelerate as at the rate needed (a=4) to get to 0.5 at t=0.5, and
            then decelerate at the same rate to end up at 1.0 at t=1.0.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_Flash">
            <summary>
            This transition type 'flashes' the properties a specified number of times, ending
            up by reverting them to their initial values. You specify the number of bounces and
            the length of each bounce. 
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Flash.#ctor(System.Int32,System.Int32)">
            <summary>
            You specify the number of bounces and the time taken for each bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_Linear">
            <summary>
            This class manages a linear transition. The percentage complete for the transition
            increases linearly with time.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Linear.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time (in milliseconds) that the
            transition will take.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_Linear.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            We return the percentage completed.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_ThrowAndCatch">
            <summary>
            This transition bounces the property to a destination value and back to the
            original value. It is decelerated to the destination and then acclerated back
            as if being thrown against gravity and then descending back with gravity.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_ThrowAndCatch.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the total time taken for the bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.TransitionType_UserDefined">
            <summary>
            This class allows you to create user-defined transition types. You specify these
            as a list of TransitionElements. Each of these defines: 
            End time , End value, Interpolation method
            
            For example, say you want to make a bouncing effect with a decay:
            
            EndTime%    EndValue%   Interpolation
            --------    ---------   -------------
            50          <USER>         <GROUP> 
            75          50          Deceleration
            85          100         Acceleration
            91          75          Deceleration
            95          100         Acceleration
            98          90          Deceleration
            100         100         Acceleration
            
            The time values are expressed as a percentage of the overall transition time. This 
            means that you can create a user-defined transition-type and then use it for transitions
            of different lengths.
            
            The values are percentages of the values between the start and end values of the properties
            being animated in the transitions. 0% is the start value and 100% is the end value.
            
            The interpolation is one of the values from the InterpolationMethod enum.
            
            So the example above accelerates to the destination (as if under gravity) by
            t=50%, then bounces back up to half the initial height by t=75%, slowing down 
            (as if against gravity) before falling down again and bouncing to decreasing 
            heights each time.
            
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_UserDefined.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_UserDefined.#ctor(System.Collections.Generic.IList{Utilities.BunifuButton.Transitions.TransitionElement},System.Int32)">
            <summary>
            Constructor. You pass in the list of TransitionElements and the total time
            (in milliseconds) for the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_UserDefined.setup(System.Collections.Generic.IList{Utilities.BunifuButton.Transitions.TransitionElement},System.Int32)">
            <summary>
            Sets up the transitions. 
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_UserDefined.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Called to find the value for the movement of properties for the time passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.TransitionType_UserDefined.getElementInfo(System.Double,System.Double@,System.Double@,System.Double@,System.Double@,Utilities.BunifuButton.Transitions.InterpolationMethod@)">
            <summary>
            Returns the element info for the time-fraction passed in. 
            </summary>
        </member>
        <member name="T:Utilities.BunifuButton.Transitions.Utility">
            <summary>
            A class holding static utility functions.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.getValue(System.Object,System.String)">
            <summary>
            Returns the value of the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.setValue(System.Object,System.String,System.Object)">
            <summary>
            Sets the value of the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.interpolate(System.Double,System.Double,System.Double)">
            <summary>
            Returns a value between d1 and d2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.interpolate(System.Int32,System.Int32,System.Double)">
            <summary>
            Returns a value betweeen i1 and i2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.interpolate(System.Single,System.Single,System.Double)">
            <summary>
            Returns a value betweeen f1 and f2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.convertLinearToEaseInEaseOut(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under an ease-in-ease-out transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.convertLinearToAcceleration(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under a constant acceleration transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.convertLinearToDeceleration(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under a constant deceleration transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuButton.Transitions.Utility.raiseEvent``1(System.EventHandler{``0},System.Object,``0)">
            <summary>
            Fires the event passed in in a thread-safe way. 
            </summary><remarks>
            This method loops through the targets of the event and invokes each in turn. If the
            target supports ISychronizeInvoke (such as forms or controls) and is set to run 
            on a different thread, then we call BeginInvoke to marshal the event to the target
            thread. If the target does not support this interface (such as most non-form classes)
            or we are on the same thread as the target, then the event is fired on the same
            thread as this is called from.
            </remarks>
        </member>
    </members>
</doc>
