-- =====================================================
-- El Dawliya International System - HR Module Enhanced Schema
-- Version: 2.0
-- Date: 2025-01-30
-- Description: Enhanced HR Module with Performance, Payroll, Leave Management
-- =====================================================

-- =====================================================
-- Enhanced Employee Management
-- =====================================================

-- Enhanced Employee table (extends existing Tbl_Employee)
CREATE TABLE HR.Employees (
    EmployeeID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
    FirstName NVARCHAR(50) NOT NULL,
    MiddleName NVARCHAR(50),
    LastName NVARCHAR(50) NOT NULL,
    FullNameArabic NVARCHAR(150) NOT NULL,
    FullNameEnglish NVARCHAR(150),
    Email NVARCHAR(100) UNIQUE,
    Phone1 NVARCHAR(20),
    Phone2 NVARCHAR(20),
    EmergencyContact NVARCHAR(100),
    EmergencyPhone NVARCHAR(20),
    Address NVARCHAR(255),
    City NVARCHAR(50),
    Governorate NVARCHAR(50),
    PostalCode NVARCHAR(10),
    NationalID NVARCHAR(20) UNIQUE,
    PassportNumber NVARCHAR(20),
    DateOfBirth DATE NOT NULL,
    PlaceOfBirth NVARCHAR(100),
    Gender NVARCHAR(10) NOT NULL CHECK (Gender IN ('Male', 'Female')),
    MaritalStatus NVARCHAR(20) CHECK (MaritalStatus IN ('Single', 'Married', 'Divorced', 'Widowed')),
    Nationality NVARCHAR(50),
    Religion NVARCHAR(50),
    BloodType NVARCHAR(5),
    EmploymentStatus NVARCHAR(20) NOT NULL DEFAULT 'Active' CHECK (EmploymentStatus IN ('Active', 'Inactive', 'Terminated', 'Suspended')),
    HireDate DATE NOT NULL,
    TerminationDate DATE,
    TerminationReason NVARCHAR(255),
    ProbationEndDate DATE,
    ContractType NVARCHAR(20) CHECK (ContractType IN ('Permanent', 'Temporary', 'Contract', 'Intern')),
    ContractStartDate DATE,
    ContractEndDate DATE,
    DepartmentID INT,
    PositionID INT,
    DirectManagerID INT,
    WorkLocation NVARCHAR(100),
    WorkScheduleID INT,
    SalaryGrade NVARCHAR(10),
    BaseSalary DECIMAL(10,2),
    Currency NVARCHAR(5) DEFAULT 'EGP',
    BankAccountNumber NVARCHAR(50),
    BankName NVARCHAR(100),
    TaxNumber NVARCHAR(20),
    SocialInsuranceNumber NVARCHAR(20),
    MedicalInsuranceNumber NVARCHAR(20),
    ProfilePicture VARBINARY(MAX),
    Notes NVARCHAR(MAX),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    CONSTRAINT FK_Employees_Department FOREIGN KEY (DepartmentID) REFERENCES HR.Departments(DepartmentID),
    CONSTRAINT FK_Employees_Position FOREIGN KEY (PositionID) REFERENCES HR.Positions(PositionID),
    CONSTRAINT FK_Employees_DirectManager FOREIGN KEY (DirectManagerID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_Employees_WorkSchedule FOREIGN KEY (WorkScheduleID) REFERENCES HR.WorkSchedules(WorkScheduleID),
    CONSTRAINT FK_Employees_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_Employees_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Security.Users(UserID)
)
GO

-- Departments with hierarchy support
CREATE TABLE HR.Departments (
    DepartmentID INT IDENTITY(1,1) PRIMARY KEY,
    DepartmentCode NVARCHAR(20) NOT NULL UNIQUE,
    DepartmentName NVARCHAR(100) NOT NULL,
    DepartmentNameEnglish NVARCHAR(100),
    ParentDepartmentID INT,
    ManagerID INT,
    CostCenter NVARCHAR(20),
    Budget DECIMAL(15,2),
    Location NVARCHAR(100),
    Description NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    CONSTRAINT FK_Departments_Parent FOREIGN KEY (ParentDepartmentID) REFERENCES HR.Departments(DepartmentID),
    CONSTRAINT FK_Departments_Manager FOREIGN KEY (ManagerID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_Departments_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_Departments_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Security.Users(UserID)
)
GO

-- Positions/Jobs with detailed specifications
CREATE TABLE HR.Positions (
    PositionID INT IDENTITY(1,1) PRIMARY KEY,
    PositionCode NVARCHAR(20) NOT NULL UNIQUE,
    PositionTitle NVARCHAR(100) NOT NULL,
    PositionTitleEnglish NVARCHAR(100),
    DepartmentID INT NOT NULL,
    JobLevel INT,
    MinSalary DECIMAL(10,2),
    MaxSalary DECIMAL(10,2),
    JobDescription NVARCHAR(MAX),
    Requirements NVARCHAR(MAX),
    Responsibilities NVARCHAR(MAX),
    Skills NVARCHAR(MAX),
    Education NVARCHAR(100),
    Experience NVARCHAR(100),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    CONSTRAINT FK_Positions_Department FOREIGN KEY (DepartmentID) REFERENCES HR.Departments(DepartmentID),
    CONSTRAINT FK_Positions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_Positions_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Security.Users(UserID)
)
GO

-- Work schedules for flexible working arrangements
CREATE TABLE HR.WorkSchedules (
    WorkScheduleID INT IDENTITY(1,1) PRIMARY KEY,
    ScheduleName NVARCHAR(50) NOT NULL,
    ScheduleType NVARCHAR(20) NOT NULL CHECK (ScheduleType IN ('Fixed', 'Flexible', 'Shift', 'Remote')),
    StartTime TIME,
    EndTime TIME,
    WorkingDays NVARCHAR(20), -- e.g., 'SMTWTFS' for days of week
    TotalHoursPerDay DECIMAL(4,2),
    TotalHoursPerWeek DECIMAL(5,2),
    BreakDuration INT, -- in minutes
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_WorkSchedules_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- =====================================================
-- Performance Evaluation System
-- =====================================================

-- Performance evaluation templates
CREATE TABLE HR.PerformanceTemplates (
    TemplateID INT IDENTITY(1,1) PRIMARY KEY,
    TemplateName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    EvaluationPeriod NVARCHAR(20) CHECK (EvaluationPeriod IN ('Monthly', 'Quarterly', 'Semi-Annual', 'Annual')),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_PerformanceTemplates_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Performance criteria/KPIs
CREATE TABLE HR.PerformanceCriteria (
    CriteriaID INT IDENTITY(1,1) PRIMARY KEY,
    TemplateID INT NOT NULL,
    CriteriaName NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    Weight DECIMAL(5,2) NOT NULL DEFAULT 1.0, -- Percentage weight
    MaxScore DECIMAL(5,2) NOT NULL DEFAULT 5.0,
    CriteriaType NVARCHAR(20) CHECK (CriteriaType IN ('Quantitative', 'Qualitative', 'Behavioral')),
    SortOrder INT,
    IsActive BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_PerformanceCriteria_Template FOREIGN KEY (TemplateID) REFERENCES HR.PerformanceTemplates(TemplateID)
)
GO

-- Performance evaluation cycles
CREATE TABLE HR.PerformanceCycles (
    CycleID INT IDENTITY(1,1) PRIMARY KEY,
    CycleName NVARCHAR(100) NOT NULL,
    TemplateID INT NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    SelfEvaluationDeadline DATE,
    ManagerEvaluationDeadline DATE,
    Status NVARCHAR(20) NOT NULL DEFAULT 'Draft' CHECK (Status IN ('Draft', 'Active', 'Completed', 'Cancelled')),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_PerformanceCycles_Template FOREIGN KEY (TemplateID) REFERENCES HR.PerformanceTemplates(TemplateID),
    CONSTRAINT FK_PerformanceCycles_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Individual performance evaluations
CREATE TABLE HR.PerformanceEvaluations (
    EvaluationID INT IDENTITY(1,1) PRIMARY KEY,
    CycleID INT NOT NULL,
    EmployeeID INT NOT NULL,
    EvaluatorID INT NOT NULL,
    EvaluationType NVARCHAR(20) NOT NULL CHECK (EvaluationType IN ('Self', 'Manager', 'Peer', '360')),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending' CHECK (Status IN ('Pending', 'In Progress', 'Completed', 'Approved')),
    OverallScore DECIMAL(5,2),
    OverallRating NVARCHAR(20),
    Strengths NVARCHAR(MAX),
    AreasForImprovement NVARCHAR(MAX),
    Goals NVARCHAR(MAX),
    Comments NVARCHAR(MAX),
    SubmittedDate DATETIME2,
    ApprovedDate DATETIME2,
    ApprovedBy INT,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_PerformanceEvaluations_Cycle FOREIGN KEY (CycleID) REFERENCES HR.PerformanceCycles(CycleID),
    CONSTRAINT FK_PerformanceEvaluations_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_PerformanceEvaluations_Evaluator FOREIGN KEY (EvaluatorID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_PerformanceEvaluations_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES HR.Employees(EmployeeID)
)
GO

-- Performance evaluation scores for each criteria
CREATE TABLE HR.PerformanceScores (
    ScoreID INT IDENTITY(1,1) PRIMARY KEY,
    EvaluationID INT NOT NULL,
    CriteriaID INT NOT NULL,
    Score DECIMAL(5,2),
    Comments NVARCHAR(500),
    CONSTRAINT FK_PerformanceScores_Evaluation FOREIGN KEY (EvaluationID) REFERENCES HR.PerformanceEvaluations(EvaluationID),
    CONSTRAINT FK_PerformanceScores_Criteria FOREIGN KEY (CriteriaID) REFERENCES HR.PerformanceCriteria(CriteriaID),
    CONSTRAINT UQ_PerformanceScores_EvaluationCriteria UNIQUE (EvaluationID, CriteriaID)
)
GO

-- =====================================================
-- Leave Management System
-- =====================================================

-- Leave types configuration
CREATE TABLE HR.LeaveTypes (
    LeaveTypeID INT IDENTITY(1,1) PRIMARY KEY,
    LeaveTypeName NVARCHAR(50) NOT NULL,
    LeaveTypeNameEnglish NVARCHAR(50),
    Description NVARCHAR(255),
    MaxDaysPerYear INT,
    MaxConsecutiveDays INT,
    RequiresApproval BIT NOT NULL DEFAULT 1,
    RequiresDocumentation BIT NOT NULL DEFAULT 0,
    IsPaid BIT NOT NULL DEFAULT 1,
    CarryForward BIT NOT NULL DEFAULT 0,
    MaxCarryForwardDays INT,
    AccrualRate DECIMAL(5,2), -- Days per month
    Gender NVARCHAR(10) CHECK (Gender IN ('All', 'Male', 'Female')),
    MinServiceMonths INT DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_LeaveTypes_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Employee leave balances
CREATE TABLE HR.LeaveBalances (
    BalanceID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    LeaveTypeID INT NOT NULL,
    Year INT NOT NULL,
    EntitledDays DECIMAL(5,2) NOT NULL DEFAULT 0,
    UsedDays DECIMAL(5,2) NOT NULL DEFAULT 0,
    RemainingDays AS (EntitledDays - UsedDays) PERSISTED,
    CarriedForwardDays DECIMAL(5,2) NOT NULL DEFAULT 0,
    LastUpdated DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_LeaveBalances_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_LeaveBalances_LeaveType FOREIGN KEY (LeaveTypeID) REFERENCES HR.LeaveTypes(LeaveTypeID),
    CONSTRAINT UQ_LeaveBalances_EmployeeTypeYear UNIQUE (EmployeeID, LeaveTypeID, Year)
)
GO

-- Leave requests
CREATE TABLE HR.LeaveRequests (
    RequestID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    LeaveTypeID INT NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    TotalDays DECIMAL(5,2) NOT NULL,
    Reason NVARCHAR(500),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending' CHECK (Status IN ('Pending', 'Approved', 'Rejected', 'Cancelled')),
    RequestDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ApprovedBy INT,
    ApprovedDate DATETIME2,
    RejectionReason NVARCHAR(500),
    EmergencyContact NVARCHAR(100),
    EmergencyPhone NVARCHAR(20),
    AttachmentPath NVARCHAR(255),
    Comments NVARCHAR(500),
    CONSTRAINT FK_LeaveRequests_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_LeaveRequests_LeaveType FOREIGN KEY (LeaveTypeID) REFERENCES HR.LeaveTypes(LeaveTypeID),
    CONSTRAINT FK_LeaveRequests_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES HR.Employees(EmployeeID)
)
GO

-- Leave approval workflow
CREATE TABLE HR.LeaveApprovalWorkflow (
    WorkflowID INT IDENTITY(1,1) PRIMARY KEY,
    RequestID INT NOT NULL,
    ApproverID INT NOT NULL,
    ApprovalLevel INT NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending' CHECK (Status IN ('Pending', 'Approved', 'Rejected')),
    Comments NVARCHAR(500),
    ActionDate DATETIME2,
    CONSTRAINT FK_LeaveApprovalWorkflow_Request FOREIGN KEY (RequestID) REFERENCES HR.LeaveRequests(RequestID),
    CONSTRAINT FK_LeaveApprovalWorkflow_Approver FOREIGN KEY (ApproverID) REFERENCES HR.Employees(EmployeeID)
)
GO
