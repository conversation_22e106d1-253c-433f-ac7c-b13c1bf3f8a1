-- =====================================================
-- El Dawliya International System - Data Migration Script
-- Version: 2.0
-- Date: 2025-01-30
-- Description: Migrate existing data to enhanced schema
-- =====================================================

-- Enable IDENTITY_INSERT for data migration
SET IDENTITY_INSERT Security.Users ON
SET IDENTITY_INSERT HR.Departments ON
SET IDENTITY_INSERT HR.Positions ON
SET IDENTITY_INSERT HR.Employees ON

-- =====================================================
-- Migrate Users Data
-- =====================================================

-- First, create default system roles
INSERT INTO Security.Roles (RoleName, RoleDescription, IsSystemRole, IsActive)
VALUES
    ('System Administrator', 'Full system access', 1, 1),
    ('HR Manager', 'HR module full access', 0, 1),
    ('HR Employee', 'HR module limited access', 0, 1),
    ('Employee', 'Self-service access only', 0, 1),
    ('Manager', 'Department management access', 0, 1)
GO

-- Migrate existing users from Users_Login table
INSERT INTO Security.Users (
    UserID, Username, Email, PasswordHash, PasswordSalt,
    FirstName, LastName, IsActive, CreatedDate, EmployeeID
)
SELECT
    UserID,
    UserName,
    UserName + '@eldawliya.com' AS Email, -- Generate email
    UserPassword AS PasswordHash, -- Note: These need to be re-hashed with proper security
    NEWID() AS PasswordSalt, -- Generate new salt
    COALESCE(SUBSTRING(UserName, 1, CHARINDEX(' ', UserName + ' ') - 1), UserName) AS FirstName,
    COALESCE(SUBSTRING(UserName, CHARINDEX(' ', UserName + ' ') + 1, LEN(UserName)), '') AS LastName,
    1 AS IsActive,
    GETDATE() AS CreatedDate,
    NULL AS EmployeeID -- Will be linked later
FROM Users_Login
WHERE UserName IS NOT NULL AND UserName != ''
GO

-- Assign default roles to migrated users
INSERT INTO Security.UserRoles (UserID, RoleID, AssignedDate, IsActive)
SELECT
    u.UserID,
    CASE
        WHEN ul.User_Group = 'Admin' OR ul.Allows_Settings = 1 THEN 1 -- System Administrator
        WHEN ul.Allows_HR = 1 THEN 2 -- HR Manager
        ELSE 4 -- Employee
    END AS RoleID,
    GETDATE() AS AssignedDate,
    1 AS IsActive
FROM Security.Users u
INNER JOIN Users_Login ul ON u.UserID = ul.UserID
GO

-- =====================================================
-- Migrate Department Data
-- =====================================================

-- Check if Tbl_Department exists and migrate
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Tbl_Department')
BEGIN
    INSERT INTO HR.Departments (
        DepartmentID, DepartmentCode, DepartmentName,
        IsActive, CreatedDate, CreatedBy
    )
    SELECT
        Dept_Code AS DepartmentID,
        'DEPT' + RIGHT('000' + CAST(Dept_Code AS VARCHAR), 3) AS DepartmentCode,
        Dept_Name AS DepartmentName,
        1 AS IsActive,
        GETDATE() AS CreatedDate,
        1 AS CreatedBy -- System user
    FROM Tbl_Department
    WHERE Dept_Name IS NOT NULL AND Dept_Name != ''
END
GO

-- =====================================================
-- Migrate Job/Position Data
-- =====================================================

-- Check if Tbl_Jop exists and migrate
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Tbl_Jop')
BEGIN
    INSERT INTO HR.Positions (
        PositionID, PositionCode, PositionTitle, DepartmentID,
        IsActive, CreatedDate, CreatedBy
    )
    SELECT
        Jop_Code AS PositionID,
        'POS' + RIGHT('000' + CAST(Jop_Code AS VARCHAR), 3) AS PositionCode,
        Jop_Name AS PositionTitle,
        1 AS DepartmentID, -- Default department, update manually if needed
        1 AS IsActive,
        GETDATE() AS CreatedDate,
        1 AS CreatedBy
    FROM Tbl_Jop
    WHERE Jop_Name IS NOT NULL AND Jop_Name != ''
END
GO

-- =====================================================
-- Migrate Employee Data
-- =====================================================

-- Check if Tbl_Employee exists and migrate
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Tbl_Employee')
BEGIN
    INSERT INTO HR.Employees (
        EmployeeID, EmployeeCode, FirstName, LastName, FullNameArabic,
        Phone1, Address, NationalID, DateOfBirth, PlaceOfBirth,
        Gender, MaritalStatus, Nationality, EmploymentStatus,
        HireDate, DepartmentID, PositionID, BaseSalary,
        IsActive, CreatedDate, CreatedBy
    )
    SELECT
        Emp_ID AS EmployeeID,
        'EMP' + RIGHT('0000' + CAST(Emp_ID AS VARCHAR), 4) AS EmployeeCode,
        -- Split full name into first and last name
        CASE
            WHEN CHARINDEX(' ', LTRIM(RTRIM(Emp_Full_Name))) > 0
            THEN LEFT(LTRIM(RTRIM(Emp_Full_Name)), CHARINDEX(' ', LTRIM(RTRIM(Emp_Full_Name))) - 1)
            ELSE LTRIM(RTRIM(Emp_Full_Name))
        END AS FirstName,
        CASE
            WHEN CHARINDEX(' ', LTRIM(RTRIM(Emp_Full_Name))) > 0
            THEN SUBSTRING(LTRIM(RTRIM(Emp_Full_Name)), CHARINDEX(' ', LTRIM(RTRIM(Emp_Full_Name))) + 1, LEN(Emp_Full_Name))
            ELSE ''
        END AS LastName,
        Emp_Full_Name AS FullNameArabic,
        Emp_Phone1 AS Phone1,
        Emp_Address AS Address,
        National_ID AS NationalID,
        Date_Birth AS DateOfBirth,
        Place_Birth AS PlaceOfBirth,
        'Male' AS Gender, -- Default, update manually if needed
        COALESCE(Emp_Marital_Status, 'Single') AS MaritalStatus,
        COALESCE(Emp_Nationality, 'Egyptian') AS Nationality,
        CASE
            WHEN Working_Condition = 'سارى' THEN 'Active'
            WHEN Working_Condition = 'غير سارى' THEN 'Inactive'
            ELSE 'Active'
        END AS EmploymentStatus,
        COALESCE(Emp_Date_Hiring, GETDATE()) AS HireDate,
        COALESCE(Dept_Code, 1) AS DepartmentID,
        COALESCE(Jop_Code, 1) AS PositionID,
        0 AS BaseSalary, -- Default, update manually
        1 AS IsActive,
        GETDATE() AS CreatedDate,
        1 AS CreatedBy
    FROM Tbl_Employee
    WHERE Emp_Full_Name IS NOT NULL AND Emp_Full_Name != ''
END
GO

-- =====================================================
-- Migrate Attendance Data
-- =====================================================

-- Check if Tbl_Car_Attendance exists and migrate
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Tbl_Car_Attendance')
BEGIN
    -- First, create a default attendance device
    INSERT INTO HR.AttendanceDevices (DeviceName, DeviceIP, Location, IsActive, CreatedDate)
    VALUES ('Legacy System', '127.0.0.1', 'Main Office', 1, GETDATE())

    DECLARE @DefaultDeviceID INT = SCOPE_IDENTITY()

    -- Migrate attendance records
    INSERT INTO HR.AttendanceRecords (
        EmployeeID, AttendanceDate, CheckInTime, CheckOutTime,
        AttendanceStatus, DeviceID, IsManualEntry, Notes, CreatedDate
    )
    SELECT
        -- Map Car_ID to EmployeeID (assuming Car_ID corresponds to employee)
        COALESCE(e.EmployeeID, 1) AS EmployeeID,
        Attendance_Date AS AttendanceDate,
        CASE
            WHEN Check_In IS NOT NULL
            THEN CAST(Attendance_Date AS DATETIME) + CAST(Check_In AS DATETIME)
            ELSE NULL
        END AS CheckInTime,
        CASE
            WHEN Check_Out IS NOT NULL
            THEN CAST(Attendance_Date AS DATETIME) + CAST(Check_Out AS DATETIME)
            ELSE NULL
        END AS CheckOutTime,
        CASE
            WHEN Attendance_Code = '1' THEN 'Present'
            WHEN Attendance_Code = '2' THEN 'Absent'
            ELSE 'Present'
        END AS AttendanceStatus,
        @DefaultDeviceID AS DeviceID,
        1 AS IsManualEntry, -- Mark as manual since it's migrated data
        COALESCE(Notes, '') AS Notes,
        GETDATE() AS CreatedDate
    FROM Tbl_Car_Attendance ca
    LEFT JOIN HR.Employees e ON ca.Car_ID = e.EmployeeID -- Adjust this mapping as needed
    WHERE Attendance_Date IS NOT NULL
END
GO

-- =====================================================
-- Create Default System Settings
-- =====================================================

INSERT INTO dbo.SystemSettings (SettingKey, SettingValue, SettingDescription, Category, IsSystemSetting)
VALUES
    ('PasswordMinLength', '8', 'Minimum password length', 'Security', 1),
    ('PasswordRequireUppercase', 'true', 'Require uppercase letters in password', 'Security', 1),
    ('PasswordRequireLowercase', 'true', 'Require lowercase letters in password', 'Security', 1),
    ('PasswordRequireNumbers', 'true', 'Require numbers in password', 'Security', 1),
    ('PasswordRequireSpecialChars', 'true', 'Require special characters in password', 'Security', 1),
    ('PasswordExpiryDays', '90', 'Password expiry period in days', 'Security', 1),
    ('MaxFailedLoginAttempts', '5', 'Maximum failed login attempts before lockout', 'Security', 1),
    ('SessionTimeoutMinutes', '30', 'Session timeout in minutes', 'Security', 1),
    ('CompanyName', 'El Dawliya International', 'Company name', 'General', 1),
    ('DefaultCurrency', 'EGP', 'Default currency code', 'General', 1),
    ('WorkingDaysPerWeek', '5', 'Standard working days per week', 'HR', 1),
    ('WorkingHoursPerDay', '8', 'Standard working hours per day', 'HR', 1),
    ('OvertimeRate', '1.5', 'Overtime rate multiplier', 'Payroll', 1),
    ('TaxRate', '0.14', 'Default tax rate', 'Payroll', 1)
GO

-- =====================================================
-- Create Default Leave Types
-- =====================================================

INSERT INTO HR.LeaveTypes (
    LeaveTypeName, LeaveTypeNameEnglish, Description,
    MaxDaysPerYear, RequiresApproval, IsPaid, CreatedDate, CreatedBy
)
VALUES
    ('إجازة سنوية', 'Annual Leave', 'Annual vacation leave', 21, 1, 1, GETDATE(), 1),
    ('إجازة مرضية', 'Sick Leave', 'Medical leave', 30, 1, 1, GETDATE(), 1),
    ('إجازة طارئة', 'Emergency Leave', 'Emergency leave', 7, 1, 1, GETDATE(), 1),
    ('إجازة أمومة', 'Maternity Leave', 'Maternity leave', 90, 1, 1, GETDATE(), 1),
    ('إجازة أبوة', 'Paternity Leave', 'Paternity leave', 3, 1, 1, GETDATE(), 1),
    ('إجازة بدون راتب', 'Unpaid Leave', 'Leave without pay', 365, 1, 0, GETDATE(), 1)
GO

-- Disable IDENTITY_INSERT
SET IDENTITY_INSERT Security.Users OFF
SET IDENTITY_INSERT HR.Departments OFF
SET IDENTITY_INSERT HR.Positions OFF
SET IDENTITY_INSERT HR.Employees OFF

-- =====================================================
-- Update User-Employee Links
-- =====================================================

-- Link users to employees based on name matching (manual adjustment may be needed)
UPDATE Security.Users
SET EmployeeID = e.EmployeeID
FROM Security.Users u
INNER JOIN HR.Employees e ON
    (u.FirstName + ' ' + u.LastName) = e.FullNameArabic
    OR u.Username = e.EmployeeCode
WHERE u.EmployeeID IS NULL

GO

PRINT 'Data migration completed successfully!'
PRINT 'Please review the migrated data and make necessary adjustments.'
PRINT 'Important: Update password hashes with proper security implementation.'