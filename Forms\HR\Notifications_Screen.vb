﻿Imports System.Data.SqlClient

Public Class Notifications_Screen
    Public ConStr As String = GetConnectionString()
    Public Con As New SqlConnection(ConStr)
    Public dt_Notifications As New DataTable
    Dim ds As New DataSet
    Private columnsToExport As Object

    Private Function GetColumnHeaderName(columnName As String) As String
        Select Case columnName
            Case "Emp_ID" : Return "الكود"
            Case "Emp_First_Name" : Return "الاسم الأول"
            Case "Emp_Second_Name" : Return "الاسم الثاني"
            Case "Emp_Full_Name" : Return "اسم الموظف"
            Case "Emp_Name_English" : Return "الاسم باللغة الإنجليزية"
            Case "Emp_Phone1" : Return "رقم التليفون الأول"
            Case "Emp_Phone2" : Return "رقم التليفون الثاني"
            Case "Emp_Address" : Return "العنوان"
            Case "Emp_Marital_Status" : Return "الحالة الاجتماعية"
            Case "Emp_Nationality" : Return "الجنسية"
            Case "People_With_Special_Needs" : Return "أشخاص ذوي احتياجات خاصة"
            Case "National_ID" : Return "الرقم القومي"
            Case "Date_Birth" : Return "تاريخ الميلاد"
            Case "Place_Birth" : Return "مكان الميلاد"
            Case "Emp_Image" : Return "صورة الموظف"
            Case "Emp_Type" : Return "نوع الموظف"
            Case "Working_Condition" : Return "حالة العمل"
            Case "Dept_Code" : Return "كود القسم"
            Case "Dept_Name" : Return "اسم القسم"
            Case "Jop_Code" : Return "كود الوظيفة"
            Case "Jop_Name" : Return "اسم الوظيفة"
            Case "Emp_Date_Hiring" : Return "تاريخ التعيين"
            Case "Emp_Car" : Return "السيارة"
            Case "Car_Ride_Time" : Return "وقت ركوب السيارة"
            Case "Car_Pick_Up_Point" : Return "نقطة ركوب السيارة"
            Case "Insurance_Status" : Return "حالة التأمين"
            Case "Jop_Code_insurance" : Return "كود المهنة في التأمين"
            Case "Jop_Name_insurance" : Return "اسم المهنة في التأمين"
            Case "Health_Card" : Return "بطاقة الصحية"
            Case "Health_Card_Number" : Return "رقم بطاقة الصحية"
            Case "Health_Card_Start_Date" : Return "تاريخ بدء بطاقة الصحية"
            Case "Health_Card_Renewal_Date" : Return "تاريخ تجديد البطاقة الصحية"
            Case "Health_Card_Expiration_Date" : Return "تاريخ انتهاء البطاقة الصحية"
            Case "Number_Insurance" : Return "رقم التأمين"
            Case "Date_Insurance_Start" : Return "تاريخ بدء التأمين"
            Case "Insurance_Salary" : Return "راتب التأمين"
            Case "Percentage_Insurance_Payable" : Return "نسبة التأمين المدفوعة"
            Case "Due_Insurance_Amount" : Return "المبلغ المستحق للتأمين"
            Case "Form_S1" : Return "نموذج S1"
            Case "Confirmation_Insurance_Entry" : Return "تأكيد دخول التأمين"
            Case "Delivery_Date_S1" : Return "تاريخ تسليم نموذج S1"
            Case "Receive_Date_S1" : Return "تاريخ استلام نموذج S1"
            Case "Form_S6" : Return "نموذج S6"
            Case "Delivery_Date_S6" : Return "تاريخ تسليم نموذج S6"
            Case "Receive_Date_S6" : Return "تاريخ استلام نموذج S6"
            Case "Hiring_Date_Health_Card" : Return "تاريخ التعيين للبطاقة الصحية"
            Case "Skill_level_measurement_certificate" : Return "شهادة قياس مستوى المهارة"
            Case "The_health_card_remains_expire" : Return "بتقى على انتهاء البطاقة الصحية"
            Case "End_date_probationary_period" : Return "تاريخ انتهاء فترة الاختبار"
            Case "CurrentWeekShift" : Return "وردية الأسبوع الحالي"
            Case "NextWeekShift" : Return "وردية الأسبوع القادم"
            Case "Friday_Operation" : Return "تشغيل يوم الجمعة"
            Case "Shift_Type" : Return "نوع الوردية"
            Case "Entrance_Date_S1" : Return "تاريخ دخول نموذج S1"
            Case "Entrance_Number_S1" : Return "رقم دخول نموذج S1"
            Case "Remaining_Contract_Renewal" : Return "تجديد العقد المتبقي"
            Case "Medical_Exam_Form_Submission" : Return "تقديم نموذج الفحص الطبي"
            Case "Years_Since_Contract_Start" : Return "يعمل فى الشركة منذ"
            Case "Contract_Renewal_Date" : Return "تاريخ تجديد العقد"
            Case "Contract_Expiry_Date" : Return "تاريخ انتهاء العقد"
            Case "Insurance_Code" : Return "كود التأمين"
            Case "Personal_ID_Expiry_Date" : Return "تاريخ انتهاء بطاقة الهوية الشخصية"
            Case "Contract_Renewal_Month" : Return "شهر تجديد العقد"
            Case "Military_Service_Certificate" : Return "شهادة الخدمة العسكرية"
            Case "Qualification_Certificate" : Return "شهادة التأهيل"
            Case "Birth_Certificate" : Return "شهادة الميلاد"
            Case "Insurance_Printout" : Return "طباعة التأمين"
            Case "ID_Card_Photo" : Return "صورة بطاقة الهوية"
            Case "Personal_Photos" : Return "صور شخصية"
            Case "Employment_Contract" : Return "عقد العمل"
            Case "Medical_Exam_Form" : Return "نموذج الفحص الطبي"
            Case "Criminal_Record_Check" : Return "فحص السجل الجنائي"
            Case "Social_Status_Report" : Return "تقرير الحالة الاجتماعية"
            Case "Work_Heel" : Return "العمل على الكعب"
            Case "Heel_Work_Number" : Return "رقم العمل على الكعب"
            Case "Heel_Work_Registration_Date" : Return "اخر تاريخ لتسجيل كعب العمل"
            Case "Heel_Work_Recipient" : Return "مستلم العمل على الكعب"
            Case "Heel_Work_Recipient_Address" : Return "عنوان مستلم العمل على الكعب"
            Case "Entrance_Number_S6" : Return "رقم دخول نموذج S6"
            Case "Entrance_Date_S6" : Return "تاريخ دخول نموذج S6"
            Case "Shift_paper" : Return "ورق الورديات"
            Case "Age" : Return "العمر"
            Case "Date_Resignation" : Return "تاريخ الاستقالة"
            Case "Reason_Resignation" : Return "سبب الاستقالة"
            Case "Mother_Name" : Return "اسم الأم"
            Case Else : Return columnName
        End Select
    End Function

    Public Sub Select_Personal_ID_Expiry_Date(dgv As DataGridView)
        Dim da As New SqlDataAdapter
        dt_Notifications.Clear()
        da = New SqlDataAdapter("Select_Personal_ID_Expiry_Date", Con)
        da.SelectCommand.CommandType = CommandType.StoredProcedure
        Dim daysParam As New SqlParameter("@Days", SqlDbType.Int)
        daysParam.Value = Convert.ToInt32(Txt_Days.Text)
        da.SelectCommand.Parameters.Add(daysParam)
        da.Fill(dt_Notifications)
        dgv.DataSource = dt_Notifications
        Try
            If Con.State = ConnectionState.Closed Then
                Con.Open()
            End If

            For Each column As DataGridViewColumn In dgv.Columns
                column.HeaderText = GetColumnHeaderName(column.Name)
            Next
            dgv.Columns("Emp_Full_Name").AutoSizeMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show("خطأ: " & ex.Message)
        End Try
        Con.Close()
    End Sub

    Public Sub Select_Health_Card_Expiration_Date(dgv As DataGridView)
        Dim da As New SqlDataAdapter
        dt_Notifications.Clear()
        da = New SqlDataAdapter("Select_Health_Card_Expiration_Date", Con)
        da.SelectCommand.CommandType = CommandType.StoredProcedure
        Dim daysParam As New SqlParameter("@Days", SqlDbType.Int)
        daysParam.Value = Convert.ToInt32(Txt_Days.Text)
        da.SelectCommand.Parameters.Add(daysParam)
        da.Fill(dt_Notifications)
        dgv.DataSource = dt_Notifications
        Try
            If Con.State = ConnectionState.Closed Then
                Con.Open()
            End If

            For Each column As DataGridViewColumn In dgv.Columns
                column.HeaderText = GetColumnHeaderName(column.Name)
            Next
            dgv.Columns("Emp_Full_Name").AutoSizeMode = DataGridViewAutoSizeColumnsMode.Fill
        Catch ex As Exception
            MessageBox.Show("خطأ: " & ex.Message)
        End Try
        Con.Close()
    End Sub
    Private Sub Btn_Export_Excel_Click(sender As Object, e As EventArgs) Handles Btn_Export_Excel.Click
        Dim saveDialog As New SaveFileDialog()
        saveDialog.Filter = "Excel Files|*.xlsx;*.xls"
        saveDialog.Title = "E:\El Dawliya International System\Excel"

        Select Case Me.Combo_Notifications.Text
            Case "انتهاء الشهادة الصحية"
                Dim columnsToExport As New List(Of String)
                If saveDialog.ShowDialog() = DialogResult.OK Then
                    ' قائمة بأسماء الأعمدة التي تريد تصديرها
                    ' اضف أسماء الأعمدة التي تريد تصديرها إلى القائمة
                    columnsToExport.Add("Emp_ID")
                    columnsToExport.Add("Emp_Full_Name")
                    columnsToExport.Add("Health_Card")
                    columnsToExport.Add("Health_Card_Start_Date")
                    columnsToExport.Add("Health_Card_Number")
                    columnsToExport.Add("Health_Card_Renewal_Date")
                    columnsToExport.Add("Health_Card_Expiration_Date")
                    ' استدعاء الدالة ExportToExcel مع تمرير قائمة الأعمدة المرغوب تصديرها
                    ExportToExcel(Dgv_Notifications, saveDialog.FileName, columnsToExport)
                End If
            Case "انتهاء بطاقة الرقم القومى"
                Dim columnsToExport As New List(Of String)
                If saveDialog.ShowDialog() = DialogResult.OK Then
                    ' قائمة بأسماء الأعمدة التي تريد تصديرها
                    ' اضف أسماء الأعمدة التي تريد تصديرها إلى القائمة
                    columnsToExport.Add("Emp_ID")
                    columnsToExport.Add("Emp_Full_Name")
                    columnsToExport.Add("Personal_ID_Expiry_Date")
                    columnsToExport.Add("Emp_Nationality")
                    ' استدعاء الدالة ExportToExcel مع تمرير قائمة الأعمدة المرغوب تصديرها
                    ExportToExcel(Dgv_Notifications, saveDialog.FileName, columnsToExport)
                End If
        End Select
    End Sub

    Private Sub Notifications_Screen_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        FormResizer.InitializeForm(Me)
        BindingSource1.DataSource = dt_Notifications.DefaultView
    End Sub

    Private Sub Notifications_Screen_Resize(sender As Object, e As EventArgs) Handles MyBase.Resize
        FormResizer.ResizeForm(Me)
    End Sub

    Private Sub ToolStripButton1_Click(sender As Object, e As EventArgs) Handles ToolStripButton1.Click
        Select Case Combo_Notifications.Text
            Case "انتهاء الشهادة الصحية"
                Lbl_Notifications_Name.Text = "انتهاء الشهادة الصحية"
                dt_Notifications.Clear()
                dt_Notifications.Columns.Clear()
                Select_Health_Card_Expiration_Date(Dgv_Notifications)
                Lbl_Count.Text = Dgv_Notifications.RowCount.ToString()
            Case "انتهاء بطاقة الرقم القومى"
                Lbl_Notifications_Name.Text = "انتهاء بطاقة الرقم القومى"
                dt_Notifications.Clear()
                dt_Notifications.Columns.Clear()
                Select_Personal_ID_Expiry_Date(Dgv_Notifications)
                Lbl_Count.Text = Dgv_Notifications.RowCount.ToString()
        End Select
    End Sub
    Private Sub dgv_Tbl_Employee_FilterStringChanged(sender As Object, e As EventArgs) Handles Dgv_Notifications.FilterStringChanged
        BindingSource1.Filter = Dgv_Notifications.FilterString
        Dgv_Notifications.DataSource = BindingSource1
        Lbl_Count.Text = Dgv_Notifications.RowCount.ToString()
    End Sub

    Private Sub dgv_Tbl_Employee_SortStringChanged(sender As Object, e As EventArgs) Handles Dgv_Notifications.SortStringChanged
        BindingSource1.Sort = Dgv_Notifications.SortString
        Dgv_Notifications.DataSource = BindingSource1
        Lbl_Count.Text = Dgv_Notifications.RowCount.ToString()
    End Sub
End Class



'Imports System.Data.SqlClient
'Imports System.IO
'Imports Microsoft.Office.Interop.Excel
'Imports System.Configuration
'Imports System.ComponentModel

'Public Class Notifications_Screen
'    Inherits Form

'#Region "Properties and Fields"
'    Private ReadOnly _connectionString As String = ConfigurationManager.ConnectionStrings("DefaultConnection").ConnectionString
'    Private ReadOnly _notificationsDataTable As New DataTable
'    Private ReadOnly _bindingSource As New BindingSource
'    Private ReadOnly _dataAdapter As SqlDataAdapter
'    Private _currentNotificationType As NotificationType = NotificationType.None

'    ' UI Controls (assumed to exist in form)
'    Private WithEvents Dgv_Notifications As DataGridView
'    Private WithEvents Combo_Notifications As ComboBox
'    Private WithEvents Txt_Days As TextBox
'    Private WithEvents Btn_Export_Excel As Button
'    Private WithEvents Btn_Refresh As Button
'    Private WithEvents Lbl_Count As Label
'    Private WithEvents Lbl_Notifications_Name As Label
'    Private WithEvents ToolStripButton1 As ToolStripButton
'    Private WithEvents ProgressBar1 As ProgressBar
'    Private WithEvents BackgroundWorker1 As BackgroundWorker
'#End Region

'#Region "Enums"
'    Public Enum NotificationType
'        None = 0
'        HealthCardExpiry = 1
'        PersonalIDExpiry = 2
'        ContractExpiry = 3
'        InsuranceExpiry = 4
'    End Enum
'#End Region

'#Region "Constructor and Initialization"
'    Public Sub New()
'        InitializeComponent()
'        InitializeForm()
'    End Sub

'    Private Sub InitializeForm()
'        Try
'            SetupDataGridView()
'            SetupComboBox()
'            SetupBindingSource()
'            SetupBackgroundWorker()
'            LoadInitialData()
'        Catch ex As Exception
'            HandleError("خطأ في تهيئة النموذج", ex)
'        End Try
'    End Sub

'    Private Sub SetupDataGridView()
'        With dgv_Notifications
'            .AllowUserToAddRows = False
'            .AllowUserToDeleteRows = False
'            .ReadOnly = True
'            .SelectionMode = DataGridViewSelectionMode.FullRowSelect
'            .AutoGenerateColumns = True
'            .EnableHeadersVisualStyles = False
'            .AlternatingRowsDefaultCellStyle.BackColor = Color.LightGray
'            .DefaultCellStyle.SelectionBackColor = Color.DarkBlue
'            .DefaultCellStyle.SelectionForeColor = Color.White
'        End With
'    End Sub

'    Private Sub SetupComboBox()
'        Combo_Notifications.Items.Clear()
'        Combo_Notifications.Items.AddRange(New String() {
'            "انتهاء الشهادة الصحية",
'            "انتهاء بطاقة الرقم القومى",
'            "انتهاء العقود",
'            "انتهاء التأمين"
'        })
'        Combo_Notifications.SelectedIndex = 0
'    End Sub

'    Private Sub SetupBindingSource()
'        _bindingSource.DataSource = _notificationsDataTable
'        dgv_Notifications.DataSource = _bindingSource
'    End Sub

'    Private Sub SetupBackgroundWorker()
'        BackgroundWorker1.WorkerReportsProgress = True
'        BackgroundWorker1.WorkerSupportsCancellation = True
'    End Sub

'    Private Sub LoadInitialData()
'        Txt_Days.Text = "30" ' Default value
'        _currentNotificationType = NotificationType.HealthCardExpiry
'        LoadNotificationData()
'    End Sub
'#End Region

'#Region "Column Header Mapping"
'    Private Function GetColumnHeaderName(columnName As String) As String
'        Dim headerMappings As New Dictionary(Of String, String) From {
'            {"Emp_ID", "الكود"},
'            {"Emp_First_Name", "الاسم الأول"},
'            {"Emp_Second_Name", "الاسم الثاني"},
'            {"Emp_Full_Name", "اسم الموظف"},
'            {"Emp_Name_English", "الاسم باللغة الإنجليزية"},
'            {"Emp_Phone1", "رقم التليفون الأول"},
'            {"Emp_Phone2", "رقم التليفون الثاني"},
'            {"Emp_Address", "العنوان"},
'            {"Emp_Marital_Status", "الحالة الاجتماعية"},
'            {"Emp_Nationality", "الجنسية"},
'            {"People_With_Special_Needs", "أشخاص ذوي احتياجات خاصة"},
'            {"National_ID", "الرقم القومي"},
'            {"Date_Birth", "تاريخ الميلاد"},
'            {"Place_Birth", "مكان الميلاد"},
'            {"Emp_Image", "صورة الموظف"},
'            {"Emp_Type", "نوع الموظف"},
'            {"Working_Condition", "حالة العمل"},
'            {"Dept_Code", "كود القسم"},
'            {"Dept_Name", "اسم القسم"},
'            {"Jop_Code", "كود الوظيفة"},
'            {"Jop_Name", "اسم الوظيفة"},
'            {"Emp_Date_Hiring", "تاريخ التعيين"},
'            {"Health_Card", "بطاقة الصحية"},
'            {"Health_Card_Number", "رقم بطاقة الصحية"},
'            {"Health_Card_Start_Date", "تاريخ بدء بطاقة الصحية"},
'            {"Health_Card_Renewal_Date", "تاريخ تجديد البطاقة الصحية"},
'            {"Health_Card_Expiration_Date", "تاريخ انتهاء البطاقة الصحية"},
'            {"Personal_ID_Expiry_Date", "تاريخ انتهاء بطاقة الهوية الشخصية"},
'            {"Contract_Expiry_Date", "تاريخ انتهاء العقد"},
'            {"Insurance_Expiry_Date", "تاريخ انتهاء التأمين"},
'            {"Days_Remaining", "الأيام المتبقية"},
'            {"Status", "الحالة"}
'        }

'        Return If(headerMappings.ContainsKey(columnName), headerMappings(columnName), columnName)
'    End Function
'#End Region

'#Region "Data Loading Methods"
'    Private Sub LoadNotificationData()
'        If IsInputValid() Then
'            ShowProgress(True)
'            BackgroundWorker1.RunWorkerAsync()
'        End If
'    End Sub

'    Private Function IsInputValid() As Boolean
'        If String.IsNullOrWhiteSpace(Txt_Days.Text) Then
'            MessageBox.Show("يرجى إدخال عدد الأيام", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
'            Txt_Days.Focus()
'            Return False
'        End If

'        Dim days As Integer
'        If Not Integer.TryParse(Txt_Days.Text, days) OrElse days < 1 OrElse days > 365 Then
'            MessageBox.Show("يرجى إدخال عدد صحيح للأيام (1-365)", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
'            Txt_Days.Focus()
'            Return False
'        End If

'        Return True
'    End Function

'    Private Sub BackgroundWorker1_DoWork(sender As Object, e As DoWorkEventArgs) Handles BackgroundWorker1.DoWork
'        Try
'            Dim worker As BackgroundWorker = CType(sender, BackgroundWorker)
'            worker.ReportProgress(10, "جاري تحضير البيانات...")

'            Using connection As New SqlConnection(_connectionString)
'                connection.Open()
'                worker.ReportProgress(30, "جاري الاتصال بقاعدة البيانات...")

'                Dim tempDataTable As New DataTable
'                Dim storedProcedure As String = GetStoredProcedureName(_currentNotificationType)

'                Using command As New SqlCommand(storedProcedure, connection)
'                    command.CommandType = CommandType.StoredProcedure
'                    command.Parameters.AddWithValue("@Days", Convert.ToInt32(Txt_Days.Text))
'                    command.CommandTimeout = 60

'                    worker.ReportProgress(50, "جاري تنفيذ الاستعلام...")

'                    Using adapter As New SqlDataAdapter(command)
'                        adapter.Fill(tempDataTable)
'                    End Using
'                End Using

'                worker.ReportProgress(80, "جاري معالجة البيانات...")
'                e.Result = tempDataTable
'            End Using

'        Catch ex As Exception
'            e.Result = ex
'        End Try
'    End Sub

'    Private Sub BackgroundWorker1_ProgressChanged(sender As Object, e As ProgressChangedEventArgs) Handles BackgroundWorker1.ProgressChanged
'        ProgressBar1.Value = e.ProgressPercentage
'        If e.UserState IsNot Nothing Then
'            ' Display status message if needed
'        End If
'    End Sub

'    Private Sub BackgroundWorker1_RunWorkerCompleted(sender As Object, e As RunWorkerCompletedEventArgs) Handles BackgroundWorker1.RunWorkerCompleted
'        ShowProgress(False)

'        If TypeOf e.Result Is Exception Then
'            HandleError("خطأ في تحميل البيانات", CType(e.Result, Exception))
'        ElseIf TypeOf e.Result Is DataTable Then
'            Dim dataTable As DataTable = CType(e.Result, DataTable)
'            UpdateDataGridView(dataTable)
'        End If
'    End Sub

'    Private Function GetStoredProcedureName(notificationType As NotificationType) As String
'        Select Case notificationType
'            Case NotificationType.HealthCardExpiry
'                Return "Select_Health_Card_Expiration_Date"
'            Case NotificationType.PersonalIDExpiry
'                Return "Select_Personal_ID_Expiry_Date"
'            Case NotificationType.ContractExpiry
'                Return "Select_Contract_Expiry_Date"
'            Case NotificationType.InsuranceExpiry
'                Return "Select_Insurance_Expiry_Date"
'            Case Else
'                Throw New ArgumentException("نوع الإشعار غير مدعوم")
'        End Select
'    End Function

'    Private Sub UpdateDataGridView(dataTable As DataTable)
'        Try
'            _notificationsDataTable.Clear()
'            _notificationsDataTable.Columns.Clear()

'            For Each column As DataColumn In dataTable.Columns
'                _notificationsDataTable.Columns.Add(column.ColumnName, column.DataType)
'            Next

'            For Each row As DataRow In dataTable.Rows
'                _notificationsDataTable.ImportRow(row)
'            Next

'            ConfigureDataGridViewColumns()
'            UpdateRowCount()

'        Catch ex As Exception
'            HandleError("خطأ في تحديث البيانات", ex)
'        End Try
'    End Sub

'    Private Sub ConfigureDataGridViewColumns()
'        Try
'            For Each column As DataGridViewColumn In dgv_Notifications.Columns
'                column.HeaderText = GetColumnHeaderName(column.Name)

'                ' Configure specific columns
'                Select Case column.Name
'                    Case "Emp_Full_Name"
'                        column.AutoSizeMode = DataGridViewAutoSizeColumnsMode.Fill
'                    Case "Health_Card_Expiration_Date", "Personal_ID_Expiry_Date", "Contract_Expiry_Date"
'                        column.DefaultCellStyle.Format = "dd/MM/yyyy"
'                    Case "Days_Remaining"
'                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
'                        ' Color coding for urgency
'                        For Each row As DataGridViewRow In dgv_Notifications.Rows
'                            If row.Cells(column.Name).Value IsNot Nothing Then
'                                Dim daysRemaining As Integer = Convert.ToInt32(row.Cells(column.Name).Value)
'                                If daysRemaining <= 7 Then
'                                    row.Cells(column.Name).Style.BackColor = Color.Red
'                                    row.Cells(column.Name).Style.ForeColor = Color.White
'                                ElseIf daysRemaining <= 30 Then
'                                    row.Cells(column.Name).Style.BackColor = Color.Orange
'                                End If
'                            End If
'                        Next
'                End Select
'            Next
'        Catch ex As Exception
'            HandleError("خطأ في تكوين الأعمدة", ex)
'        End Try
'    End Sub
'#End Region

'#Region "Export Functionality"
'    Private Sub Btn_Export_Excel_Click(sender As Object, e As EventArgs) Handles Btn_Export_Excel.Click
'        Try
'            If _notificationsDataTable.Rows.Count = 0 Then
'                MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information)
'                Return
'            End If

'            Dim saveDialog As New SaveFileDialog() With {
'                .Filter = "Excel Files (*.xlsx)|*.xlsx|Excel Files (*.xls)|*.xls",
'                .Title = "حفظ ملف Excel",
'                .FileName = $"تقرير_{GetNotificationTypeName(_currentNotificationType)}_{DateTime.Now:yyyyMMdd_HHmmss}"
'            }

'            If saveDialog.ShowDialog() = DialogResult.OK Then
'                ShowProgress(True)
'                Dim exportColumns As List(Of String) = GetExportColumns(_currentNotificationType)

'                ' Run export in background thread
'                Dim exportWorker As New BackgroundWorker()
'                AddHandler exportWorker.DoWork, Sub(s, ev)
'                                                    ExportToExcel(saveDialog.FileName, exportColumns)
'                                                End Sub
'                AddHandler exportWorker.RunWorkerCompleted, Sub(s, ev)
'                                                                ShowProgress(False)
'                                                                If ev.Error IsNot Nothing Then
'                                                                    HandleError("خطأ في التصدير", ev.Error)
'                                                                Else
'                                                                    MessageBox.Show("تم تصدير البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
'                                                                    Process.Start(saveDialog.FileName)
'                                                                End If
'                                                            End Sub

'                exportWorker.RunWorkerAsync()
'            End If
'        Catch ex As Exception
'            HandleError("خطأ في عملية التصدير", ex)
'        End Try
'    End Sub

'    Private Function GetExportColumns(notificationType As NotificationType) As List(Of String)
'        Dim columns As New List(Of String)

'        ' Common columns
'        columns.AddRange({"Emp_ID", "Emp_Full_Name", "Dept_Name", "Jop_Name"})

'        ' Specific columns based on notification type
'        Select Case notificationType
'            Case NotificationType.HealthCardExpiry
'                columns.AddRange({"Health_Card", "Health_Card_Number", "Health_Card_Start_Date",
'                                "Health_Card_Renewal_Date", "Health_Card_Expiration_Date"})
'            Case NotificationType.PersonalIDExpiry
'                columns.AddRange({"National_ID", "Personal_ID_Expiry_Date", "Emp_Nationality"})
'            Case NotificationType.ContractExpiry
'                columns.AddRange({"Contract_Expiry_Date", "Emp_Date_Hiring"})
'            Case NotificationType.InsuranceExpiry
'                columns.AddRange({"Insurance_Expiry_Date", "Number_Insurance"})
'        End Select

'        Return columns
'    End Function

'    Private Sub ExportToExcel(fileName As String, columnsToExport As List(Of String))
'        Dim excelApp As Microsoft.Office.Interop.Excel.Application = Nothing
'        Dim workbook As Microsoft.Office.Interop.Excel.Workbook = Nothing
'        Dim worksheet As Microsoft.Office.Interop.Excel.Worksheet = Nothing

'        Try
'            excelApp = New Microsoft.Office.Interop.Excel.Application()
'            workbook = excelApp.Workbooks.Add()
'            worksheet = CType(workbook.Sheets(1), Microsoft.Office.Interop.Excel.Worksheet)

'            ' Set worksheet name
'            worksheet.Name = GetNotificationTypeName(_currentNotificationType)

'            ' Export headers
'            For i As Integer = 0 To columnsToExport.Count - 1
'                worksheet.Cells(1, i + 1) = GetColumnHeaderName(columnsToExport(i))
'            Next

'            ' Style headers
'            With worksheet.Range($"A1:{GetColumnLetter(columnsToExport.Count)}1")
'                .Font.Bold = True
'                .Interior.Color = RGB(184, 204, 228)
'                .Borders.LineStyle = Microsoft.Office.Interop.Excel.XlLineStyle.xlContinuous
'            End With

'            ' Export data
'            For rowIndex As Integer = 0 To _notificationsDataTable.Rows.Count - 1
'                For colIndex As Integer = 0 To columnsToExport.Count - 1
'                    Dim columnName As String = columnsToExport(colIndex)
'                    If _notificationsDataTable.Columns.Contains(columnName) Then
'                        Dim cellValue = _notificationsDataTable.Rows(rowIndex)(columnName)
'                        worksheet.Cells(rowIndex + 2, colIndex + 1) = If(cellValue Is DBNull.Value, "", cellValue.ToString())
'                    End If
'                Next
'            Next

'            ' Auto-fit columns
'            worksheet.Columns.AutoFit()

'            ' Add borders to all data
'            Dim dataRange As String = $"A1:{GetColumnLetter(columnsToExport.Count)}{_notificationsDataTable.Rows.Count + 1}"
'            worksheet.Range(dataRange).Borders.LineStyle = Microsoft.Office.Interop.Excel.XlLineStyle.xlContinuous

'            ' Save the file
'            workbook.SaveAs(fileName)

'        Finally
'            ' Clean up
'            If worksheet IsNot Nothing Then System.Runtime.InteropServices.Marshal.ReleaseComObject(worksheet)
'            If workbook IsNot Nothing Then
'                workbook.Close(False)
'                System.Runtime.InteropServices.Marshal.ReleaseComObject(workbook)
'            End If
'            If excelApp IsNot Nothing Then
'                excelApp.Quit()
'                System.Runtime.InteropServices.Marshal.ReleaseComObject(excelApp)
'            End If
'        End Try
'    End Sub

'    Private Function GetColumnLetter(columnNumber As Integer) As String
'        Dim columnLetter As String = ""
'        While columnNumber > 0
'            columnNumber -= 1
'            columnLetter = Chr(65 + (columnNumber Mod 26)) + columnLetter
'            columnNumber = columnNumber \ 26
'        End While
'        Return columnLetter
'    End Function
'#End Region

'#Region "Event Handlers"
'    Private Sub ToolStripButton1_Click(sender As Object, e As EventArgs) Handles ToolStripButton1.Click
'        Try
'            _currentNotificationType = GetNotificationTypeFromCombo()
'            Lbl_Notifications_Name.Text = GetNotificationTypeName(_currentNotificationType)
'            LoadNotificationData()
'        Catch ex As Exception
'            HandleError("خطأ في تحميل الإشعارات", ex)
'        End Try
'    End Sub

'    Private Sub Combo_Notifications_SelectedIndexChanged(sender As Object, e As EventArgs) Handles Combo_Notifications.SelectedIndexChanged
'        _currentNotificationType = GetNotificationTypeFromCombo()
'        Lbl_Notifications_Name.Text = GetNotificationTypeName(_currentNotificationType)
'    End Sub

'    Private Sub Btn_Refresh_Click(sender As Object, e As EventArgs) Handles Btn_Refresh.Click
'        LoadNotificationData()
'    End Sub

'    Private Sub dgv_Notifications_FilterStringChanged(sender As Object, e As EventArgs) Handles dgv_Notifications.FilterStringChanged
'        Try
'            _bindingSource.Filter = dgv_Notifications.FilterString
'            UpdateRowCount()
'        Catch ex As Exception
'            HandleError("خطأ في تطبيق الفلتر", ex)
'        End Try
'    End Sub

'    Private Sub dgv_Notifications_SortStringChanged(sender As Object, e As EventArgs) Handles dgv_Notifications.SortStringChanged
'        Try
'            _bindingSource.Sort = dgv_Notifications.SortString
'            UpdateRowCount()
'        Catch ex As Exception
'            HandleError("خطأ في تطبيق الترتيب", ex)
'        End Try
'    End Sub

'    Private Sub Txt_Days_KeyPress(sender As Object, e As KeyPressEventArgs) Handles Txt_Days.KeyPress
'        ' Only allow numbers and backspace
'        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> ControlChars.Back Then
'            e.Handled = True
'        End If
'    End Sub

'    Private Sub Txt_Days_TextChanged(sender As Object, e As EventArgs) Handles Txt_Days.TextChanged
'        ' Enable/disable refresh button based on input validity
'        Btn_Refresh.Enabled = IsInputValid()
'    End Sub
'#End Region

'#Region "Helper Methods"
'    Private Function GetNotificationTypeFromCombo() As NotificationType
'        Select Case Combo_Notifications.SelectedIndex
'            Case 0
'                Return NotificationType.HealthCardExpiry
'            Case 1
'                Return NotificationType.PersonalIDExpiry
'            Case 2
'                Return NotificationType.ContractExpiry
'            Case 3
'                Return NotificationType.InsuranceExpiry
'            Case Else
'                Return NotificationType.None
'        End Select
'    End Function

'    Private Function GetNotificationTypeName(notificationType As NotificationType) As String
'        Select Case notificationType
'            Case NotificationType.HealthCardExpiry
'                Return "انتهاء الشهادة الصحية"
'            Case NotificationType.PersonalIDExpiry
'                Return "انتهاء بطاقة الرقم القومى"
'            Case NotificationType.ContractExpiry
'                Return "انتهاء العقود"
'            Case NotificationType.InsuranceExpiry
'                Return "انتهاء التأمين"
'            Case Else
'                Return "غير محدد"
'        End Select
'    End Function

'    Private Sub UpdateRowCount()
'        Lbl_Count.Text = $"عدد السجلات: {dgv_Notifications.RowCount}"
'    End Sub

'    Private Sub ShowProgress(show As Boolean)
'        ProgressBar1.Visible = show
'        If show Then
'            ProgressBar1.Value = 0
'            ProgressBar1.Style = ProgressBarStyle.Continuous
'        End If
'    End Sub

'    Private Sub HandleError(message As String, ex As Exception)
'        Dim errorMessage As String = $"{message}{Environment.NewLine}التفاصيل: {ex.Message}"
'        MessageBox.Show(errorMessage, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)

'        ' Log error (implement logging mechanism)
'        LogError(ex, message)
'    End Sub

'    Private Sub LogError(ex As Exception, context As String)
'        ' Implement logging mechanism (e.g., to file, database, or event log)
'        Try
'            Dim logEntry As String = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {context}: {ex.Message}{Environment.NewLine}{ex.StackTrace}{Environment.NewLine}"
'            File.AppendAllText("ErrorLog.txt", logEntry)
'        Catch
'            ' Fail silently if logging fails
'        End Try
'    End Sub
'#End Region

'#Region "Form Events"
'    Private Sub Notifications_Screen_Load(sender As Object, e As EventArgs) Handles MyBase.Load
'        Try
'            Me.WindowState = FormWindowState.Maximized
'            Me.Text = "شاشة الإشعارات - نظام الدولية"
'        Catch ex As Exception
'            HandleError("خطأ في تحميل النموذج", ex)
'        End Try
'    End Sub

'    Private Sub Notifications_Screen_FormClosing(sender As Object, e As FormClosingEventArgs) Handles MyBase.FormClosing
'        Try
'            ' Cancel any running background operations
'            If BackgroundWorker1.IsBusy Then
'                BackgroundWorker1.CancelAsync()
'            End If
'        Catch ex As Exception
'            ' Ignore errors during form closing
'        End Try
'    End Sub
'#End Region
'End Class