<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Bunifu.UI.WinForms.BunifuGauge</name>
    </assembly>
    <members>
        <member name="T:Bunifu.UI.WinForms.BunifuRadialGauge">
            <summary>
            Provides a highly customizable, arc-based, range value display meter 
            for previewing value movements within definite ranges of motion.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.#ctor">
            <summary>
            Creates a new <see cref="T:Bunifu.UI.WinForms.BunifuRadialGauge"/> control.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuRadialGauge.CapStyles">
            <summary>
            Provides various cap styles used when 
            drawing the control's edges.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuRadialGauge.CapStyles.Flat">
            <summary>
            Draws a flat cap.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuRadialGauge.CapStyles.Round">
            <summary>
            Draws a rounded cap.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ShowRangeLabels">
            <summary>
            Gets or sets a value indicating whether
            the gauge's range labels will be displayed.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ShowValueLabel">
            <summary>
            Gets or sets a value indicating whether
            the Gauge's value label will be displayed.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.AutoGenerateProgressColorWhenLow">
            <summary>
            Gets or sets a lighter background color that will be 
            automatically generated for the progress background color 
            that is based on the set 'ProgressColorLow' color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.AutoGenerateProgressColorWhenHigh">
            <summary>
            Gets or sets a lighter background color that will be 
            automatically generated for the progress background color 
            that is based on the set 'ProgressColorHigh' color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.Value">
            <summary>
            Gets or sets the gauge's pointer value.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"> exception is thrown when the value 
            set is outside expected range, that is, between the Minimum and Maximum set values.</exception>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ValueByTransition">
            <summary>
            Gets or sets the gauge's pointer value using a smooth transition.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"> exception is thrown when the value 
            set is outside expected range, that is, between the Minimum and Maximum set values.</exception>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.Minimum">
            <summary>
            Gets or sets the minimum range value.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"> exception is thrown when 
            the Maximum property value is greater than the Minimum property value.</exception>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.Maximum">
            <summary>
            Gets or sets the maximum range value.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"> exception is thrown when 
            the Minimum property value is greater than the Maximum property value.</exception>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.WarningMark">
            <summary>
            Gets or sets the point at which the gauge should
            mark as the beginning of high value ranges.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.LighteningFactor">
            <summary>
            Gets or sets a lightening value/factor that will
            be used to generate the progress background color
            when the value set is either high or low. (Default is 70)
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ProgressColorLow">
            <summary>
            Gets or sets the Gauge's progress color whenever 
            it is within the low or minimum value ranges.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ProgressColorHigh">
            <summary>
            Gets or sets the gauge's progress color whenever 
            it is within the high or maximum value ranges as 
            specified by the property <see cref="P:Bunifu.UI.WinForms.BunifuRadialGauge.ProgressHighValueMark"/>.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.RangeLabelsColor">
            <summary>
            Gets or sets the gauge's range labels' color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ValueLabelColor">
            <summary>
            Gets or sets the gauge's value label color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ProgressBackColor">
            <summary>
            Gets or sets the gauge's progress bakcground color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.Prefix">
            <summary>
            Gets or sets the gauge's prefix text
            that precedes the gauge value.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.Suffix">
            <summary>
            Gets or sets the Gauge's suffix text that 
            is displayed besides the gauge value.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.Thickness">
            <summary>
            Gets or sets the gauge's progress thickness.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.Font">
            <summary>
            Gets or sets the gauge's standard font.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ProgressCap">
            <summary>
            Gets or sets the rendering style 
            of the progress edges.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.RangeLabelsFont">
            <summary>
            Gets or sets the Gauge's range labels' font.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.RangeEnd">
            <summary>
            Gets or sets the Gauge's end range value.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"> exception is thrown when the Minimum property value is greater than the Maximum property value.</exception>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.RangeStart">
            <summary>
            Gets or sets the Gauge's start range value.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"> exception is thrown when the Maximum property value is greater than the Minimum property value.</exception>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ProgressHighValueMark">
            <summary>
            Gets or sets the point at which the Gauge should
            mark as the beginning of high value ranges.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.GaugeRectangle">
            <summary>
            Gets the Gauge's drawing rectangle.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.BackgroundImage">
            <summary>
            Gets or sets the background image.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.BackgroundImageLayout">
            <summary>
            Gets or sets the background image layout.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuRadialGauge.ValueChanged">
            <summary>
            Occurs when the 'Value' property is changed.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuRadialGauge.ValueChangedEventArgs">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuRadialGauge.ValueChanged"/> event.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.ValueChangedEventArgs.#ctor(System.Int32)">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuRadialGauge.ValueChanged"/> event.
            </summary>
            <param name="value">The newly set ScrollBar value.</param>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuRadialGauge.ValueChangedEventArgs.Value">
            <summary>
            Gets the new <see cref="T:Bunifu.UI.WinForms.BunifuRadialGauge"/> value.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.TransitionValue(System.Int32,System.Int32)">
            <summary>
            Smoothly transitions the Gauge's value 
            from one point within range to another.
            </summary>
            <param name="value">The new value to transition to.</param>
            <param name="transitionSpeed">(Optional) The transition speed in milliseconds.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.SetDefaults">
            <summary>
            Applies the control's default values.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.AlignLabels">
            <summary>
            Aligns the gauge's labels accordingly.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.ConvertToDegrees(System.Int32)">
            <summary>
            Converts an integer into degrees.
            </summary>
            <param name="value">The integer value.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.ConvertRange(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Converts the ScrollBar's size dimensions to the user-specified range dimensions.
            </summary>
            <param name="originalStart">The original starting point.</param>
            <param name="originalEnd">The original ending point.</param>
            <param name="newStart">The new starting point in the user-specified range.</param>
            <param name="newEnd">The new ending point in the user-specified range.</param>
            <param name="value">The value to be set within the range.</param>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuRadialGauge.BunifuRadialGaugeActionList">
            <summary>
            Initializes a new instance of the <see cref="T:Bunifu.UI.WinForms.BunifuRadialGauge.BunifuRadialGaugeActionList"/> class.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.BunifuRadialGaugeActionList.GetSortedActionItems">
            <summary>
            Implementation of this abstract method creates Smart Tag items,
            associates their targets, and collects them into a list.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuRadialGauge.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuRadialGauge.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Extensions.ColorExtensions">
            <summary>
            Provides a collection of methods that extend the .NET Color class.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.IManagedType">
            <summary>
            Interface for all types we can perform transitions on. 
            Each type (e.g. int, double, Color) that we can perform a transition on 
            needs to have its own class that implements this interface. These classes 
            tell the transition system how to act on objects of that type.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.IManagedType.getManagedType">
            <summary>
            Returns the Type that the instance is managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.IManagedType.copy(System.Object)">
            <summary>
            Returns a deep copy of the object passed in. (In particular this is 
            needed for types that are objects.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.IManagedType.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns an object holding the value between the start and end corresponding
            to the percentage passed in. (Note: the percentage can be less than 0% or
            greater than 100%.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ITransitionType.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Called by the Transition framework when its timer ticks to pass in the
            time (in ms) since the transition started. 
            
            You should return (in an out parameter) the percentage movement towards 
            the destination value for the time passed in. Note: this does not need to
            be a smooth transition from 0% to 100%. You can overshoot with values
            greater than 100% or undershoot if you need to (for example, to have some
            form of "elasticity").
            
            The percentage should be returned as (for example) 0.1 for 10%.
            
            You should return (in an out parameter) whether the transition has completed.
            (This may not be at the same time as the percentage has moved to 100%.)
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.ManagedType_Color">
            <summary>
            Class that manages transitions for Color properties. For these we
            need to transition the R, G, B and A sub-properties independently.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Color.getManagedType">
            <summary>
            Returns the type we are managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Color.copy(System.Object)">
            <summary>
            Returns a copy of the color object passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Color.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Creates an intermediate value for the colors depending on the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.ManagedType_Double">
            <summary>
            Manages transitions for double properties.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Double.getManagedType">
            <summary>
             Returns the type managed by this class.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Double.copy(System.Object)">
            <summary>
            Returns a copy of the double passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Double.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the value between start and end for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Float.getManagedType">
            <summary>
            Returns the type we're managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Float.copy(System.Object)">
            <summary>
            Returns a copy of the float passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Float.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the interpolated value for the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.ManagedType_Int">
            <summary>
            Manages transitions for int properties.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Int.getManagedType">
            <summary>
            Returns the type we are managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Int.copy(System.Object)">
            <summary>
            Returns a copy of the int passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_Int.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the value between the start and end for the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.ManagedType_String">
            <summary>
            Manages transitions for strings. This doesn't make as much sense as transitions
            on other types, but I like the way it looks!
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_String.getManagedType">
            <summary>
            Returns the type we're managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_String.copy(System.Object)">
            <summary>
            Returns a copy of the string passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.ManagedType_String.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns an "interpolated" string.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.Transition">
            <summary>
            Lets you perform animated transitions of properties on arbitrary objects. These 
            will often be transitions of UI properties, for example an animated fade-in of 
            a UI object, or an animated move of a UI object from one position to another.
            
            Each transition can simulataneously change multiple properties, including properties
            across multiple objects.
            
            Example transition
            ------------------
            a.      Transition t = new Transition(new TransitionMethod_Linear(500));
            b.      t.add(form1, "Width", 500);
            c.      t.add(form1, "BackColor", Color.Red);
            d.      t.run();
              
            Line a:         Creates a new transition. You specify the transition method.
                            
            Lines b. and c: Set the destination values of the properties you are animating.
            
            Line d:         Starts the transition.
            
            Transition methods
            ------------------
            TransitionMethod objects specify how the transition is made. Examples include
            linear transition, ease-in-ease-out and so on. Different transition methods may
            need different parameters.
            
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.#cctor">
            <summary>
            You should register all managed-types here.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.Transition.Args">
            <summary>
            Args passed with the TransitionCompletedEvent.
            </summary>
        </member>
        <member name="E:Utilities.BunifuGauge.Transitions.Transition.TransitionCompletedEvent">
            <summary>
            Event raised when the transition hass completed.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.run(System.Object,System.String,System.Object,Utilities.BunifuGauge.Transitions.ITransitionType)">
            <summary>
            Creates and immediately runs a transition on the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.run(System.Object,System.String,System.Object,System.Object,Utilities.BunifuGauge.Transitions.ITransitionType)">
            <summary>
            Sets the property passed in to the initial value passed in, then creates and 
            immediately runs a transition on it.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.runChain(Utilities.BunifuGauge.Transitions.Transition[])">
            <summary>
            Creates a TransitionChain and runs it.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.#ctor(Utilities.BunifuGauge.Transitions.ITransitionType)">
            <summary>
            Constructor. You pass in the object that holds the properties 
            that you are performing transitions on.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.add(System.Object,System.String,System.Object)">
            <summary>
            Adds a property that should be animated as part of this transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.run">
            <summary>
            Starts the transition.
            </summary>
        </member>
        <member name="P:Utilities.BunifuGauge.Transitions.Transition.TransitionedProperties">
            <summary>
            Property that returns a list of information about each property managed
            by this transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.removeProperty(Utilities.BunifuGauge.Transitions.Transition.TransitionedPropertyInfo)">
            <summary>
            We remove the property with the info passed in from the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.onTimer">
            <summary>
            Called when the transition timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.setProperty(System.Object,Utilities.BunifuGauge.Transitions.Transition.PropertyUpdateArgs)">
            <summary>
            Sets a property on the object passed in to the value passed in. This method
            invokes itself on the GUI thread if the property is being invoked on a GUI 
            object.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.isDisposed(System.Object)">
            <summary>
            Returns true if the object passed in is a Control and is disposed
            or in the process of disposing. (If this is the case, we don't want
            to make any changes to its properties.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Transition.registerType(Utilities.BunifuGauge.Transitions.IManagedType)">
            <summary>
            Registers a transition-type. We hold them in a map.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionChain.runNextTransition">
            <summary>
            Runs the next transition in the list.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionChain.onTransitionCompleted(System.Object,Utilities.BunifuGauge.Transitions.Transition.Args)">
            <summary>
            Called when the transition we have just run has completed.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionElement.#ctor(System.Double,System.Double,Utilities.BunifuGauge.Transitions.InterpolationMethod)">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="P:Utilities.BunifuGauge.Transitions.TransitionElement.EndTime">
            <summary>
            The percentage of elapsed time, expressed as (for example) 75 for 75%.
            </summary>
        </member>
        <member name="P:Utilities.BunifuGauge.Transitions.TransitionElement.EndValue">
            <summary>
            The value of the animated properties at the EndTime. This is the percentage 
            movement of the properties between their start and end values. This should
            be expressed as (for example) 75 for 75%.
            </summary>
        </member>
        <member name="P:Utilities.BunifuGauge.Transitions.TransitionElement.InterpolationMethod">
            <summary>
            The interpolation method to use when moving between the previous value
            and the current one.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionManager">
            <summary>
            This class is responsible for running transitions. It holds the timer that
            triggers transaction animation. 
            </summary><remarks>
            This class is a singleton.
            
            We manage the transaction timer here so that we can have a single timer
            across all transactions. If each transaction has its own timer, this creates
            one thread for each transaction, and this can lead to too many threads in
            an application.
            
            This class essentially just manages the timer for the transitions. It calls 
            back into the running transitions, which do the actual work of the transition.
            
            </remarks>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionManager.getInstance">
            <summary>
            Singleton's getInstance method.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionManager.register(Utilities.BunifuGauge.Transitions.Transition)">
            <summary>
            You register a transition with the manager here. This will start to run
            the transition as the manager's timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionManager.removeDuplicates(Utilities.BunifuGauge.Transitions.Transition)">
            <summary>
            Checks if any existing transitions are acting on the same properties as the
            transition passed in. If so, we remove the duplicated properties from the 
            older transitions.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionManager.removeDuplicates(Utilities.BunifuGauge.Transitions.Transition,Utilities.BunifuGauge.Transitions.Transition)">
            <summary>
            Finds any properties in the old-transition that are also in the new one,
            and removes them from the old one.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionManager.#ctor">
            <summary>
            Private constructor (for singleton).
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionManager.onTimerElapsed(System.Object,System.Timers.ElapsedEventArgs)">
            <summary>
            Called when the timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionManager.onTransitionCompleted(System.Object,Utilities.BunifuGauge.Transitions.Transition.Args)">
            <summary>
            Called when a transition has completed. 
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_Acceleration">
            <summary>
            Manages transitions under constant acceleration from a standing start.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Acceleration.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Acceleration.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            The initial velocity is 0, and the acceleration to get to 1.0
            at t=1.0 is 2, so the formula just becomes:
              s = t^2
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_Bounce">
            <summary>
            This transition bounces the property to a destination value and back to the
            original value. It is accelerated to the destination and then decelerated back
            as if being dropped with gravity and bouncing back against gravity.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Bounce.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the total time taken for the bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_CriticalDamping">
            <summary>
            This transition animates with an exponential decay. This has a damping effect
            similar to the motion of a needle on an electomagnetically controlled dial.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_CriticalDamping.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_CriticalDamping.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_Deceleration">
            <summary>
            Manages a transition starting from a high speed and decelerating to zero by
            the end of the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Deceleration.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Deceleration.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            The initial velocity is 2, and the acceleration to get to 1.0
            at t=1.0 is -2, so the formula becomes:
              s = t(2-t)
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_EaseInEaseOut">
            <summary>
            Manages an ease-in-ease-out transition. This accelerates during the first 
            half of the transition, and then decelerates during the second half.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_EaseInEaseOut.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_EaseInEaseOut.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            We accelerate as at the rate needed (a=4) to get to 0.5 at t=0.5, and
            then decelerate at the same rate to end up at 1.0 at t=1.0.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_Flash">
            <summary>
            This transition type 'flashes' the properties a specified number of times, ending
            up by reverting them to their initial values. You specify the number of bounces and
            the length of each bounce. 
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Flash.#ctor(System.Int32,System.Int32)">
            <summary>
            You specify the number of bounces and the time taken for each bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_Linear">
            <summary>
            This class manages a linear transition. The percentage complete for the transition
            increases linearly with time.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Linear.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time (in milliseconds) that the
            transition will take.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_Linear.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            We return the percentage completed.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_ThrowAndCatch">
            <summary>
            This transition bounces the property to a destination value and back to the
            original value. It is decelerated to the destination and then acclerated back
            as if being thrown against gravity and then descending back with gravity.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_ThrowAndCatch.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the total time taken for the bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.TransitionType_UserDefined">
            <summary>
            This class allows you to create user-defined transition types. You specify these
            as a list of TransitionElements. Each of these defines: 
            End time , End value, Interpolation method
            
            For example, say you want to make a bouncing effect with a decay:
            
            EndTime%    EndValue%   Interpolation
            --------    ---------   -------------
            50          <USER>         <GROUP> 
            75          50          Deceleration
            85          100         Acceleration
            91          75          Deceleration
            95          100         Acceleration
            98          90          Deceleration
            100         100         Acceleration
            
            The time values are expressed as a percentage of the overall transition time. This 
            means that you can create a user-defined transition-type and then use it for transitions
            of different lengths.
            
            The values are percentages of the values between the start and end values of the properties
            being animated in the transitions. 0% is the start value and 100% is the end value.
            
            The interpolation is one of the values from the InterpolationMethod enum.
            
            So the example above accelerates to the destination (as if under gravity) by
            t=50%, then bounces back up to half the initial height by t=75%, slowing down 
            (as if against gravity) before falling down again and bouncing to decreasing 
            heights each time.
            
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_UserDefined.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_UserDefined.#ctor(System.Collections.Generic.IList{Utilities.BunifuGauge.Transitions.TransitionElement},System.Int32)">
            <summary>
            Constructor. You pass in the list of TransitionElements and the total time
            (in milliseconds) for the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_UserDefined.setup(System.Collections.Generic.IList{Utilities.BunifuGauge.Transitions.TransitionElement},System.Int32)">
            <summary>
            Sets up the transitions. 
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_UserDefined.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Called to find the value for the movement of properties for the time passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.TransitionType_UserDefined.getElementInfo(System.Double,System.Double@,System.Double@,System.Double@,System.Double@,Utilities.BunifuGauge.Transitions.InterpolationMethod@)">
            <summary>
            Returns the element info for the time-fraction passed in. 
            </summary>
        </member>
        <member name="T:Utilities.BunifuGauge.Transitions.Utility">
            <summary>
            A class holding static utility functions.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.getValue(System.Object,System.String)">
            <summary>
            Returns the value of the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.setValue(System.Object,System.String,System.Object)">
            <summary>
            Sets the value of the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.interpolate(System.Double,System.Double,System.Double)">
            <summary>
            Returns a value between d1 and d2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.interpolate(System.Int32,System.Int32,System.Double)">
            <summary>
            Returns a value betweeen i1 and i2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.interpolate(System.Single,System.Single,System.Double)">
            <summary>
            Returns a value betweeen f1 and f2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.convertLinearToEaseInEaseOut(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under an ease-in-ease-out transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.convertLinearToAcceleration(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under a constant acceleration transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.convertLinearToDeceleration(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under a constant deceleration transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuGauge.Transitions.Utility.raiseEvent``1(System.EventHandler{``0},System.Object,``0)">
            <summary>
            Fires the event passed in in a thread-safe way. 
            </summary><remarks>
            This method loops through the targets of the event and invokes each in turn. If the
            target supports ISychronizeInvoke (such as forms or controls) and is set to run 
            on a different thread, then we call BeginInvoke to marshal the event to the target
            thread. If the target does not support this interface (such as most non-form classes)
            or we are on the same thread as the target, then the event is fired on the same
            thread as this is called from.
            </remarks>
        </member>
    </members>
</doc>
