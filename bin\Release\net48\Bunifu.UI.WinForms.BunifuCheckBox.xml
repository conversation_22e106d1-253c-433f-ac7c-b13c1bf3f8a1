<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Bunifu.UI.WinForms.BunifuCheckBox</name>
    </assembly>
    <members>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox">
            <summary>
            Provides enhanced visual support for on and off states when supporting feature selections.m
            </summary>
            <seealso cref="T:System.Windows.Forms.UserControl" />
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.#ctor">
            <summary>
            Creates a new BunifuCheckBox control.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.#ctor(System.Boolean)">
            <summary>
            Creates a new BunifuCheckBox control.
            </summary>
            <param name="isChecked">Sets the CheckBox check-state to true or false.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.#ctor(System.Boolean,System.Windows.Forms.Control)">
            <summary>
            Creates a new BunifuCheckBox control.
            </summary>
            <param name="isChecked">Sets the CheckBox check-state to true or false.</param>
            <param name="bindingControl">Sets the control to bind directly with the CheckBox; this in most cases is a Label.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.#ctor(System.Boolean,System.Windows.Forms.Control,System.Drawing.Point)">
            <summary>
            Creates a new BunifuCheckBox control.
            </summary>
            <param name="isChecked">Sets the CheckBox check-state to true or false.</param>
            <param name="bindingControl">Sets the control to bind directly with the CheckBox; this in most cases is a Label.</param>
            <param name="location">Sets the location of the CheckBox within the Form. [Example: New Point(12, 12)]</param>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.CheckBoxStyles">
            <summary>
            Specifies the available styles applicable to Bunifu CheckBoxes.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.CheckBoxStyles.Bunifu">
            <summary>
            Renders Bunifu's standard CheckBox.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.CheckBoxStyles.Flat">
            <summary>
            Renders a standard flat designed CheckBox.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.CheckBoxStyles.Round">
            <summary>
            Renders a smooth round designed CheckBox.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.CheckStates">
            <summary>
            Specifies the CheckBox check-state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.CheckStates.Checked">
            <summary>
            The checked state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.CheckStates.Unchecked">
            <summary>
            The unchecked state.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.CheckStates.Indeterminate">
            <summary>
            The indeterminate state.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPositions">
            <summary>
            Specifies the bound control's position in relation to the CheckBox attached to it.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPositions.Left">
            <summary>
            Positions the control to the left of the CheckBox.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPositions.Right">
            <summary>
            Positions the control to the right of the CheckBox.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.OnUncheck">
            <summary>
            Represents the normal or default state of the control.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.OnHoverChecked">
            <summary>
            Represents the hovered state of the control, that is, when the control gets mouse-focus.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.OnHoverUnchecked">
            <summary>
            Represents the hovered state of the control, that is, when the control gets mouse-focus.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.OnCheck">
            <summary>
            Represents the checked state of the control.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.OnDisable">
            <summary>
            Represents the disabled or inactive state of the control.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.BorderRadius">
            <summary>
            Gets or sets the control's overral border radius.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.AutoCheck">
            <summary>
            Causes the Checkbox to automatically change state when clicked.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.Checked">
            <summary>
            Gets or sets the CheckBox check-state.
            </summary>
            <value>CheckState</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.CheckState">
            <summary>
            Gets or sets the CheckBox check-state.
            </summary>
            <value>CheckState</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.ThreeState">
            <summary>
            Gets or sets a value indicating whether the CheckBox will allow three states, that is, the \"Checked\", \"Unchecked\" and \"Indeterminate\" states rather than \"Checked\" and \"Unchecked\" only.
            </summary>
            <value>ThreeState</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.AllowCheckmarkAnimation">
            <summary>
            Gets or sets a value indicating whether the Checkmark will allow check-state-change animations/transitions at runtime.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.AllowCheckBoxAnimation">
            <summary>
            Gets or sets a value indicating whether the CheckBox will allow standard animations at runtime.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.AllowBindingControlAnimation">
            <summary>
            Gets or sets a value indicating whether the CheckBox animation will be applied to the bound control.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.AllowBindingControlColorChanges">
            <summary>
            Gets or sets a value indicating whether the CheckBox will
            colorize the bound control. This is especially applicable
            where the bound control is a <see cref="T:System.Windows.Forms.Label"/>.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.AllowOnHoverStates">
            <summary>
            Gets a value indicating whether the CheckBox will allow the \"OnHoverChecked\" and \"OnHoverUnchecked\" states to be active at runtime.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.AllowBindingControlLocation">
            <summary>
            Gets or sets a value indicating whether the CheckBox will allow the bound control's location to be positioned based on it's own location.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.BindingControl">
            <summary>
            Gets or sets the control to bind directly with the CheckBox; this in most cases is a Label. This setting also binds the most appropriate events of the CheckBox to the control selected.
            </summary>
            <value>
            BindingControl
            </value>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPosition">
            <summary>
            Gets or sets the position of the bound control in relation to the CheckBox.
            </summary>
            <value>
            BindingControlPosition
            </value>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.ToolTipText">
            <summary>
            Gets or sets the control's ToolTip Text.
            </summary>
            <value>
            ToolTipText
            </value>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.Style">
            <summary>
            [Deprecated] Gets or sets the standard CheckBox style to be applied.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.CustomCheckmarkImage">
            <summary>
            Gets or sets a custom checkmark image.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.CheckmarkRectangle">
            <summary>
            Gets the Checkmark's rectangle.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.NewBindingControl(System.Windows.Forms.Control)">
            <summary>
            Allows you to easily create a new binding control and automatically adds and binds it to the CheckBox.
            </summary>
            <param name="control">The control to create and bind, together with its properties.
            Note: You can create a label, a button or any other control you'd prefer to bind with.
            </param>
            <returns>Bound Control.</returns>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.NewBindingLabel(System.String,System.Drawing.Font,System.Drawing.Color)">
            <summary>
            Allows you to easily create a new Label that will automatically be bound with the CheckBox.
            </summary>
            <param name="text">The label's Text property value.</param>
            <param name="font">The label's Font property value.</param>
            <param name="foreColor">The label's ForeColor property value.</param>
            <returns>Bound Label.</returns>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.Refresh">
            <summary>
            Forces the control to invalidate its client area and immediately redraw itself and any child controls.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.DrawCanvas(System.Drawing.Graphics,System.Drawing.Rectangle,System.Int32,System.Drawing.Pen,System.Drawing.Color,Bunifu.UI.WinForms.CheckBoxState)">
            <summary>
            Draws a user-defined Graphics canvas.
            </summary>
            <param name="graphics">The Graphics canvas.</param>
            <param name="Bounds">The canvas rectangle-bounds.</param>
            <param name="cornerRadius">The canvas corner-radius.</param>
            <param name="drawingPen">The pen used to draw objects inside the canvas.</param>
            <param name="fillColor">Color of the fill.</param>
            <param name="checkBoxState">The state to manipulate/render.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.DrawCheckmark(System.Drawing.Graphics@,System.Drawing.Pen@,Bunifu.UI.WinForms.CheckBoxState@,System.Drawing.Point,System.Drawing.Point,System.Drawing.Point,System.Int32,System.Int32)">
            <summary>
            Draws the Checkmark bitmap.
            </summary>
            <param name="graphics">The Graphics to reference.</param>
            <param name="checkmarkPen">The checkmark pen.</param>
            <param name="checkBoxState">The current/active CheckBox state.</param>
            <param name="StartPoint">The start point.</param>
            <param name="LeftPoint">The left point.</param>
            <param name="RightPoint">The right point.</param>
            <param name="width">The checkmark width.</param>
            <param name="height">The checkmark height.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.RenderCanvas(Bunifu.UI.WinForms.CheckBoxState)">
            <summary>
            Renders the control region.
            </summary>
            <param name="checkBoxState">Specify the state to be applied to the control.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.OnPropertyChange(System.String)">
            <summary>
            Invokes the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.PropertyChanged"/> event.
            </summary>
            <param name="propertyName">
            The valid name of the property changed.
            </param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.PerformCheckStateTransitions">
            <summary>
            Performs a check-state-changed color transition within the CheckBox region.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.ShowCheckmark(System.Boolean)">
            <summary>
            Displays the checkmark allowing transitioning from the current size to the standard size.
            </summary>
            <param name="isRendering">Set to true if in render mode; otherwise set to false.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.HideCheckmark(System.Boolean)">
            <summary>
            Hide the checkmark allowing transitioning from the current size to the standard size.
            </summary>
            <param name="isRendering">Set to <see cref="!:true"/> if in render mode; otherwise set to <see cref="!:false"/>.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.AnimateCheckBox_TransitionCompletedEvent(System.Object,Utilities.BunifuCheckBox.Transitions.Transition.Args)">
            <summary>
            This event is called once the transitions have completed.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.AnimateChecks_TransitionCompletedEvent(System.Object,Utilities.BunifuCheckBox.Transitions.Transition.Args)">
            <summary>
            [Reserved] This event is called once the transitions have completed.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.HandleControlLocationBinding(System.Boolean,System.Windows.Forms.Control)">
            <summary>
            Handles binding and moving of the internally bound/set control with the CheckBox.
            </summary>
            <param name="isInPropertyMode">Is the current call to this method within a property? If so, use the "value" parameter in the setter to pass the control.</param>
            <param name="boundControl">The selected user control to be bound with the CheckBox.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.IsInDesignMode">
            <summary>
            Determines whether the hosting process is in design-mode.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.IsDesignMode">
            <summary>
            Determines whether the hosting process is in design-mode as per the LicenseManager module.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuCheckBox.CheckedChanged">
            <summary>
            Occurs whenever the Checked property has been changed.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuCheckBox.CheckStateChanged">
            <summary>
            Occurs whenever the CheckState property has been changed.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChanged">
            <summary>
            Occurs whenever the active CheckBox state's properties have been changed.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuCheckBox.StylePropertyChanged">
            <summary>
            Occurs whenever the CheckBox Style property has been changed.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlChanged">
            <summary>
            Occurs whenever the bound control has been changed.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPositionChanged">
            <summary>
            Occurs whenever the bound control's position in relation to the CheckBox has been changed.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.CheckedChangedEventArgs">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.CheckedChanged"/> and <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.CheckStateChanged"/> events.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.CheckedChangedEventArgs.#ctor(Bunifu.UI.WinForms.BunifuCheckBox.CheckStates,System.Boolean)">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.CheckedChanged"/> and <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.CheckStateChanged"/> events.
            </summary>
            <param name="state">Provide the state to be passed-in.</param>
            <param name="isChecked">Set the Checked value to true/false.</param>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.CheckedChangedEventArgs.Checked">
            <summary>
            Gets a value indicating whether the CheckBox is checked or not.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.CheckedChangedEventArgs.CheckState">
            <summary>
            Gets a value indicating whether the CheckBox check-state is set to true or false
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChanged"/> event.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.#ctor(Bunifu.UI.WinForms.CheckBoxState)">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChanged"/> event.
            </summary>
            <param name="currentState">The currently active CheckBox state.</param>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.CurrentState">
            <summary>
            Provides access to the available state properties in the CheckBox.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.CheckBoxColor">
            <summary>
            Gets the checkbox inner fill color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.BorderRadius">
            <summary>
            Gets the checkbox's border radius.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.BorderThickness">
            <summary>
            Gets the checkbox border thickness.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.BorderColor">
            <summary>
            Gets the checkbox border color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.CheckmarkColor">
            <summary>
            Gets the checkbox border color.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.CheckmarkThickness">
            <summary>
            Gets the checkmark thickness.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StatePropertiesChangedEventArgs.UseBorderThicknessForCheckmark">
            <summary>
            Gets a value indicating whether the property UseBorderThicknessForCheckmark has been allowed.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.StylePropertyChangedEventArgs">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.StylePropertyChanged"/> event.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.StylePropertyChangedEventArgs.#ctor(Bunifu.UI.WinForms.BunifuCheckBox.CheckBoxStyles)">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.StylePropertyChanged"/> event.
            </summary>
            <param name="currentStyle">The currently applied CheckBox style.</param>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.StylePropertyChangedEventArgs.Style">
            <summary>
            Gets the Style applied to the CheckBox.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlChangedEventArgs">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlChanged"/> event.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlChangedEventArgs.#ctor(System.Windows.Forms.Control)">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlChanged"/> event.
            </summary>
            <param name="currentlyBoundControl">The currently bound control.</param>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlChangedEventArgs.Control">
            <summary>
            Gets the control that is currently bound-up with the CheckBox.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.PositionChangedEventArgs">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPositionChanged"/> event.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.PositionChangedEventArgs.#ctor(Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPositions)">
            <summary>
            Provides data for the <see cref="E:Bunifu.UI.WinForms.BunifuCheckBox.BindingControlPositionChanged"/> event.
            </summary>
            <param name="currentControlPosition">The currently set control's position.</param>
        </member>
        <member name="P:Bunifu.UI.WinForms.BunifuCheckBox.PositionChangedEventArgs.BindingControlPosition">
            <summary>
            Gets the current position of the bound control in relation to the CheckBox.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.BunifuCheckBox.BunifuCheckBoxActionList">
            <summary>
            Initializes a new instance of the <see cref="T:Bunifu.UI.WinForms.BunifuCheckBox.BunifuCheckBoxActionList"/> class.
            </summary>
            <seealso cref="T:System.ComponentModel.Design.DesignerActionList" />
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.BunifuCheckBoxActionList.GetSortedActionItems">
            <summary>
            Implementation of this abstract method creates Smart Tag items,
            associates their targets, and collects them into a list.
            </summary>
        </member>
        <member name="F:Bunifu.UI.WinForms.BunifuCheckBox.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Bunifu.UI.WinForms.BunifuCheckBox.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:Bunifu.UI.WinForms.CheckBoxState">
            <summary>
            An abstract class used to define various states within Bunifu CheckBox.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.CheckBoxState.#ctor(System.String)">
            <summary>
            Creates a new Bunifu CheckBox state.
            </summary>
            <param name="name">Provide a name for the state. This can be used to track the state once initialized.</param>
        </member>
        <member name="F:Bunifu.UI.WinForms.CheckBoxState.CheckBoxSize">
            <summary>
            Gets the current CheckBox size.
            </summary>
        </member>
        <member name="E:Bunifu.UI.WinForms.CheckBoxState.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="M:Bunifu.UI.WinForms.CheckBoxState.NotifyPropertyChanged(System.String)">
            <summary>
            Notifies the property changed event.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.Name">
            <summary>
            Gets the name applied to the state.
            </summary>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.CheckBoxColor">
            <summary>
            Gets or sets the CheckBox inner color.
            </summary>
            <value>CheckBoxColor.</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.BorderRadius">
            <summary>
            Gets or sets the CheckBox border radius. The higher the value, the more curved the edges appear.
            </summary>
            <value>BorderRadius</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.BorderThickness">
            <summary>
            Gets or sets the CheckBox border thickness.
            </summary>
            <value>BorderThickness</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.BorderColor">
            <summary>
            Gets or sets the CheckBox border color.
            </summary>
            <value>BorderColor</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.CheckmarkColor">
            <summary>
            Gets or sets the CheckBox checkmark color.
            </summary>
            <value>CheckmarkColor</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.CheckmarkThickness">
            <summary>
            Gets or sets the CheckBox checkmark thickness.
            </summary>
            <value>CheckmarkThickness</value>
        </member>
        <member name="P:Bunifu.UI.WinForms.CheckBoxState.UseBorderThicknessForCheckmark">
            <summary>
            Gets or sets a value indicating whether to use the border thickness of the CheckBox as the border thickness of the checkmark.
            </summary>
            <value>UseBorderThicknessForCheckmark</value>
        </member>
        <member name="M:Bunifu.UI.WinForms.CheckBoxState.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> containing the values passed to the state separated by semi-colons.
            The order of appearance is: <see cref="P:Bunifu.UI.WinForms.CheckBoxState.BorderColor"/>, <see cref="P:Bunifu.UI.WinForms.CheckBoxState.CheckBoxColor"/>, 
            <see cref="P:Bunifu.UI.WinForms.CheckBoxState.CheckmarkColor"/>, <see cref="P:Bunifu.UI.WinForms.CheckBoxState.BorderRadius"/>, <see cref="P:Bunifu.UI.WinForms.CheckBoxState.BorderThickness"/>, 
            <see cref="P:Bunifu.UI.WinForms.CheckBoxState.CheckmarkThickness"/>, <see cref="P:Bunifu.UI.WinForms.CheckBoxState.UseBorderThicknessForCheckmark"/>.
            </summary>
            <returns>String</returns>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.IManagedType">
            <summary>
            Interface for all types we can perform transitions on. 
            Each type (e.g. int, double, Color) that we can perform a transition on 
            needs to have its own class that implements this interface. These classes 
            tell the transition system how to act on objects of that type.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.IManagedType.getManagedType">
            <summary>
            Returns the Type that the instance is managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.IManagedType.copy(System.Object)">
            <summary>
            Returns a deep copy of the object passed in. (In particular this is 
            needed for types that are objects.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.IManagedType.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns an object holding the value between the start and end corresponding
            to the percentage passed in. (Note: the percentage can be less than 0% or
            greater than 100%.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ITransitionType.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Called by the Transition framework when its timer ticks to pass in the
            time (in ms) since the transition started. 
            
            You should return (in an out parameter) the percentage movement towards 
            the destination value for the time passed in. Note: this does not need to
            be a smooth transition from 0% to 100%. You can overshoot with values
            greater than 100% or undershoot if you need to (for example, to have some
            form of "elasticity").
            
            The percentage should be returned as (for example) 0.1 for 10%.
            
            You should return (in an out parameter) whether the transition has completed.
            (This may not be at the same time as the percentage has moved to 100%.)
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.ManagedType_Color">
            <summary>
            Class that manages transitions for Color properties. For these we
            need to transition the R, G, B and A sub-properties independently.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Color.getManagedType">
            <summary>
            Returns the type we are managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Color.copy(System.Object)">
            <summary>
            Returns a copy of the color object passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Color.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Creates an intermediate value for the colors depending on the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.ManagedType_Double">
            <summary>
            Manages transitions for double properties.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Double.getManagedType">
            <summary>
             Returns the type managed by this class.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Double.copy(System.Object)">
            <summary>
            Returns a copy of the double passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Double.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the value between start and end for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Float.getManagedType">
            <summary>
            Returns the type we're managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Float.copy(System.Object)">
            <summary>
            Returns a copy of the float passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Float.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the interpolated value for the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.ManagedType_Int">
            <summary>
            Manages transitions for int properties.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Int.getManagedType">
            <summary>
            Returns the type we are managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Int.copy(System.Object)">
            <summary>
            Returns a copy of the int passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_Int.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns the value between the start and end for the percentage passed in.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.ManagedType_String">
            <summary>
            Manages transitions for strings. This doesn't make as much sense as transitions
            on other types, but I like the way it looks!
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_String.getManagedType">
            <summary>
            Returns the type we're managing.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_String.copy(System.Object)">
            <summary>
            Returns a copy of the string passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.ManagedType_String.getIntermediateValue(System.Object,System.Object,System.Double)">
            <summary>
            Returns an "interpolated" string.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.Transition">
            <summary>
            Lets you perform animated transitions of properties on arbitrary objects. These 
            will often be transitions of UI properties, for example an animated fade-in of 
            a UI object, or an animated move of a UI object from one position to another.
            
            Each transition can simulataneously change multiple properties, including properties
            across multiple objects.
            
            Example transition
            ------------------
            a.      Transition t = new Transition(new TransitionMethod_Linear(500));
            b.      t.add(form1, "Width", 500);
            c.      t.add(form1, "BackColor", Color.Red);
            d.      t.run();
              
            Line a:         Creates a new transition. You specify the transition method.
                            
            Lines b. and c: Set the destination values of the properties you are animating.
            
            Line d:         Starts the transition.
            
            Transition methods
            ------------------
            TransitionMethod objects specify how the transition is made. Examples include
            linear transition, ease-in-ease-out and so on. Different transition methods may
            need different parameters.
            
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.#cctor">
            <summary>
            You should register all managed-types here.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.Transition.Args">
            <summary>
            Args passed with the TransitionCompletedEvent.
            </summary>
        </member>
        <member name="E:Utilities.BunifuCheckBox.Transitions.Transition.TransitionCompletedEvent">
            <summary>
            Event raised when the transition hass completed.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.run(System.Object,System.String,System.Object,Utilities.BunifuCheckBox.Transitions.ITransitionType)">
            <summary>
            Creates and immediately runs a transition on the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.run(System.Object,System.String,System.Object,System.Object,Utilities.BunifuCheckBox.Transitions.ITransitionType)">
            <summary>
            Sets the property passed in to the initial value passed in, then creates and 
            immediately runs a transition on it.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.runChain(Utilities.BunifuCheckBox.Transitions.Transition[])">
            <summary>
            Creates a TransitionChain and runs it.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.#ctor(Utilities.BunifuCheckBox.Transitions.ITransitionType)">
            <summary>
            Constructor. You pass in the object that holds the properties 
            that you are performing transitions on.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.add(System.Object,System.String,System.Object)">
            <summary>
            Adds a property that should be animated as part of this transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.run">
            <summary>
            Starts the transition.
            </summary>
        </member>
        <member name="P:Utilities.BunifuCheckBox.Transitions.Transition.TransitionedProperties">
            <summary>
            Property that returns a list of information about each property managed
            by this transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.removeProperty(Utilities.BunifuCheckBox.Transitions.Transition.TransitionedPropertyInfo)">
            <summary>
            We remove the property with the info passed in from the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.onTimer">
            <summary>
            Called when the transition timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.setProperty(System.Object,Utilities.BunifuCheckBox.Transitions.Transition.PropertyUpdateArgs)">
            <summary>
            Sets a property on the object passed in to the value passed in. This method
            invokes itself on the GUI thread if the property is being invoked on a GUI 
            object.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.isDisposed(System.Object)">
            <summary>
            Returns true if the object passed in is a Control and is disposed
            or in the process of disposing. (If this is the case, we don't want
            to make any changes to its properties.)
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Transition.registerType(Utilities.BunifuCheckBox.Transitions.IManagedType)">
            <summary>
            Registers a transition-type. We hold them in a map.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionChain.runNextTransition">
            <summary>
            Runs the next transition in the list.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionChain.onTransitionCompleted(System.Object,Utilities.BunifuCheckBox.Transitions.Transition.Args)">
            <summary>
            Called when the transition we have just run has completed.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionElement.#ctor(System.Double,System.Double,Utilities.BunifuCheckBox.Transitions.InterpolationMethod)">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="P:Utilities.BunifuCheckBox.Transitions.TransitionElement.EndTime">
            <summary>
            The percentage of elapsed time, expressed as (for example) 75 for 75%.
            </summary>
        </member>
        <member name="P:Utilities.BunifuCheckBox.Transitions.TransitionElement.EndValue">
            <summary>
            The value of the animated properties at the EndTime. This is the percentage 
            movement of the properties between their start and end values. This should
            be expressed as (for example) 75 for 75%.
            </summary>
        </member>
        <member name="P:Utilities.BunifuCheckBox.Transitions.TransitionElement.InterpolationMethod">
            <summary>
            The interpolation method to use when moving between the previous value
            and the current one.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionManager">
            <summary>
            This class is responsible for running transitions. It holds the timer that
            triggers transaction animation. 
            </summary><remarks>
            This class is a singleton.
            
            We manage the transaction timer here so that we can have a single timer
            across all transactions. If each transaction has its own timer, this creates
            one thread for each transaction, and this can lead to too many threads in
            an application.
            
            This class essentially just manages the timer for the transitions. It calls 
            back into the running transitions, which do the actual work of the transition.
            
            </remarks>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionManager.getInstance">
            <summary>
            Singleton's getInstance method.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionManager.register(Utilities.BunifuCheckBox.Transitions.Transition)">
            <summary>
            You register a transition with the manager here. This will start to run
            the transition as the manager's timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionManager.removeDuplicates(Utilities.BunifuCheckBox.Transitions.Transition)">
            <summary>
            Checks if any existing transitions are acting on the same properties as the
            transition passed in. If so, we remove the duplicated properties from the 
            older transitions.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionManager.removeDuplicates(Utilities.BunifuCheckBox.Transitions.Transition,Utilities.BunifuCheckBox.Transitions.Transition)">
            <summary>
            Finds any properties in the old-transition that are also in the new one,
            and removes them from the old one.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionManager.#ctor">
            <summary>
            Private constructor (for singleton).
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionManager.onTimerElapsed(System.Object,System.Timers.ElapsedEventArgs)">
            <summary>
            Called when the timer ticks.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionManager.onTransitionCompleted(System.Object,Utilities.BunifuCheckBox.Transitions.Transition.Args)">
            <summary>
            Called when a transition has completed. 
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_Acceleration">
            <summary>
            Manages transitions under constant acceleration from a standing start.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Acceleration.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Acceleration.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            The initial velocity is 0, and the acceleration to get to 1.0
            at t=1.0 is 2, so the formula just becomes:
              s = t^2
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_Bounce">
            <summary>
            This transition bounces the property to a destination value and back to the
            original value. It is accelerated to the destination and then decelerated back
            as if being dropped with gravity and bouncing back against gravity.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Bounce.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the total time taken for the bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_CriticalDamping">
            <summary>
            This transition animates with an exponential decay. This has a damping effect
            similar to the motion of a needle on an electomagnetically controlled dial.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_CriticalDamping.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_CriticalDamping.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_Deceleration">
            <summary>
            Manages a transition starting from a high speed and decelerating to zero by
            the end of the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Deceleration.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Deceleration.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            The initial velocity is 2, and the acceleration to get to 1.0
            at t=1.0 is -2, so the formula becomes:
              s = t(2-t)
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_EaseInEaseOut">
            <summary>
            Manages an ease-in-ease-out transition. This accelerates during the first 
            half of the transition, and then decelerates during the second half.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_EaseInEaseOut.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time that the transition 
            will take (in milliseconds).
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_EaseInEaseOut.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Works out the percentage completed given the time passed in.
            This uses the formula:
              s = ut + 1/2at^2
            We accelerate as at the rate needed (a=4) to get to 0.5 at t=0.5, and
            then decelerate at the same rate to end up at 1.0 at t=1.0.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_Flash">
            <summary>
            This transition type 'flashes' the properties a specified number of times, ending
            up by reverting them to their initial values. You specify the number of bounces and
            the length of each bounce. 
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Flash.#ctor(System.Int32,System.Int32)">
            <summary>
            You specify the number of bounces and the time taken for each bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_Linear">
            <summary>
            This class manages a linear transition. The percentage complete for the transition
            increases linearly with time.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Linear.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the time (in milliseconds) that the
            transition will take.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_Linear.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            We return the percentage completed.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_ThrowAndCatch">
            <summary>
            This transition bounces the property to a destination value and back to the
            original value. It is decelerated to the destination and then acclerated back
            as if being thrown against gravity and then descending back with gravity.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_ThrowAndCatch.#ctor(System.Int32)">
            <summary>
            Constructor. You pass in the total time taken for the bounce.
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.TransitionType_UserDefined">
            <summary>
            This class allows you to create user-defined transition types. You specify these
            as a list of TransitionElements. Each of these defines: 
            End time , End value, Interpolation method
            
            For example, say you want to make a bouncing effect with a decay:
            
            EndTime%    EndValue%   Interpolation
            --------    ---------   -------------
            50          <USER>         <GROUP> 
            75          50          Deceleration
            85          100         Acceleration
            91          75          Deceleration
            95          100         Acceleration
            98          90          Deceleration
            100         100         Acceleration
            
            The time values are expressed as a percentage of the overall transition time. This 
            means that you can create a user-defined transition-type and then use it for transitions
            of different lengths.
            
            The values are percentages of the values between the start and end values of the properties
            being animated in the transitions. 0% is the start value and 100% is the end value.
            
            The interpolation is one of the values from the InterpolationMethod enum.
            
            So the example above accelerates to the destination (as if under gravity) by
            t=50%, then bounces back up to half the initial height by t=75%, slowing down 
            (as if against gravity) before falling down again and bouncing to decreasing 
            heights each time.
            
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_UserDefined.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_UserDefined.#ctor(System.Collections.Generic.IList{Utilities.BunifuCheckBox.Transitions.TransitionElement},System.Int32)">
            <summary>
            Constructor. You pass in the list of TransitionElements and the total time
            (in milliseconds) for the transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_UserDefined.setup(System.Collections.Generic.IList{Utilities.BunifuCheckBox.Transitions.TransitionElement},System.Int32)">
            <summary>
            Sets up the transitions. 
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_UserDefined.onTimer(System.Int32,System.Double@,System.Boolean@)">
            <summary>
            Called to find the value for the movement of properties for the time passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.TransitionType_UserDefined.getElementInfo(System.Double,System.Double@,System.Double@,System.Double@,System.Double@,Utilities.BunifuCheckBox.Transitions.InterpolationMethod@)">
            <summary>
            Returns the element info for the time-fraction passed in. 
            </summary>
        </member>
        <member name="T:Utilities.BunifuCheckBox.Transitions.Utility">
            <summary>
            A class holding static utility functions.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.getValue(System.Object,System.String)">
            <summary>
            Returns the value of the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.setValue(System.Object,System.String,System.Object)">
            <summary>
            Sets the value of the property passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.interpolate(System.Double,System.Double,System.Double)">
            <summary>
            Returns a value between d1 and d2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.interpolate(System.Int32,System.Int32,System.Double)">
            <summary>
            Returns a value betweeen i1 and i2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.interpolate(System.Single,System.Single,System.Double)">
            <summary>
            Returns a value betweeen f1 and f2 for the percentage passed in.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.convertLinearToEaseInEaseOut(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under an ease-in-ease-out transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.convertLinearToAcceleration(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under a constant acceleration transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.convertLinearToDeceleration(System.Double)">
            <summary>
            Converts a fraction representing linear time to a fraction representing
            the distance traveled under a constant deceleration transition.
            </summary>
        </member>
        <member name="M:Utilities.BunifuCheckBox.Transitions.Utility.raiseEvent``1(System.EventHandler{``0},System.Object,``0)">
            <summary>
            Fires the event passed in in a thread-safe way. 
            </summary><remarks>
            This method loops through the targets of the event and invokes each in turn. If the
            target supports ISychronizeInvoke (such as forms or controls) and is set to run 
            on a different thread, then we call BeginInvoke to marshal the event to the target
            thread. If the target does not support this interface (such as most non-form classes)
            or we are on the same thread as the target, then the event is fired on the same
            thread as this is called from.
            </remarks>
        </member>
    </members>
</doc>
