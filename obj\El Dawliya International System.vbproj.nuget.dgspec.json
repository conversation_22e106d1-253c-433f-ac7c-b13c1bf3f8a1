{"format": 1, "restore": {"E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj": {}}, "projects": {"E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj", "projectName": "El Dawliya International System", "projectPath": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\El Dawliya International System.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\My_Project\\El Dawliya International System\\تجربة\\El Dawliya International System\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"ADGV": {"target": "Package", "version": "[0.1.0.10, )"}, "ClosedXML": {"target": "Package", "version": "[0.104.2, )"}, "CrystalReports.Engine": {"target": "Package", "version": "[13.0.4003, )"}, "DataGridView-AutoFilter": {"target": "Package", "version": "[1.1.0, )"}, "ExcelDataReader": {"target": "Package", "version": "[3.7.0, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.7.0, )"}, "MaterialSkin.2": {"target": "Package", "version": "[2.3.1, )"}, "MetroModernUI": {"target": "Package", "version": "[1.4.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Office.Interop.Excel": {"target": "Package", "version": "[15.0.4795.1001, )"}, "Microsoft.Office.Interop.Word": {"target": "Package", "version": "[15.0.4797.1004, )"}, "Microsoft.SqlServer.SqlManagementObjects": {"target": "Package", "version": "[172.64.0, )"}, "Microsoft.Toolkit.Uwp.Notifications": {"target": "Package", "version": "[7.1.3, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[10.0.0-preview.1.25080.4, )"}, "System.Data.OleDb": {"target": "Package", "version": "[10.0.0-preview.1.25080.5, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "WindowsAPICodePack-Shell": {"target": "Package", "version": "[1.1.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}