﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <RootNamespace>El_Dawliya_International_System</RootNamespace>
    <UseWindowsForms>True</UseWindowsForms>
    <MyType>WindowsForms</MyType>
    <ApplicationIcon>Resources\maintenance.ico</ApplicationIcon>
    <ImportedNamespaces>System.Data=False,System.Data.OleDb=False,System.Drawing=False,System.Windows.Forms=False,Microsoft.VisualBasic=True,System=True,System.Collections=True,System.Collections.Generic=True,System.Diagnostics=True,System.Linq=True,System.Xml.Linq=True,System.Threading.Tasks=True,El Dawliya International System=True</ImportedNamespaces>
    <Platforms>AnyCPU;x86</Platforms>
    <ReferencePath>C:\Program Files (x86)\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Common\SAP BusinessObjects Enterprise XI 4.0\win64_x64\dotnet\</ReferencePath>
    <GeneratePackageOnBuild>False</GeneratePackageOnBuild>
    <Version>$(VersionPrefix)</Version>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
    <TargetFramework>net48</TargetFramework>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="Forms\Report\Attendance_Report.rpt" />
    <None Remove="Forms\Report\EmployeeBalance.rpt" />
    <None Remove="Forms\Report\Employee_Card.rpt" />
    <None Remove="Forms\Report\FormS1.rpt" />
    <None Remove="Forms\Report\FormS6.rpt" />
    <None Remove="Forms\Report\Holidays_Newspaper.rpt" />
    <None Remove="Forms\Report\Penalties_Newspaper.rpt" />
    <None Remove="Forms\Report\Reports_Machine_Problems.rpt" />
    <None Remove="Forms\Report\Rep_Contract.rpt" />
    <None Remove="Forms\Report\Rep_Contract_Main.rpt" />
    <None Remove="Forms\Report\Rep_Expiry_Notification.rpt" />
    <None Remove="Forms\Report\Rep_EznEdafa_Document_Query.rpt" />
    <None Remove="Forms\Report\Rep_EznSarf_Document_Query.rpt" />
    <None Remove="Forms\Report\Rep_InternalPhones.rpt" />
    <None Remove="Forms\Report\Rep_Last_used_item.rpt" />
    <None Remove="Forms\Report\Rep_Printing_Items.rpt" />
    <None Remove="Forms\Report\Rep_Print_Shift_Report.rpt" />
    <None Remove="Forms\Report\Rep_Program_Design_Tasks.rpt" />
    <None Remove="Forms\Report\Rep_Purchase_Orders.rpt" />
    <None Remove="Forms\Report\Rep_Task_Reminder.rpt" />
    <None Remove="Forms\Report\Rep_UserPermissions.rpt" />
    <None Remove="Forms\Report\Rpt_Employee_Note.rpt" />
    <None Remove="Forms\Report\Rpt_Meetings.rpt" />
    <None Remove="Forms\Report\Sick_Newspaper.rpt" />
    <None Remove="Forms\Report\Social_Status.rpt" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="AcroPDFLib">
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>05bfd3f1-6319-4f30-b752-c7a22889bcc4</Guid>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="AxAcroPDFLib">
      <WrapperTool>aximp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>05bfd3f1-6319-4f30-b752-c7a22889bcc4</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
    </COMReference>
    <COMReference Include="ZKFPEngXControl">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>4</VersionMajor>
      <Guid>d95cb779-00cb-4b49-97b9-9f0b61cab3c1</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="zkemkeeper">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>fe9ded34-e159-408e-8490-b720a5e632c7</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CTVLib">
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>cd6c7865-5864-11d0-abf0-0020af6b0b7a</Guid>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="AxCTVLib">
      <WrapperTool>aximp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>cd6c7865-5864-11d0-abf0-0020af6b0b7a</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
    </COMReference>
    <COMReference Include="CrystalReportViewerExportLib">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>13</VersionMajor>
      <Guid>5c21a516-c6ac-42f2-a7b7-846ffe1b122a</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalActiveXReportViewerLib13">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>13</VersionMajor>
      <Guid>b816e96d-d151-4000-badb-53a2d8f3fc65</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalReportViewerWebReportSourceLib">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>13</VersionMajor>
      <Guid>14173ba7-071d-4216-8c9b-eab29a55e2d4</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalAnalysisCommonControlsLib">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>e377f909-bcfe-4e8e-b17c-8b1cbc7c7243</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalAnalysisCommLayerLib">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>aaa3345b-2177-4d19-8d75-21688fe36251</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalAnalysisRequestModelLib">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>3fd134df-56e0-4860-b065-bf986a086176</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalAnalysisXmlSerializeLib">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>1</VersionMajor>
      <Guid>2743f9af-ed45-4ca5-8625-2709b908d63c</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CRQUERYENGINE">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>13</VersionMajor>
      <Guid>e4b81d51-fb89-4d07-84a3-00ba9b71d76e</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
    <COMReference Include="CrystalReportsLocalConnectionMgrLib">
      <WrapperTool>tlbimp</WrapperTool>
      <VersionMinor>0</VersionMinor>
      <VersionMajor>13</VersionMajor>
      <Guid>ec39f6c4-a9f2-4341-806b-1529e049a945</Guid>
      <Lcid>0</Lcid>
      <Isolated>false</Isolated>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\maintenance.ico" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\Report\Attendance_Report.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Attendance_Report.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\EmployeeBalance.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>EmployeeBalance.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Employee_Card.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Employee_Card.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\FormS1.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>FormS1.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\FormS6.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>FormS6.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Holidays_Newspaper.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Holidays_Newspaper.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Penalties_Newspaper.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Penalties_Newspaper.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Reports_Machine_Problems.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Reports_Machine_Problems.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Contract_Main.rpt">
      <LastGenOutput>Rep_Contract_Main.vb</LastGenOutput>
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_InternalPhones.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_InternalPhones.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Last_used_item.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Last_used_item.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Printing_Items.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Printing_Items.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Print_Shift_Report.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Print_Shift_Report.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Machine_Stop.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Machine_Stop.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Contract.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Contract.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Expiry_Notification.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Expiry_Notification.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_EznEdafa_Document_Query.rpt">
      <LastGenOutput>Rep_EznEdafa_Document_Query.vb</LastGenOutput>
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_EznSarf_Document_Query.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_EznSarf_Document_Query.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Program_Design_Tasks.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Program_Design_Tasks.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Purchase_Orders.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Purchase_Orders.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_Task_Reminder.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_Task_Reminder.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rep_UserPermissions.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rep_UserPermissions.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rpt_Employee_Note.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Employee_Note.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Rpt_Meetings.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Meetings.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Sick_Newspaper.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Sick_Newspaper.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Report\Social_Status.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Social_Status.vb</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Import Include="System.Data" />
    <Import Include="System.Data.OleDb" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="ADGV" Version="0.1.0.10" />
    <PackageReference Include="ClosedXML" Version="0.104.2" />
    <PackageReference Include="CrystalReports.Engine" Version="13.0.4003" />
    <PackageReference Include="DataGridView-AutoFilter" Version="1.1.0" />
    <PackageReference Include="ExcelDataReader" Version="3.7.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.7.0" />
    <PackageReference Include="MaterialSkin.2" Version="2.3.1" />
    <PackageReference Include="MetroModernUI" Version="1.4.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
    <PackageReference Include="Microsoft.Office.Interop.Word" Version="15.0.4797.1004" />
    <PackageReference Include="Microsoft.SqlServer.SqlManagementObjects" Version="172.64.0" />
    <PackageReference Include="Microsoft.Toolkit.Uwp.Notifications" Version="7.1.3" />
    <PackageReference Include="Microsoft.Windows.Compatibility" Version="10.0.0-preview.1.25080.4" />
    <PackageReference Include="System.Data.OleDb" Version="10.0.0-preview.1.25080.5" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="WindowsAPICodePack-Shell" Version="1.1.1" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Forms\HR\EditAttendanceForm.vb" />
    <Compile Update="Forms\HR\Frm_Car_Attendance_Search.vb" />
    <Compile Update="Forms\Report\Attendance_Report.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Attendance_Report.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Employee_Card.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Employee_Card.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\FormS1.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>FormS1.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\FormS6.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>FormS6.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Holidays_Newspaper.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Holidays_Newspaper.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Penalties_Newspaper.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Penalties_Newspaper.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Reports_Machine_Problems.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Reports_Machine_Problems.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Contract_Main.vb">
      <SubType>Component</SubType>
      <DependentUpon>Rep_Contract_Main.rpt</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Update="Forms\Report\Rep_InternalPhones.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_InternalPhones.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Last_used_item.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Last_used_item.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Printing_Items.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Printing_Items.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Print_Shift_Report.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Print_Shift_Report.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Machine_Stop.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Machine_Stop.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Contract.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Contract.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Expiry_Notification.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Expiry_Notification.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_EznEdafa_Document_Query.vb">
      <DependentUpon>Rep_EznEdafa_Document_Query.rpt</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Update="Forms\Report\Rep_EznSarf_Document_Query.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_EznSarf_Document_Query.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Program_Design_Tasks.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Program_Design_Tasks.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Purchase_Orders.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Purchase_Orders.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_Task_Reminder.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_Task_Reminder.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rep_UserPermissions.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rep_UserPermissions.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rpt_Employee_Note.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rpt_Employee_Note.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Rpt_Meetings.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Rpt_Meetings.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Sick_Newspaper.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Sick_Newspaper.rpt</DependentUpon>
    </Compile>
    <Compile Update="Forms\Report\Social_Status.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Social_Status.rpt</DependentUpon>
    </Compile>
    <Compile Update="My Project\Application.Designer.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Update="My Project\Resources.Designer.vb">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Update="My Project\Settings.Designer.vb">
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="My Project\Resources.resx">
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Update="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Update="My Project\Settings.settings">
      <CustomToolNamespace>My</CustomToolNamespace>
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Bunifu.UI.WinForms.BunifuCheckBox">
      <HintPath>C:\Users\<USER>\.nuget\packages\bunifu.ui.winforms\5.0.3\lib\Bunifu.UI.WinForms.BunifuCheckBox.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine">
      <HintPath>D:\Learn\نماذج تدريب مختلفة\سورس كود برنامج إدارة المبيعات بتاريخ 06-أبريل-2015\bin\Debug\CrystalDecisions.CrystalReports.Engine.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.ReportSource">
      <HintPath>D:\Learn\نماذج تدريب مختلفة\سورس كود برنامج إدارة المبيعات بتاريخ 06-أبريل-2015\bin\Debug\CrystalDecisions.ReportSource.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Shared">
      <HintPath>D:\Learn\نماذج تدريب مختلفة\سورس كود برنامج إدارة المبيعات بتاريخ 06-أبريل-2015\bin\Debug\CrystalDecisions.Shared.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.Windows.Forms">
      <HintPath>D:\Learn\نماذج تدريب مختلفة\سورس كود برنامج إدارة المبيعات بتاريخ 06-أبريل-2015\bin\Debug\CrystalDecisions.Windows.Forms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel">
      <HintPath>bin\Debug\net8.0-windows8.0\Microsoft.Office.Interop.Excel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfo">
      <HintPath>C:\Program Files (x86)\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.ConnectionInfo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Dmf">
      <HintPath>C:\Program Files (x86)\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.Dmf.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Sdk.Sfc">
      <HintPath>C:\Program Files (x86)\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.Management.Sdk.Sfc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo">
      <HintPath>C:\Program Files (x86)\Microsoft SQL Server\120\SDK\Assemblies\Microsoft.SqlServer.Smo.dll</HintPath>
    </Reference>
    <Reference Include="XanderUI">
      <HintPath>..\XanderUI\XanderUI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{c0c07587-41a7-46c8-8fbd-3f9c8ebe2ddc}" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Core\Database\" />
  </ItemGroup>
</Project>