''' <summary>
''' Generic repository interface following Repository pattern
''' Provides basic CRUD operations for all entities
''' </summary>
''' <typeparam name="T">Entity type</typeparam>
Public Interface IRepository(Of T As Class)
    ''' <summary>
    ''' Get entity by ID
    ''' </summary>
    ''' <param name="id">Entity ID</param>
    ''' <returns>Entity or Nothing if not found</returns>
    Function GetById(id As Object) As T
    
    ''' <summary>
    ''' Get entity by ID asynchronously
    ''' </summary>
    ''' <param name="id">Entity ID</param>
    ''' <returns>Task containing entity or Nothing if not found</returns>
    Function GetByIdAsync(id As Object) As Task(Of T)
    
    ''' <summary>
    ''' Get all entities
    ''' </summary>
    ''' <returns>Collection of all entities</returns>
    Function GetAll() As IEnumerable(Of T)
    
    ''' <summary>
    ''' Get all entities asynchronously
    ''' </summary>
    ''' <returns>Task containing collection of all entities</returns>
    Function GetAllAsync() As Task(Of IEnumerable(Of T))
    
    ''' <summary>
    ''' Find entities matching predicate
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Collection of matching entities</returns>
    Function Find(predicate As Func(Of T, Boolean)) As IEnumerable(Of T)
    
    ''' <summary>
    ''' Find entities matching predicate asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing collection of matching entities</returns>
    Function FindAsync(predicate As Func(Of T, Boolean)) As Task(Of IEnumerable(Of T))
    
    ''' <summary>
    ''' Get first entity matching predicate
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>First matching entity or Nothing</returns>
    Function FirstOrDefault(predicate As Func(Of T, Boolean)) As T
    
    ''' <summary>
    ''' Get first entity matching predicate asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing first matching entity or Nothing</returns>
    Function FirstOrDefaultAsync(predicate As Func(Of T, Boolean)) As Task(Of T)
    
    ''' <summary>
    ''' Add new entity
    ''' </summary>
    ''' <param name="entity">Entity to add</param>
    ''' <returns>Added entity with generated ID</returns>
    Function Add(entity As T) As T
    
    ''' <summary>
    ''' Add new entity asynchronously
    ''' </summary>
    ''' <param name="entity">Entity to add</param>
    ''' <returns>Task containing added entity with generated ID</returns>
    Function AddAsync(entity As T) As Task(Of T)
    
    ''' <summary>
    ''' Add multiple entities
    ''' </summary>
    ''' <param name="entities">Entities to add</param>
    Sub AddRange(entities As IEnumerable(Of T))
    
    ''' <summary>
    ''' Add multiple entities asynchronously
    ''' </summary>
    ''' <param name="entities">Entities to add</param>
    ''' <returns>Task representing the operation</returns>
    Function AddRangeAsync(entities As IEnumerable(Of T)) As Task
    
    ''' <summary>
    ''' Update existing entity
    ''' </summary>
    ''' <param name="entity">Entity to update</param>
    Sub Update(entity As T)
    
    ''' <summary>
    ''' Update existing entity asynchronously
    ''' </summary>
    ''' <param name="entity">Entity to update</param>
    ''' <returns>Task representing the operation</returns>
    Function UpdateAsync(entity As T) As Task
    
    ''' <summary>
    ''' Remove entity
    ''' </summary>
    ''' <param name="entity">Entity to remove</param>
    Sub Remove(entity As T)
    
    ''' <summary>
    ''' Remove entity by ID
    ''' </summary>
    ''' <param name="id">ID of entity to remove</param>
    Sub Remove(id As Object)
    
    ''' <summary>
    ''' Remove entity asynchronously
    ''' </summary>
    ''' <param name="entity">Entity to remove</param>
    ''' <returns>Task representing the operation</returns>
    Function RemoveAsync(entity As T) As Task
    
    ''' <summary>
    ''' Remove entity by ID asynchronously
    ''' </summary>
    ''' <param name="id">ID of entity to remove</param>
    ''' <returns>Task representing the operation</returns>
    Function RemoveAsync(id As Object) As Task
    
    ''' <summary>
    ''' Remove multiple entities
    ''' </summary>
    ''' <param name="entities">Entities to remove</param>
    Sub RemoveRange(entities As IEnumerable(Of T))
    
    ''' <summary>
    ''' Remove multiple entities asynchronously
    ''' </summary>
    ''' <param name="entities">Entities to remove</param>
    ''' <returns>Task representing the operation</returns>
    Function RemoveRangeAsync(entities As IEnumerable(Of T)) As Task
    
    ''' <summary>
    ''' Count total entities
    ''' </summary>
    ''' <returns>Total count</returns>
    Function Count() As Integer
    
    ''' <summary>
    ''' Count entities matching predicate
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Count of matching entities</returns>
    Function Count(predicate As Func(Of T, Boolean)) As Integer
    
    ''' <summary>
    ''' Count total entities asynchronously
    ''' </summary>
    ''' <returns>Task containing total count</returns>
    Function CountAsync() As Task(Of Integer)
    
    ''' <summary>
    ''' Count entities matching predicate asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing count of matching entities</returns>
    Function CountAsync(predicate As Func(Of T, Boolean)) As Task(Of Integer)
    
    ''' <summary>
    ''' Check if any entities exist
    ''' </summary>
    ''' <returns>True if any entities exist</returns>
    Function Any() As Boolean
    
    ''' <summary>
    ''' Check if any entities matching predicate exist
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>True if any matching entities exist</returns>
    Function Any(predicate As Func(Of T, Boolean)) As Boolean
    
    ''' <summary>
    ''' Check if any entities exist asynchronously
    ''' </summary>
    ''' <returns>Task containing true if any entities exist</returns>
    Function AnyAsync() As Task(Of Boolean)
    
    ''' <summary>
    ''' Check if any entities matching predicate exist asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing true if any matching entities exist</returns>
    Function AnyAsync(predicate As Func(Of T, Boolean)) As Task(Of Boolean)
    
    ''' <summary>
    ''' Get paged results
    ''' </summary>
    ''' <param name="pageNumber">Page number (1-based)</param>
    ''' <param name="pageSize">Number of items per page</param>
    ''' <returns>Paged result</returns>
    Function GetPaged(pageNumber As Integer, pageSize As Integer) As PagedResult(Of T)
    
    ''' <summary>
    ''' Get paged results asynchronously
    ''' </summary>
    ''' <param name="pageNumber">Page number (1-based)</param>
    ''' <param name="pageSize">Number of items per page</param>
    ''' <returns>Task containing paged result</returns>
    Function GetPagedAsync(pageNumber As Integer, pageSize As Integer) As Task(Of PagedResult(Of T))
    
    ''' <summary>
    ''' Execute raw SQL query
    ''' </summary>
    ''' <param name="sql">SQL query</param>
    ''' <param name="parameters">Query parameters</param>
    ''' <returns>Collection of entities</returns>
    Function ExecuteQuery(sql As String, ParamArray parameters() As Object) As IEnumerable(Of T)
    
    ''' <summary>
    ''' Execute raw SQL query asynchronously
    ''' </summary>
    ''' <param name="sql">SQL query</param>
    ''' <param name="parameters">Query parameters</param>
    ''' <returns>Task containing collection of entities</returns>
    Function ExecuteQueryAsync(sql As String, ParamArray parameters() As Object) As Task(Of IEnumerable(Of T))
    
    ''' <summary>
    ''' Execute raw SQL command
    ''' </summary>
    ''' <param name="sql">SQL command</param>
    ''' <param name="parameters">Command parameters</param>
    ''' <returns>Number of affected rows</returns>
    Function ExecuteCommand(sql As String, ParamArray parameters() As Object) As Integer
    
    ''' <summary>
    ''' Execute raw SQL command asynchronously
    ''' </summary>
    ''' <param name="sql">SQL command</param>
    ''' <param name="parameters">Command parameters</param>
    ''' <returns>Task containing number of affected rows</returns>
    Function ExecuteCommandAsync(sql As String, ParamArray parameters() As Object) As Task(Of Integer)
End Interface

''' <summary>
''' Paged result container
''' </summary>
''' <typeparam name="T">Entity type</typeparam>
Public Class PagedResult(Of T)
    Public Property Items As IEnumerable(Of T)
    Public Property TotalCount As Integer
    Public Property PageNumber As Integer
    Public Property PageSize As Integer
    Public Property TotalPages As Integer
    Public Property HasPreviousPage As Boolean
    Public Property HasNextPage As Boolean
    
    Public Sub New(items As IEnumerable(Of T), totalCount As Integer, pageNumber As Integer, pageSize As Integer)
        Me.Items = items
        Me.TotalCount = totalCount
        Me.PageNumber = pageNumber
        Me.PageSize = pageSize
        Me.TotalPages = Math.Ceiling(totalCount / pageSize)
        Me.HasPreviousPage = pageNumber > 1
        Me.HasNextPage = pageNumber < TotalPages
    End Sub
End Class
