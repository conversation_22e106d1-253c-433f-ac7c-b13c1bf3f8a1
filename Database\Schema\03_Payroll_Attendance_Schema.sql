-- =====================================================
-- El Dawliya International System - Payroll & Attendance Schema
-- Version: 2.0
-- Date: 2025-01-30
-- Description: Enhanced Payroll and Attendance Management
-- =====================================================

-- =====================================================
-- Enhanced Attendance System
-- =====================================================

-- Attendance devices (ZK biometric devices)
CREATE TABLE HR.AttendanceDevices (
    DeviceID INT IDENTITY(1,1) PRIMARY KEY,
    DeviceName NVARCHAR(50) NOT NULL,
    DeviceIP NVARCHAR(15) NOT NULL,
    DevicePort INT NOT NULL DEFAULT 4370,
    Location NVARCHAR(100),
    DeviceType NVARCHAR(20) DEFAULT 'ZK',
    IsActive BIT NOT NULL DEFAULT 1,
    LastSyncTime DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_AttendanceDevices_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Enhanced attendance records
CREATE TABLE HR.AttendanceRecords (
    AttendanceID BIGINT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    AttendanceDate DATE NOT NULL,
    CheckInTime DATETIME2,
    CheckOutTime DATETIME2,
    WorkingHours AS (
        CASE 
            WHEN CheckInTime IS NOT NULL AND CheckOutTime IS NOT NULL 
            THEN DATEDIFF(MINUTE, CheckInTime, CheckOutTime) / 60.0
            ELSE 0
        END
    ) PERSISTED,
    ScheduledStartTime TIME,
    ScheduledEndTime TIME,
    ScheduledHours DECIMAL(4,2),
    LateMinutes AS (
        CASE 
            WHEN CheckInTime IS NOT NULL AND ScheduledStartTime IS NOT NULL 
                AND CAST(CheckInTime AS TIME) > ScheduledStartTime
            THEN DATEDIFF(MINUTE, ScheduledStartTime, CAST(CheckInTime AS TIME))
            ELSE 0
        END
    ) PERSISTED,
    EarlyLeaveMinutes AS (
        CASE 
            WHEN CheckOutTime IS NOT NULL AND ScheduledEndTime IS NOT NULL 
                AND CAST(CheckOutTime AS TIME) < ScheduledEndTime
            THEN DATEDIFF(MINUTE, CAST(CheckOutTime AS TIME), ScheduledEndTime)
            ELSE 0
        END
    ) PERSISTED,
    OvertimeMinutes AS (
        CASE 
            WHEN CheckOutTime IS NOT NULL AND ScheduledEndTime IS NOT NULL 
                AND CAST(CheckOutTime AS TIME) > ScheduledEndTime
            THEN DATEDIFF(MINUTE, ScheduledEndTime, CAST(CheckOutTime AS TIME))
            ELSE 0
        END
    ) PERSISTED,
    AttendanceStatus NVARCHAR(20) NOT NULL DEFAULT 'Present' 
        CHECK (AttendanceStatus IN ('Present', 'Absent', 'Late', 'Half Day', 'Leave', 'Holiday')),
    DeviceID INT,
    IsManualEntry BIT NOT NULL DEFAULT 0,
    Notes NVARCHAR(500),
    ApprovedBy INT,
    ApprovedDate DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    CONSTRAINT FK_AttendanceRecords_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_AttendanceRecords_Device FOREIGN KEY (DeviceID) REFERENCES HR.AttendanceDevices(DeviceID),
    CONSTRAINT FK_AttendanceRecords_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_AttendanceRecords_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT UQ_AttendanceRecords_EmployeeDate UNIQUE (EmployeeID, AttendanceDate)
)
GO

-- Shift patterns for different work schedules
CREATE TABLE HR.ShiftPatterns (
    ShiftID INT IDENTITY(1,1) PRIMARY KEY,
    ShiftName NVARCHAR(50) NOT NULL,
    StartTime TIME NOT NULL,
    EndTime TIME NOT NULL,
    BreakDuration INT NOT NULL DEFAULT 0, -- in minutes
    GracePeriod INT NOT NULL DEFAULT 15, -- late tolerance in minutes
    IsNightShift BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_ShiftPatterns_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Employee shift assignments
CREATE TABLE HR.EmployeeShifts (
    AssignmentID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    ShiftID INT NOT NULL,
    EffectiveDate DATE NOT NULL,
    EndDate DATE,
    IsActive BIT NOT NULL DEFAULT 1,
    AssignedBy INT,
    AssignedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_EmployeeShifts_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_EmployeeShifts_Shift FOREIGN KEY (ShiftID) REFERENCES HR.ShiftPatterns(ShiftID),
    CONSTRAINT FK_EmployeeShifts_AssignedBy FOREIGN KEY (AssignedBy) REFERENCES Security.Users(UserID)
)
GO

-- =====================================================
-- Payroll System
-- =====================================================

-- Salary components (basic, allowances, deductions)
CREATE TABLE HR.SalaryComponents (
    ComponentID INT IDENTITY(1,1) PRIMARY KEY,
    ComponentCode NVARCHAR(20) NOT NULL UNIQUE,
    ComponentName NVARCHAR(100) NOT NULL,
    ComponentNameEnglish NVARCHAR(100),
    ComponentType NVARCHAR(20) NOT NULL CHECK (ComponentType IN ('Basic', 'Allowance', 'Deduction', 'Overtime', 'Bonus')),
    CalculationType NVARCHAR(20) NOT NULL CHECK (CalculationType IN ('Fixed', 'Percentage', 'Formula', 'Attendance')),
    DefaultValue DECIMAL(10,2),
    Formula NVARCHAR(255),
    IsTaxable BIT NOT NULL DEFAULT 1,
    IsStatutory BIT NOT NULL DEFAULT 0, -- for legal deductions like tax, insurance
    DisplayOrder INT,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_SalaryComponents_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Employee salary structure
CREATE TABLE HR.EmployeeSalaryStructure (
    StructureID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    ComponentID INT NOT NULL,
    Amount DECIMAL(10,2) NOT NULL,
    Percentage DECIMAL(5,2),
    EffectiveDate DATE NOT NULL,
    EndDate DATE,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_EmployeeSalaryStructure_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_EmployeeSalaryStructure_Component FOREIGN KEY (ComponentID) REFERENCES HR.SalaryComponents(ComponentID),
    CONSTRAINT FK_EmployeeSalaryStructure_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Payroll periods
CREATE TABLE HR.PayrollPeriods (
    PeriodID INT IDENTITY(1,1) PRIMARY KEY,
    PeriodName NVARCHAR(50) NOT NULL,
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    PayDate DATE NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'Open' CHECK (Status IN ('Open', 'Processing', 'Approved', 'Paid', 'Closed')),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ProcessedDate DATETIME2,
    ProcessedBy INT,
    ApprovedDate DATETIME2,
    ApprovedBy INT,
    CONSTRAINT FK_PayrollPeriods_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_PayrollPeriods_ProcessedBy FOREIGN KEY (ProcessedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_PayrollPeriods_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Security.Users(UserID)
)
GO

-- Payroll calculations
CREATE TABLE HR.PayrollCalculations (
    CalculationID BIGINT IDENTITY(1,1) PRIMARY KEY,
    PeriodID INT NOT NULL,
    EmployeeID INT NOT NULL,
    ComponentID INT NOT NULL,
    Amount DECIMAL(10,2) NOT NULL,
    Quantity DECIMAL(8,2) DEFAULT 1,
    Rate DECIMAL(10,2),
    CalculationBase DECIMAL(10,2),
    Formula NVARCHAR(255),
    Notes NVARCHAR(255),
    CONSTRAINT FK_PayrollCalculations_Period FOREIGN KEY (PeriodID) REFERENCES HR.PayrollPeriods(PeriodID),
    CONSTRAINT FK_PayrollCalculations_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_PayrollCalculations_Component FOREIGN KEY (ComponentID) REFERENCES HR.SalaryComponents(ComponentID),
    CONSTRAINT UQ_PayrollCalculations_PeriodEmployeeComponent UNIQUE (PeriodID, EmployeeID, ComponentID)
)
GO

-- Payroll summary
CREATE TABLE HR.PayrollSummary (
    SummaryID BIGINT IDENTITY(1,1) PRIMARY KEY,
    PeriodID INT NOT NULL,
    EmployeeID INT NOT NULL,
    GrossSalary DECIMAL(12,2) NOT NULL,
    TotalAllowances DECIMAL(12,2) NOT NULL DEFAULT 0,
    TotalDeductions DECIMAL(12,2) NOT NULL DEFAULT 0,
    TaxableIncome DECIMAL(12,2) NOT NULL DEFAULT 0,
    IncomeTax DECIMAL(12,2) NOT NULL DEFAULT 0,
    NetSalary DECIMAL(12,2) NOT NULL,
    WorkingDays DECIMAL(5,2),
    ActualWorkingDays DECIMAL(5,2),
    AbsentDays DECIMAL(5,2),
    LeaveDays DECIMAL(5,2),
    OvertimeHours DECIMAL(6,2),
    LateHours DECIMAL(6,2),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Calculated' CHECK (Status IN ('Calculated', 'Approved', 'Paid')),
    CalculatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ApprovedDate DATETIME2,
    ApprovedBy INT,
    PaidDate DATETIME2,
    PaymentMethod NVARCHAR(20),
    PaymentReference NVARCHAR(50),
    CONSTRAINT FK_PayrollSummary_Period FOREIGN KEY (PeriodID) REFERENCES HR.PayrollPeriods(PeriodID),
    CONSTRAINT FK_PayrollSummary_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_PayrollSummary_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT UQ_PayrollSummary_PeriodEmployee UNIQUE (PeriodID, EmployeeID)
)
GO

-- Overtime records
CREATE TABLE HR.OvertimeRecords (
    OvertimeID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    OvertimeDate DATE NOT NULL,
    StartTime DATETIME2 NOT NULL,
    EndTime DATETIME2 NOT NULL,
    TotalHours AS (DATEDIFF(MINUTE, StartTime, EndTime) / 60.0) PERSISTED,
    OvertimeType NVARCHAR(20) NOT NULL CHECK (OvertimeType IN ('Regular', 'Holiday', 'Weekend')),
    Rate DECIMAL(5,2) NOT NULL DEFAULT 1.5, -- multiplier
    Reason NVARCHAR(255),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending' CHECK (Status IN ('Pending', 'Approved', 'Rejected')),
    RequestedBy INT,
    ApprovedBy INT,
    ApprovedDate DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_OvertimeRecords_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_OvertimeRecords_RequestedBy FOREIGN KEY (RequestedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_OvertimeRecords_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES HR.Employees(EmployeeID)
)
GO

-- Bonus and incentives
CREATE TABLE HR.BonusRecords (
    BonusID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeID INT NOT NULL,
    BonusType NVARCHAR(50) NOT NULL,
    Amount DECIMAL(10,2) NOT NULL,
    Percentage DECIMAL(5,2),
    BasedOn NVARCHAR(50), -- 'Basic Salary', 'Performance', 'Fixed Amount'
    Reason NVARCHAR(255),
    EffectiveDate DATE NOT NULL,
    PeriodID INT, -- if linked to specific payroll period
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending' CHECK (Status IN ('Pending', 'Approved', 'Paid')),
    ApprovedBy INT,
    ApprovedDate DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_BonusRecords_Employee FOREIGN KEY (EmployeeID) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_BonusRecords_Period FOREIGN KEY (PeriodID) REFERENCES HR.PayrollPeriods(PeriodID),
    CONSTRAINT FK_BonusRecords_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES HR.Employees(EmployeeID),
    CONSTRAINT FK_BonusRecords_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- Attendance indexes
CREATE NONCLUSTERED INDEX IX_AttendanceRecords_EmployeeID ON HR.AttendanceRecords(EmployeeID)
CREATE NONCLUSTERED INDEX IX_AttendanceRecords_AttendanceDate ON HR.AttendanceRecords(AttendanceDate)
CREATE NONCLUSTERED INDEX IX_AttendanceRecords_Status ON HR.AttendanceRecords(AttendanceStatus)

-- Payroll indexes
CREATE NONCLUSTERED INDEX IX_PayrollCalculations_PeriodID ON HR.PayrollCalculations(PeriodID)
CREATE NONCLUSTERED INDEX IX_PayrollCalculations_EmployeeID ON HR.PayrollCalculations(EmployeeID)
CREATE NONCLUSTERED INDEX IX_PayrollSummary_PeriodID ON HR.PayrollSummary(PeriodID)
CREATE NONCLUSTERED INDEX IX_PayrollSummary_EmployeeID ON HR.PayrollSummary(EmployeeID)

-- Leave indexes
CREATE NONCLUSTERED INDEX IX_LeaveRequests_EmployeeID ON HR.LeaveRequests(EmployeeID)
CREATE NONCLUSTERED INDEX IX_LeaveRequests_Status ON HR.LeaveRequests(Status)
CREATE NONCLUSTERED INDEX IX_LeaveRequests_StartDate ON HR.LeaveRequests(StartDate)

-- Performance indexes
CREATE NONCLUSTERED INDEX IX_PerformanceEvaluations_EmployeeID ON HR.PerformanceEvaluations(EmployeeID)
CREATE NONCLUSTERED INDEX IX_PerformanceEvaluations_CycleID ON HR.PerformanceEvaluations(CycleID)
CREATE NONCLUSTERED INDEX IX_PerformanceEvaluations_Status ON HR.PerformanceEvaluations(Status)

GO
