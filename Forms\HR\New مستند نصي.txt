Imports System.Data.SqlClient
Imports System.IO
Imports System.Text
Public Class Frm_Add_Employee
    Public ConStr As String = GetConnectionString()
    Public Con As New SqlConnection(ConStr)
    Public Sub ClearControls()
        Me.Emp_ID.Text = vbNullString
        Me.Emp_Full_Name.Text = vbNullString
        Me.Emp_Phone1.Text = vbNullString
        Me.Emp_Address.Text = vbNullString
        Me.Emp_Marital_Status.Text = vbNullString
        Me.Emp_Nationality.Text = vbNullString
        Me.National_ID.Text = vbNullString
        Me.Date_Birth.Value = Today
        Me.Emp_Image.Image = My.Resources.icons8_add_image_96
        Me.Dept_Code.Text = vbNullString
        Me.Jop_Code.Text = vbNullString
        Me.Emp_Date_Hiring.Value = Today
        Me.Emp_Car.Text = vbNullString
        Me.Car_Ride_Time.Value = Today
        Me.Car_Pick_Up_Point.Text = vbNullString
    End Sub
    Public Sub fillcmb_Tbl_Car(ByVal cmb As ComboBox)
        Dim DT As New DataTable
        Dim DA As New SqlDataAdapter
        DT.Clear()
        DA = New SqlDataAdapter("Select * FROM Tbl_Car ", Con)
        DA.Fill(DT)
        If DT.Rows.Count > 0 Then
            cmb.DataSource = DT
            cmb.ValueMember = "Car_ID"
            cmb.DisplayMember = "Car_Name"
        Else
            cmb.DataSource = Nothing
        End If
    End Sub
    Public Sub fillcmb_Tbl_Waiting_Point(ByVal cmb As ComboBox)
        Dim DT As New DataTable
        Dim DA As New SqlDataAdapter
        DT.Clear()
        DA = New SqlDataAdapter("Select * FROM Tbl_Waiting_Point ", Con)
        DA.Fill(DT)
        If DT.Rows.Count > 0 Then
            cmb.DataSource = DT
            cmb.ValueMember = "Waiting_Point_ID"
            cmb.DisplayMember = "Waiting_Point_Name"
        Else
            cmb.DataSource = Nothing
        End If
    End Sub
    Public Sub Insert_Tbl_Employee(ByVal Emp_ID As Int32, ByVal Emp_Full_Name As String, ByVal Emp_Phone1 As String, ByVal Emp_Address As String, ByVal Emp_Marital_Status As String, ByVal Emp_Nationality As String, ByVal National_ID As String, ByVal Date_Birth As DateTime, ByVal Place_Birth As String, ByVal Emp_Image As PictureBox, ByVal Dept_Code As Int32, ByVal Dept_Name As String, ByVal Jop_Code As Int32, ByVal Jop_Name As String, ByVal Emp_Date_Hiring As DateTime, ByVal Emp_Car As String, ByVal Car_Ride_Time As DateTime, ByVal Car_Pick_Up_Point As String, ByVal Working_Condition As String)
        Try
            Dim Cmd As New SqlCommand
            With Cmd
                .Connection = Con
                .CommandType = CommandType.Text
                '.CommandText = "Insert Into Tbl_Employee (Emp_ID,Emp_Full_Name,Emp_Phone1,Emp_Address,Emp_Marital_Status,Emp_Nationality,National_ID,Date_Birth,Emp_Image,Dept_Code,Jop_Code,Emp_Date_Hiring,Emp_Car,Car_Ride_Time,Car_Pick_Up_Point) values(@Emp_ID,@Emp_Full_Name,@Emp_Phone1,@Emp_Address,@Emp_Marital_Status,@Emp_Nationality,@National_ID,@Date_Birth,@Emp_Image,@Dept_Code,@Jop_Code,@Emp_Date_Hiring,@Emp_Car,@Car_Ride_Time,@Car_Pick_Up_Point)"
                .CommandText = "Insert Into Tbl_Employee (Emp_ID,Emp_Full_Name,Emp_Phone1,Emp_Address,Emp_Marital_Status,Emp_Nationality,National_ID,Date_Birth,Place_Birth,Emp_Image,Dept_Code,Dept_Name,Jop_Code,Jop_Name,Emp_Date_Hiring,Emp_Car,Car_Ride_Time,Car_Pick_Up_Point,Working_Condition) values(@Emp_ID,@Emp_Full_Name,@Emp_Phone1,@Emp_Address,@Emp_Marital_Status,@Emp_Nationality,@National_ID,@Date_Birth,@Place_Birth,@Emp_Image,@Dept_Code,@Dept_Name,@Jop_Code,@Jop_Name,@Emp_Date_Hiring,@Emp_Car,@Car_Ride_Time,@Car_Pick_Up_Point,@Working_Condition)"
                .Parameters.Clear()
                .Parameters.AddWithValue("@Emp_ID", SqlDbType.Int).Value = Emp_ID
                .Parameters.AddWithValue("@Emp_Full_Name", SqlDbType.VarChar).Value = Emp_Full_Name
                .Parameters.AddWithValue("@Emp_Phone1", SqlDbType.VarChar).Value = Emp_Phone1
                .Parameters.AddWithValue("@Emp_Address", SqlDbType.VarChar).Value = Emp_Address
                .Parameters.AddWithValue("@Emp_Marital_Status", SqlDbType.VarChar).Value = Emp_Marital_Status
                .Parameters.AddWithValue("@Emp_Nationality", SqlDbType.VarChar).Value = Emp_Nationality
                .Parameters.AddWithValue("@National_ID", SqlDbType.VarChar).Value = National_ID
                .Parameters.AddWithValue("@Date_Birth", SqlDbType.Date).Value = Date_Birth
                .Parameters.AddWithValue("@Place_Birth", SqlDbType.VarChar).Value = Place_Birth
                If Emp_Image.Image IsNot Nothing AndAlso Emp_Image.Image IsNot Nothing Then
                    ' قم بتحميل الصورة وإدراجها في قاعدة البيانات
                    Dim ms As New MemoryStream()
                    Dim bmpImage As New Bitmap(Emp_Image.Image)
                    bmpImage.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg)
                    Dim data As Byte() = ms.ToArray()
                    Dim p As New SqlParameter("@Emp_Image", SqlDbType.Image)
                    p.Value = data
                    .Parameters.Add(p)
                Else
                    ' اترك قيمة الحقل NULL
                    .Parameters.AddWithValue("@Emp_Image", DBNull.Value)
                End If
                .Parameters.AddWithValue("@Dept_Code", SqlDbType.Int).Value = Dept_Code
                .Parameters.AddWithValue("@Dept_Name", SqlDbType.VarChar).Value = Dept_Name
                .Parameters.AddWithValue("@Jop_Code", SqlDbType.Int).Value = Jop_Code
                .Parameters.AddWithValue("@Jop_Name", SqlDbType.VarChar).Value = Jop_Name
                .Parameters.AddWithValue("@Emp_Date_Hiring", SqlDbType.Date).Value = Emp_Date_Hiring
                .Parameters.AddWithValue("@Emp_Car", SqlDbType.VarChar).Value = Emp_Car
                .Parameters.AddWithValue("@Car_Ride_Time", SqlDbType.Date).Value = Car_Ride_Time
                .Parameters.AddWithValue("@Car_Pick_Up_Point", SqlDbType.VarChar).Value = Car_Pick_Up_Point
                .Parameters.AddWithValue("@Working_Condition", SqlDbType.VarChar).Value = Working_Condition
            End With
            If Con.State = ConnectionState.Closed Then
                Con.Open()
            End If
            Cmd.ExecuteNonQuery()
            MsgBox("تم إضافة السجل بنجاح", MsgBoxStyle.Information, "حفظ")
        Catch ex As Exception
            MsgBox("هذا الكود تم اضافتة مسبقاً: " & ex.Message, MsgBoxStyle.Critical, "خطأ")
        Finally
            If Con.State = ConnectionState.Open Then
                Con.Close()
            End If
        End Try
    End Sub
    Public Sub fillcmb_Tbl_Department(ByVal cmb As ComboBox)
        Dim DT As New DataTable
        Dim DA As New SqlDataAdapter
        DT.Clear()
        DA = New SqlDataAdapter("Select * FROM Tbl_Department ", Con)
        DA.Fill(DT)
        If DT.Rows.Count > 0 Then
            cmb.DataSource = DT
            cmb.ValueMember = "Dept_Code"
            cmb.DisplayMember = "Dept_Name"
        Else
            cmb.DataSource = Nothing
        End If
    End Sub

    Public Sub fillcmb_Tbl_Jop(ByVal cmb As ComboBox)
        Dim DT As New DataTable
        Dim DA As New SqlDataAdapter
        DT.Clear()
        DA = New SqlDataAdapter("Select * FROM Tbl_Jop ", Con)
        DA.Fill(DT)
        If DT.Rows.Count > 0 Then
            cmb.DataSource = DT
            cmb.ValueMember = "Jop_Code"
            cmb.DisplayMember = "Jop_Name"
        Else
            cmb.DataSource = Nothing
        End If
    End Sub

    Private Sub Emp_Full_Name_KeyPress(sender As Object, e As KeyPressEventArgs) Handles Emp_Full_Name.KeyPress
        'هذا الكود لكتابة احرف فقط
        If Not Char.IsLetter(e.KeyChar) And Not e.KeyChar = " " And Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If
    End Sub

    Private Sub Emp_ID_KeyPress(sender As Object, e As KeyPressEventArgs) Handles Emp_ID.KeyPress
        'هذا الكود لكتابة ارقام فقط
        Dim utf8 As Encoding = Encoding.UTF8
        Dim asciiBytes() As Byte = utf8.GetBytes(e.KeyChar)
        If asciiBytes.Length <> 1 Then
            e.Handled = True
        End If
    End Sub
    Private Sub Emp_Image_Click(sender As Object, e As EventArgs) Handles Emp_Image.Click
        ChoosePicture(Emp_Image)
    End Sub

    Private Sub BtnSave_Click(sender As Object, e As EventArgs) Handles BtnSave.Click
        Dim MovementDescription As String = " تم اضافة الموظف  " + Emp_Full_Name.Text
        Dim DeviceName As String = Environ$("computername")
        If Emp_ID.Text = vbNullString Or Emp_Full_Name.Text = vbNullString Then
            MessageBox.Show("عفواً ، قم بتعبئة كل الحقول", "تنبيه ", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RightAlign)
            Exit Sub
        End If
        Dim Working_Condition_Value As String = "سارى"
        Insert_Tbl_Employee(Emp_ID.Text, Emp_Full_Name.Text, Emp_Phone1.Text, Emp_Address.Text, Emp_Marital_Status.Text, Emp_Nationality.Text, National_ID.Text, Date_Birth.Value, Place_Birth.Text, Emp_Image, Int(Dept_Code.SelectedValue), Dept_Code.Text, Int(Jop_Code.SelectedValue), Jop_Code.Text, Emp_Date_Hiring.Value, Emp_Car.Text, Car_Ride_Time.Value, Car_Pick_Up_Point.Text, Working_Condition_Value)
        Insert_MovementHistory(MovementDescription, DateTime.Now, loggedInUserName, DeviceName, Me.Name, "BtnSave")
        ClearControls()
        Emp_ID.Focus()
    End Sub

    Private Sub Frm_Add_Employee_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'ChangeScreenResolution(Me)

        ClearControls()
        fillcmb_Tbl_Jop(Jop_Code)
        fillcmb_Tbl_Department(Dept_Code)
        fillcmb_Tbl_Car(Emp_Car)
        fillcmb_Tbl_Waiting_Point(Car_Pick_Up_Point)
        Emp_ID.Focus()
    End Sub

    Private Sub PictureBox2_Click(sender As Object, e As EventArgs) Handles PictureBox2.Click
        Me.Close()
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs)
        Frm_Car.ShowDialog()
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs)
        Frm_Waiting_Point.ShowDialog()
    End Sub

    Private Sub Frm_Add_Employee_Closed(sender As Object, e As EventArgs) Handles Me.Closed
        RestoreScreenResolution(Me)
    End Sub
End Class