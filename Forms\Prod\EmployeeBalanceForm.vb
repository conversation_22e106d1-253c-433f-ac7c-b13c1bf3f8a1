﻿'Imports System.Data.SqlClient

'Public Class EmployeeBalanceForm
'    Dim connectionString As String = "Server=192.168.1.10;Database=PY;User ID=admin;Password=************"

'    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
'        Dim empNo As String = txtEmpNo.Text.Trim()
'        If empNo = "" Then
'            MessageBox.Show("من فضلك أدخل رقم الموظف.")
'            Return
'        End If

'        Dim query As String =
'            "SELECT " &
'            "E.EMP_NO, " &
'            "E.EMP_NAME AS NAME_AR, " &
'            "ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0) AS SANWY_E3T, " &
'            "ISNULL(E.VAC_RG_P, 0) AS MORAHAL_E3T, " &
'            "ISNULL(E.VAC_RG_T, 0) AS MSHOB_E3T, " &
'            "ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0) - ISNULL(E.VAC_RG_T, 0) AS RASED_E3T, " &
'            "ISNULL(E.VAC_EX_Y, 0) + ISNULL(E.VAC_EX_P, 0) AS SANWT_ARDA, " &
'            "ISNULL(E.VAC_EX_P, 0) AS MORAHAL_ARDA, " &
'            "ISNULL(E.VAC_EX_T, 0) AS MSHOB_ARDA, " &
'            "ISNULL(E.VAC_EX_Y, 0) + ISNULL(E.VAC_EX_P, 0) - ISNULL(E.VAC_EX_T, 0) AS RASED_ARDA, " &
'            "ISNULL(E.VAC_SK_Y, 0) + ISNULL(E.VAC_SK_P, 0) AS SANWY_MRDA, " &
'            "ISNULL(E.VAC_SK_P, 0) AS MORAHAL_MRDA, " &
'            "ISNULL(E.VAC_SK_T, 0) AS MSHOB_MRDA, " &
'            "ISNULL(E.VAC_SK_Y, 0) + ISNULL(E.VAC_SK_P, 0) - ISNULL(E.VAC_SK_T, 0) AS RASED_MRDA, " &
'            "ISNULL(E.TKT_TR_Y, 0) AS SANWY_MRDA75, " &
'            "ISNULL(E.TKT_TR_P, 0) AS MORAHAL_MRDA75, " &
'            "ISNULL(E.TKT_TR_T, 0) AS MSHOB_MRDA75, " &
'            "ISNULL(E.TKT_TR_Y, 0) - ISNULL(E.TKT_TR_T, 0) AS RASED_MRDA75 " &
'            "FROM PYEMPMFH E " &
'            "WHERE E.EMP_NO = @EmpNo AND E.SAL_STATUS = 1"

'        Try
'            Using conn As New SqlConnection(connectionString)
'                Using cmd As New SqlCommand(query, conn)
'                    cmd.Parameters.AddWithValue("@EmpNo", empNo)
'                    conn.Open()
'                    Using reader As SqlDataReader = cmd.ExecuteReader()
'                        If reader.Read() Then
'                            lblName.Text = reader("NAME_AR").ToString()
'                            lblSanwyE3t.Text = reader("SANWY_E3T").ToString()
'                            lblMshobE3t.Text = reader("MSHOB_E3T").ToString()
'                            lblRasedE3t.Text = reader("RASED_E3T").ToString()

'                            lblSanwyArda.Text = reader("SANWT_ARDA").ToString()
'                            lblMshobArda.Text = reader("MSHOB_ARDA").ToString()
'                            lblRasedArda.Text = reader("RASED_ARDA").ToString()

'                            lblSanwyMrda.Text = reader("SANWY_MRDA").ToString()
'                            lblMshobMrda.Text = reader("MSHOB_MRDA").ToString()
'                            lblRasedMrda.Text = reader("RASED_MRDA").ToString()

'                            lblSanwyMrda75.Text = reader("SANWY_MRDA75").ToString()
'                            lblMshobMrda75.Text = reader("MSHOB_MRDA75").ToString()
'                            lblRasedMrda75.Text = reader("RASED_MRDA75").ToString()

'                        Else
'                            MessageBox.Show("لا يوجد موظف بهذا الرقم أو أنه غير نشط.")
'                            ClearLabels()
'                        End If
'                    End Using
'                End Using
'            End Using
'        Catch ex As Exception
'            MessageBox.Show("حدث خطأ: " & ex.Message)
'        End Try
'    End Sub

'    Private Sub ClearLabels()
'        lblName.Text = ""
'        lblSanwyE3t.Text = ""
'        lblMshobE3t.Text = ""
'        lblSanwyArda.Text = ""
'        lblSanwyMrda.Text = ""
'        lblSanwyMrda75.Text = ""
'    End Sub

'    Private Sub btn_Clear_Click(sender As Object, e As EventArgs) Handles btn_Clear.Click
'        ClearLabels()
'    End Sub

'End Class



Imports System.Data.SqlClient
Imports El_Dawliya_International_System.El_Dawliya_International_System.Forms.Report

Public Class EmployeeBalanceForm
    Dim connectionString As String = "Server=192.168.1.10;Database=PY;User ID=admin;Password=************"

    Private Sub btnSearch_Click(sender As Object, e As EventArgs) Handles btnSearch.Click
        Dim empNo As String = txtEmpNo.Text.Trim()
        If empNo = "" Then
            MessageBox.Show("من فضلك أدخل رقم الموظف.")
            Return
        End If

        Dim query As String =
            "SELECT " &
            "E.EMP_NO, " &
            "E.EMP_NAME AS NAME_AR, " &
            "ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0) AS SANWY_E3T, " &
            "ISNULL(E.VAC_RG_P, 0) AS MORAHAL_E3T, " &
            "ISNULL(E.VAC_RG_T, 0) AS MSHOB_E3T, " &
            "ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0) - ISNULL(E.VAC_RG_T, 0) AS RASED_E3T, " &
            "ISNULL(E.VAC_EX_Y, 0) + ISNULL(E.VAC_EX_P, 0) AS SANWT_ARDA, " &
            "ISNULL(E.VAC_EX_P, 0) AS MORAHAL_ARDA, " &
            "ISNULL(E.VAC_EX_T, 0) AS MSHOB_ARDA, " &
            "ISNULL(E.VAC_EX_Y, 0) + ISNULL(E.VAC_EX_P, 0) - ISNULL(E.VAC_EX_T, 0) AS RASED_ARDA, " &
            "ISNULL(E.VAC_SK_Y, 0) + ISNULL(E.VAC_SK_P, 0) AS SANWY_MRDA, " &
            "ISNULL(E.VAC_SK_P, 0) AS MORAHAL_MRDA, " &
            "ISNULL(E.VAC_SK_T, 0) AS MSHOB_MRDA, " &
            "ISNULL(E.VAC_SK_Y, 0) + ISNULL(E.VAC_SK_P, 0) - ISNULL(E.VAC_SK_T, 0) AS RASED_MRDA, " &
            "ISNULL(E.TKT_TR_Y, 0) AS SANWY_MRDA75, " &
            "ISNULL(E.TKT_TR_P, 0) AS MORAHAL_MRDA75, " &
            "ISNULL(E.TKT_TR_T, 0) AS MSHOB_MRDA75, " &
            "ISNULL(E.TKT_TR_Y, 0) - ISNULL(E.TKT_TR_T, 0) AS RASED_MRDA75, " &
            "CASE " &
            "WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 15 THEN MONTH(GETDATE()) * 2 " &
            "WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 24 THEN MONTH(GETDATE()) * 2.5 " &
            "ELSE 0 " &
            "END AS ALLOWED_SO_FAR, " &
            "(CASE " &
            "WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 15 THEN MONTH(GETDATE()) * 2 " &
            "WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 24 THEN MONTH(GETDATE()) * 2.5 " &
            "ELSE 0 " &
            "END) - (ISNULL(E.VAC_RG_T, 0) + ISNULL(E.VAC_EX_T, 0)) AS FINAL_BALANCE " &
            "FROM PYEMPMFH E " &
            "LEFT JOIN Pylists PL1 ON PL1.REC_TYPE = 1 AND PL1.CODE = E.MNGMNT_D " & ' التعديل هنا
            "WHERE e.EMP_NO = @EmpNo AND E.SAL_STATUS = 1 " &
            "AND PL1.DSCR_AR <> N'إدارة المصنع' AND PL1.DSCR_AR <> N'الإدارة'" ' تفعيل الاستثناء

        Try
            Using conn As New SqlConnection(connectionString)
                Using cmd As New SqlCommand(query, conn)
                    cmd.Parameters.AddWithValue("@EmpNo", empNo)
                    conn.Open()
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            lblName.Text = reader("NAME_AR").ToString()
                            lblSanwyE3t.Text = CDbl(reader("SANWY_E3T")).ToString("F2")
                            lblMshobE3t.Text = CDbl(reader("MSHOB_E3T")).ToString("F2")
                            lblRasedE3t.Text = CDbl(reader("RASED_E3T")).ToString("F2")

                            lblSanwyArda.Text = CDbl(reader("SANWT_ARDA")).ToString("F2")
                            lblMshobArda.Text = CDbl(reader("MSHOB_ARDA")).ToString("F2")
                            lblRasedArda.Text = CDbl(reader("RASED_ARDA")).ToString("F2")

                            lblSanwyMrda.Text = CDbl(reader("SANWY_MRDA")).ToString("F2")
                            lblMshobMrda.Text = CDbl(reader("MSHOB_MRDA")).ToString("F2")
                            lblRasedMrda.Text = CDbl(reader("RASED_MRDA")).ToString("F2")

                            lblSanwyMrda75.Text = CDbl(reader("SANWY_MRDA75")).ToString("F2")
                            lblMshobMrda75.Text = CDbl(reader("MSHOB_MRDA75")).ToString("F2")
                            lblRasedMrda75.Text = CDbl(reader("RASED_MRDA75")).ToString("F2")

                            ' إضافة الحقلين الجديدين لعرضهما في الفورم
                            lblAllowedSoFar.Text = CDbl(reader("ALLOWED_SO_FAR")).ToString("F2")
                            lblFinalBalance.Text = CDbl(reader("FINAL_BALANCE")).ToString("F2")

                        Else
                            MessageBox.Show("لا يوجد موظف بهذا الرقم أو أنه غير نشط.")
                            ClearLabels()
                        End If
                    End Using
                End Using
            End Using
        Catch ex As Exception
            MessageBox.Show("حدث خطأ: " & ex.Message)
        End Try
    End Sub

    Private Sub ClearLabels()
        lblName.Text = ""
        lblSanwyE3t.Text = ""
        lblMshobE3t.Text = ""
        lblSanwyArda.Text = ""
        lblSanwyMrda.Text = ""
        lblSanwyMrda75.Text = ""
        ' إضافة الحقلين الجديدين للمسح
        lblAllowedSoFar.Text = ""
        lblFinalBalance.Text = ""
        txtEmpNo.Text = ""
        txtEmpNo.Focus()
    End Sub

    Private Sub btn_Clear_Click(sender As Object, e As EventArgs) Handles btn_Clear.Click
        ClearLabels()
    End Sub

    Private Sub txtEmpNo_KeyDown(sender As Object, e As KeyEventArgs) Handles txtEmpNo.KeyDown
        If e.KeyCode = Keys.Enter Then
            ' تنفيذ الكود لاسترداد بيانات الموظف
            btnSearch.PerformClick()
        End If
    End Sub

    Private Sub EmployeeBalanceForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        ClearLabels()
    End Sub

    Private Sub btn_Report_Click(sender As Object, e As EventArgs) Handles btn_Report.Click
        Dim dt_EmployeeBalance As New DataTable()
        Dim empNo As String = txtEmpNo.Text.Trim()

        If empNo = "" Then
            MessageBox.Show("من فضلك أدخل رقم الموظف.")
            Return
        End If

        Try
            Using conn As New SqlConnection(connectionString)
                dt_EmployeeBalance.Clear()

                Dim query As String =
                    "SELECT E.EMP_NO, E.EMP_NAME AS NAME_AR, " &
                    "ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0) AS SANWY_E3T, " &
                    "ISNULL(E.VAC_RG_P, 0) AS MORAHAL_E3T, " &
                    "ISNULL(E.VAC_RG_T, 0) AS MSHOB_E3T, " &
                    "ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0) - ISNULL(E.VAC_RG_T, 0) AS RASED_E3T, " &
                    "ISNULL(E.VAC_EX_Y, 0) + ISNULL(E.VAC_EX_P, 0) AS SANWT_ARDA, " &
                    "ISNULL(E.VAC_EX_P, 0) AS MORAHAL_ARDA, " &
                    "ISNULL(E.VAC_EX_T, 0) AS MSHOB_ARDA, " &
                    "ISNULL(E.VAC_EX_Y, 0) + ISNULL(E.VAC_EX_P, 0) - ISNULL(E.VAC_EX_T, 0) AS RASED_ARDA, " &
                    "ISNULL(E.VAC_SK_Y, 0) + ISNULL(E.VAC_SK_P, 0) AS SANWY_MRDA, " &
                    "ISNULL(E.VAC_SK_P, 0) AS MORAHAL_MRDA, " &
                    "ISNULL(E.VAC_SK_T, 0) AS MSHOB_MRDA, " &
                    "ISNULL(E.VAC_SK_Y, 0) + ISNULL(E.VAC_SK_P, 0) - ISNULL(E.VAC_SK_T, 0) AS RASED_MRDA, " &
                    "ISNULL(E.TKT_TR_Y, 0) AS SANWY_MRDA75, " &
                    "ISNULL(E.TKT_TR_P, 0) AS MORAHAL_MRDA75, " &
                    "ISNULL(E.TKT_TR_T, 0) AS MSHOB_MRDA75, " &
                    "ISNULL(E.TKT_TR_Y, 0) - ISNULL(E.TKT_TR_T, 0) AS RASED_MRDA75, " &
                    "CASE WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 15 THEN MONTH(GETDATE()) * 2 " &
                    "WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 24 THEN MONTH(GETDATE()) * 2.5 ELSE 0 END AS ALLOWED_SO_FAR, " &
                    "(CASE WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 15 THEN MONTH(GETDATE()) * 2 " &
                    "WHEN (ISNULL(E.VAC_RG_Y, 0) + ISNULL(E.VAC_RG_P, 0)) = 24 THEN MONTH(GETDATE()) * 2.5 ELSE 0 END) - " &
                    "(ISNULL(E.VAC_RG_T, 0) + ISNULL(E.VAC_EX_T, 0)) AS FINAL_BALANCE " &
                    "FROM PYEMPMFH E " &
                    "LEFT JOIN Pylists PL1 ON PL1.REC_TYPE = 1 AND PL1.CODE = E.MNGMNT_D " &
                    "WHERE E.EMP_NO = @EmpNo AND E.SAL_STATUS = 1 " &
                    "AND PL1.DSCR_AR NOT IN (N'إدارة المصنع', N'الإدارة')"

                Dim da As New SqlDataAdapter(query, conn)
                da.SelectCommand.Parameters.AddWithValue("@EmpNo", empNo)
                da.Fill(dt_EmployeeBalance)

                If dt_EmployeeBalance.Rows.Count = 0 Then
                    MessageBox.Show("لم يتم العثور على موظف بهذا الرقم.")
                    Return
                End If

                Dim rep As New El_Dawliya_International_System.Forms.Report.EmployeeBalance()
                rep.SetDataSource(dt_EmployeeBalance.DefaultView)
                Frm_Report.CrystalReportViewer1.ReportSource = rep
                Frm_Report.CrystalReportViewer1.Refresh()
                Frm_Report.ShowDialog()
            End Using
        Catch ex As Exception
            MessageBox.Show("حدث خطأ أثناء توليد التقرير: " & ex.Message)
        End Try
    End Sub


End Class