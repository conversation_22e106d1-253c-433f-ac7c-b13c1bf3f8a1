Imports System.Data.SqlClient
Imports System.Threading.Tasks
Imports System.Reflection

''' <summary>
''' Base repository implementation providing common CRUD operations
''' Implements the Repository pattern with async support and proper error handling
''' </summary>
''' <typeparam name="T">Entity type</typeparam>
Public MustInherit Class BaseRepository(Of T As Class)
    Implements IRepository(Of T)

    Protected ReadOnly _dbManager As DatabaseManager
    Protected ReadOnly _logger As ILogger
    Protected ReadOnly _tableName As String
    Protected ReadOnly _primaryKeyColumn As String

    ''' <summary>
    ''' Constructor
    ''' </summary>
    ''' <param name="tableName">Database table name</param>
    ''' <param name="primaryKeyColumn">Primary key column name</param>
    Protected Sub New(tableName As String, primaryKeyColumn As String)
        _dbManager = DatabaseManager.Instance
        _logger = LoggerFactory.GetLogger(GetType(T))
        _tableName = tableName
        _primaryKeyColumn = primaryKeyColumn
    End Sub

    ''' <summary>
    ''' Map SqlDataReader to entity - must be implemented by derived classes
    ''' </summary>
    ''' <param name="reader">SqlDataReader</param>
    ''' <returns>Mapped entity</returns>
    Protected MustOverride Function MapFromReader(reader As SqlDataReader) As T

    ''' <summary>
    ''' Get entity by ID
    ''' </summary>
    ''' <param name="id">Entity ID</param>
    ''' <returns>Entity or Nothing if not found</returns>
    Public Overridable Function GetById(id As Object) As T Implements IRepository(Of T).GetById
        Try
            Dim query As String = $"SELECT * FROM {_tableName} WHERE {_primaryKeyColumn} = @Id"
            
            Return _dbManager.ExecuteQuery(Of T)(query,
                Sub(cmd) cmd.Parameters.AddWithValue("@Id", id),
                Function(reader)
                    If reader.Read() Then
                        Return MapFromReader(reader)
                    End If
                    Return Nothing
                End Function)
        Catch ex As Exception
            _logger.LogError($"Error getting {GetType(T).Name} by ID: {id}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Get entity by ID asynchronously
    ''' </summary>
    ''' <param name="id">Entity ID</param>
    ''' <returns>Task containing entity or Nothing if not found</returns>
    Public Overridable Async Function GetByIdAsync(id As Object) As Task(Of T) Implements IRepository(Of T).GetByIdAsync
        Try
            Dim query As String = $"SELECT * FROM {_tableName} WHERE {_primaryKeyColumn} = @Id"
            
            Return Await _dbManager.ExecuteQueryAsync(Of T)(query,
                Sub(cmd) cmd.Parameters.AddWithValue("@Id", id),
                Async Function(reader)
                    If Await reader.ReadAsync() Then
                        Return MapFromReader(reader)
                    End If
                    Return Nothing
                End Function)
        Catch ex As Exception
            _logger.LogError($"Error getting {GetType(T).Name} by ID async: {id}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Get all entities
    ''' </summary>
    ''' <returns>Collection of all entities</returns>
    Public Overridable Function GetAll() As IEnumerable(Of T) Implements IRepository(Of T).GetAll
        Try
            Dim query As String = $"SELECT * FROM {_tableName}"
            
            Return _dbManager.ExecuteQuery(Of IEnumerable(Of T))(query,
                Nothing,
                Function(reader)
                    Dim results As New List(Of T)()
                    While reader.Read()
                        results.Add(MapFromReader(reader))
                    End While
                    Return results
                End Function)
        Catch ex As Exception
            _logger.LogError($"Error getting all {GetType(T).Name}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Get all entities asynchronously
    ''' </summary>
    ''' <returns>Task containing collection of all entities</returns>
    Public Overridable Async Function GetAllAsync() As Task(Of IEnumerable(Of T)) Implements IRepository(Of T).GetAllAsync
        Try
            Dim query As String = $"SELECT * FROM {_tableName}"
            
            Return Await _dbManager.ExecuteQueryAsync(Of IEnumerable(Of T))(query,
                Nothing,
                Async Function(reader)
                    Dim results As New List(Of T)()
                    While Await reader.ReadAsync()
                        results.Add(MapFromReader(reader))
                    End While
                    Return results
                End Function)
        Catch ex As Exception
            _logger.LogError($"Error getting all {GetType(T).Name} async", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Find entities matching predicate
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Collection of matching entities</returns>
    Public Overridable Function Find(predicate As Func(Of T, Boolean)) As IEnumerable(Of T) Implements IRepository(Of T).Find
        Return GetAll().Where(predicate)
    End Function

    ''' <summary>
    ''' Find entities matching predicate asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing collection of matching entities</returns>
    Public Overridable Async Function FindAsync(predicate As Func(Of T, Boolean)) As Task(Of IEnumerable(Of T)) Implements IRepository(Of T).FindAsync
        Dim allEntities = Await GetAllAsync()
        Return allEntities.Where(predicate)
    End Function

    ''' <summary>
    ''' Get first entity matching predicate
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>First matching entity or Nothing</returns>
    Public Overridable Function FirstOrDefault(predicate As Func(Of T, Boolean)) As T Implements IRepository(Of T).FirstOrDefault
        Return Find(predicate).FirstOrDefault()
    End Function

    ''' <summary>
    ''' Get first entity matching predicate asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing first matching entity or Nothing</returns>
    Public Overridable Async Function FirstOrDefaultAsync(predicate As Func(Of T, Boolean)) As Task(Of T) Implements IRepository(Of T).FirstOrDefaultAsync
        Dim results = Await FindAsync(predicate)
        Return results.FirstOrDefault()
    End Function

    ''' <summary>
    ''' Add new entity - must be implemented by derived classes
    ''' </summary>
    ''' <param name="entity">Entity to add</param>
    ''' <returns>Added entity with generated ID</returns>
    Public MustOverride Function Add(entity As T) As T Implements IRepository(Of T).Add

    ''' <summary>
    ''' Add new entity asynchronously - must be implemented by derived classes
    ''' </summary>
    ''' <param name="entity">Entity to add</param>
    ''' <returns>Task containing added entity with generated ID</returns>
    Public MustOverride Function AddAsync(entity As T) As Task(Of T) Implements IRepository(Of T).AddAsync

    ''' <summary>
    ''' Add multiple entities
    ''' </summary>
    ''' <param name="entities">Entities to add</param>
    Public Overridable Sub AddRange(entities As IEnumerable(Of T)) Implements IRepository(Of T).AddRange
        For Each entity In entities
            Add(entity)
        Next
    End Sub

    ''' <summary>
    ''' Add multiple entities asynchronously
    ''' </summary>
    ''' <param name="entities">Entities to add</param>
    ''' <returns>Task representing the operation</returns>
    Public Overridable Async Function AddRangeAsync(entities As IEnumerable(Of T)) As Task Implements IRepository(Of T).AddRangeAsync
        For Each entity In entities
            Await AddAsync(entity)
        Next
    End Function

    ''' <summary>
    ''' Update existing entity - must be implemented by derived classes
    ''' </summary>
    ''' <param name="entity">Entity to update</param>
    Public MustOverride Sub Update(entity As T) Implements IRepository(Of T).Update

    ''' <summary>
    ''' Update existing entity asynchronously - must be implemented by derived classes
    ''' </summary>
    ''' <param name="entity">Entity to update</param>
    ''' <returns>Task representing the operation</returns>
    Public MustOverride Function UpdateAsync(entity As T) As Task Implements IRepository(Of T).UpdateAsync

    ''' <summary>
    ''' Remove entity
    ''' </summary>
    ''' <param name="entity">Entity to remove</param>
    Public Overridable Sub Remove(entity As T) Implements IRepository(Of T).Remove
        Dim idProperty = GetType(T).GetProperty(_primaryKeyColumn)
        If idProperty IsNot Nothing Then
            Dim id = idProperty.GetValue(entity)
            Remove(id)
        Else
            Throw New InvalidOperationException($"Primary key property {_primaryKeyColumn} not found on type {GetType(T).Name}")
        End If
    End Sub

    ''' <summary>
    ''' Remove entity by ID
    ''' </summary>
    ''' <param name="id">ID of entity to remove</param>
    Public Overridable Sub Remove(id As Object) Implements IRepository(Of T).Remove
        Try
            Dim query As String = $"DELETE FROM {_tableName} WHERE {_primaryKeyColumn} = @Id"
            
            _dbManager.ExecuteNonQuery(query,
                Sub(cmd) cmd.Parameters.AddWithValue("@Id", id))
                
        Catch ex As Exception
            _logger.LogError($"Error removing {GetType(T).Name} with ID: {id}", ex)
            Throw
        End Try
    End Sub

    ''' <summary>
    ''' Remove entity asynchronously
    ''' </summary>
    ''' <param name="entity">Entity to remove</param>
    ''' <returns>Task representing the operation</returns>
    Public Overridable Async Function RemoveAsync(entity As T) As Task Implements IRepository(Of T).RemoveAsync
        Dim idProperty = GetType(T).GetProperty(_primaryKeyColumn)
        If idProperty IsNot Nothing Then
            Dim id = idProperty.GetValue(entity)
            Await RemoveAsync(id)
        Else
            Throw New InvalidOperationException($"Primary key property {_primaryKeyColumn} not found on type {GetType(T).Name}")
        End If
    End Function

    ''' <summary>
    ''' Remove entity by ID asynchronously
    ''' </summary>
    ''' <param name="id">ID of entity to remove</param>
    ''' <returns>Task representing the operation</returns>
    Public Overridable Async Function RemoveAsync(id As Object) As Task Implements IRepository(Of T).RemoveAsync
        Try
            Dim query As String = $"DELETE FROM {_tableName} WHERE {_primaryKeyColumn} = @Id"
            
            Await _dbManager.ExecuteNonQueryAsync(query,
                Sub(cmd) cmd.Parameters.AddWithValue("@Id", id))
                
        Catch ex As Exception
            _logger.LogError($"Error removing {GetType(T).Name} with ID async: {id}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Remove multiple entities
    ''' </summary>
    ''' <param name="entities">Entities to remove</param>
    Public Overridable Sub RemoveRange(entities As IEnumerable(Of T)) Implements IRepository(Of T).RemoveRange
        For Each entity In entities
            Remove(entity)
        Next
    End Sub

    ''' <summary>
    ''' Remove multiple entities asynchronously
    ''' </summary>
    ''' <param name="entities">Entities to remove</param>
    ''' <returns>Task representing the operation</returns>
    Public Overridable Async Function RemoveRangeAsync(entities As IEnumerable(Of T)) As Task Implements IRepository(Of T).RemoveRangeAsync
        For Each entity In entities
            Await RemoveAsync(entity)
        Next
    End Function

    ''' <summary>
    ''' Count total entities
    ''' </summary>
    ''' <returns>Total count</returns>
    Public Overridable Function Count() As Integer Implements IRepository(Of T).Count
        Try
            Dim query As String = $"SELECT COUNT(*) FROM {_tableName}"
            Return _dbManager.ExecuteScalar(Of Integer)(query, Nothing)
        Catch ex As Exception
            _logger.LogError($"Error counting {GetType(T).Name}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Count entities matching predicate
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Count of matching entities</returns>
    Public Overridable Function Count(predicate As Func(Of T, Boolean)) As Integer Implements IRepository(Of T).Count
        Return Find(predicate).Count()
    End Function

    ''' <summary>
    ''' Count total entities asynchronously
    ''' </summary>
    ''' <returns>Task containing total count</returns>
    Public Overridable Async Function CountAsync() As Task(Of Integer) Implements IRepository(Of T).CountAsync
        Try
            Dim query As String = $"SELECT COUNT(*) FROM {_tableName}"
            Return Await _dbManager.ExecuteScalarAsync(Of Integer)(query, Nothing)
        Catch ex As Exception
            _logger.LogError($"Error counting {GetType(T).Name} async", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Count entities matching predicate asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing count of matching entities</returns>
    Public Overridable Async Function CountAsync(predicate As Func(Of T, Boolean)) As Task(Of Integer) Implements IRepository(Of T).CountAsync
        Dim results = Await FindAsync(predicate)
        Return results.Count()
    End Function

    ''' <summary>
    ''' Check if any entities exist
    ''' </summary>
    ''' <returns>True if any entities exist</returns>
    Public Overridable Function Any() As Boolean Implements IRepository(Of T).Any
        Return Count() > 0
    End Function

    ''' <summary>
    ''' Check if any entities matching predicate exist
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>True if any matching entities exist</returns>
    Public Overridable Function Any(predicate As Func(Of T, Boolean)) As Boolean Implements IRepository(Of T).Any
        Return Find(predicate).Any()
    End Function

    ''' <summary>
    ''' Check if any entities exist asynchronously
    ''' </summary>
    ''' <returns>Task containing true if any entities exist</returns>
    Public Overridable Async Function AnyAsync() As Task(Of Boolean) Implements IRepository(Of T).AnyAsync
        Dim count = Await CountAsync()
        Return count > 0
    End Function

    ''' <summary>
    ''' Check if any entities matching predicate exist asynchronously
    ''' </summary>
    ''' <param name="predicate">Search predicate</param>
    ''' <returns>Task containing true if any matching entities exist</returns>
    Public Overridable Async Function AnyAsync(predicate As Func(Of T, Boolean)) As Task(Of Boolean) Implements IRepository(Of T).AnyAsync
        Dim results = Await FindAsync(predicate)
        Return results.Any()
    End Function

    ''' <summary>
    ''' Get paged results
    ''' </summary>
    ''' <param name="pageNumber">Page number (1-based)</param>
    ''' <param name="pageSize">Number of items per page</param>
    ''' <returns>Paged result</returns>
    Public Overridable Function GetPaged(pageNumber As Integer, pageSize As Integer) As PagedResult(Of T) Implements IRepository(Of T).GetPaged
        Try
            Dim offset As Integer = (pageNumber - 1) * pageSize
            Dim query As String = $"SELECT * FROM {_tableName} ORDER BY {_primaryKeyColumn} OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY"
            Dim countQuery As String = $"SELECT COUNT(*) FROM {_tableName}"

            Dim totalCount = _dbManager.ExecuteScalar(Of Integer)(countQuery, Nothing)

            Dim items = _dbManager.ExecuteQuery(Of IEnumerable(Of T))(query,
                Sub(cmd)
                    cmd.Parameters.AddWithValue("@Offset", offset)
                    cmd.Parameters.AddWithValue("@PageSize", pageSize)
                End Sub,
                Function(reader)
                    Dim results As New List(Of T)()
                    While reader.Read()
                        results.Add(MapFromReader(reader))
                    End While
                    Return results
                End Function)

            Return New PagedResult(Of T)(items, totalCount, pageNumber, pageSize)

        Catch ex As Exception
            _logger.LogError($"Error getting paged {GetType(T).Name}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Get paged results asynchronously
    ''' </summary>
    ''' <param name="pageNumber">Page number (1-based)</param>
    ''' <param name="pageSize">Number of items per page</param>
    ''' <returns>Task containing paged result</returns>
    Public Overridable Async Function GetPagedAsync(pageNumber As Integer, pageSize As Integer) As Task(Of PagedResult(Of T)) Implements IRepository(Of T).GetPagedAsync
        Try
            Dim offset As Integer = (pageNumber - 1) * pageSize
            Dim query As String = $"SELECT * FROM {_tableName} ORDER BY {_primaryKeyColumn} OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY"
            Dim countQuery As String = $"SELECT COUNT(*) FROM {_tableName}"

            Dim totalCount = Await _dbManager.ExecuteScalarAsync(Of Integer)(countQuery, Nothing)

            Dim items = Await _dbManager.ExecuteQueryAsync(Of IEnumerable(Of T))(query,
                Sub(cmd)
                    cmd.Parameters.AddWithValue("@Offset", offset)
                    cmd.Parameters.AddWithValue("@PageSize", pageSize)
                End Sub,
                Async Function(reader)
                    Dim results As New List(Of T)()
                    While Await reader.ReadAsync()
                        results.Add(MapFromReader(reader))
                    End While
                    Return results
                End Function)

            Return New PagedResult(Of T)(items, totalCount, pageNumber, pageSize)

        Catch ex As Exception
            _logger.LogError($"Error getting paged {GetType(T).Name} async", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Execute raw SQL query
    ''' </summary>
    ''' <param name="sql">SQL query</param>
    ''' <param name="parameters">Query parameters</param>
    ''' <returns>Collection of entities</returns>
    Public Overridable Function ExecuteQuery(sql As String, ParamArray parameters() As Object) As IEnumerable(Of T) Implements IRepository(Of T).ExecuteQuery
        Try
            Return _dbManager.ExecuteQuery(Of IEnumerable(Of T))(sql,
                Sub(cmd)
                    For i As Integer = 0 To parameters.Length - 1
                        cmd.Parameters.AddWithValue($"@p{i}", parameters(i))
                    Next
                End Sub,
                Function(reader)
                    Dim results As New List(Of T)()
                    While reader.Read()
                        results.Add(MapFromReader(reader))
                    End While
                    Return results
                End Function)
        Catch ex As Exception
            _logger.LogError($"Error executing query for {GetType(T).Name}: {sql}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Execute raw SQL query asynchronously
    ''' </summary>
    ''' <param name="sql">SQL query</param>
    ''' <param name="parameters">Query parameters</param>
    ''' <returns>Task containing collection of entities</returns>
    Public Overridable Async Function ExecuteQueryAsync(sql As String, ParamArray parameters() As Object) As Task(Of IEnumerable(Of T)) Implements IRepository(Of T).ExecuteQueryAsync
        Try
            Return Await _dbManager.ExecuteQueryAsync(Of IEnumerable(Of T))(sql,
                Sub(cmd)
                    For i As Integer = 0 To parameters.Length - 1
                        cmd.Parameters.AddWithValue($"@p{i}", parameters(i))
                    Next
                End Sub,
                Async Function(reader)
                    Dim results As New List(Of T)()
                    While Await reader.ReadAsync()
                        results.Add(MapFromReader(reader))
                    End While
                    Return results
                End Function)
        Catch ex As Exception
            _logger.LogError($"Error executing async query for {GetType(T).Name}: {sql}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Execute raw SQL command
    ''' </summary>
    ''' <param name="sql">SQL command</param>
    ''' <param name="parameters">Command parameters</param>
    ''' <returns>Number of affected rows</returns>
    Public Overridable Function ExecuteCommand(sql As String, ParamArray parameters() As Object) As Integer Implements IRepository(Of T).ExecuteCommand
        Try
            Return _dbManager.ExecuteNonQuery(sql,
                Sub(cmd)
                    For i As Integer = 0 To parameters.Length - 1
                        cmd.Parameters.AddWithValue($"@p{i}", parameters(i))
                    Next
                End Sub)
        Catch ex As Exception
            _logger.LogError($"Error executing command for {GetType(T).Name}: {sql}", ex)
            Throw
        End Try
    End Function

    ''' <summary>
    ''' Execute raw SQL command asynchronously
    ''' </summary>
    ''' <param name="sql">SQL command</param>
    ''' <param name="parameters">Command parameters</param>
    ''' <returns>Task containing number of affected rows</returns>
    Public Overridable Async Function ExecuteCommandAsync(sql As String, ParamArray parameters() As Object) As Task(Of Integer) Implements IRepository(Of T).ExecuteCommandAsync
        Try
            Return Await _dbManager.ExecuteNonQueryAsync(sql,
                Sub(cmd)
                    For i As Integer = 0 To parameters.Length - 1
                        cmd.Parameters.AddWithValue($"@p{i}", parameters(i))
                    Next
                End Sub)
        Catch ex As Exception
            _logger.LogError($"Error executing async command for {GetType(T).Name}: {sql}", ex)
            Throw
        End Try
    End Function
End Class
