﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="BtnDelete.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="BtnDelete.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="BtnDelete.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt, style=Bold</value>
  </data>
  <data name="BtnDelete.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="BtnDelete.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 17</value>
  </data>
  <data name="BtnDelete.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 7, 5, 7</value>
  </data>
  <data name="BtnDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 53</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="BtnDelete.TabIndex" type="System.Int32, mscorlib">
    <value>108</value>
  </data>
  <data name="BtnDelete.Text" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="BtnDelete.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;BtnDelete.Name" xml:space="preserve">
    <value>BtnDelete</value>
  </data>
  <data name="&gt;&gt;BtnDelete.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;BtnDelete.Parent" xml:space="preserve">
    <value>Panel3</value>
  </data>
  <data name="&gt;&gt;BtnDelete.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="BtnSave.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="BtnSave.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="BtnSave.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt, style=Bold</value>
  </data>
  <data name="BtnSave.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="BtnSave.Location" type="System.Drawing.Point, System.Drawing">
    <value>453, 17</value>
  </data>
  <data name="BtnSave.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 7, 5, 7</value>
  </data>
  <data name="BtnSave.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="BtnSave.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 53</value>
  </data>
  <data name="BtnSave.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="BtnSave.Text" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="BtnSave.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;BtnSave.Name" xml:space="preserve">
    <value>BtnSave</value>
  </data>
  <data name="&gt;&gt;BtnSave.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;BtnSave.Parent" xml:space="preserve">
    <value>Panel3</value>
  </data>
  <data name="&gt;&gt;BtnSave.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="BtnNewAdd.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="BtnNewAdd.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="BtnNewAdd.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt, style=Bold</value>
  </data>
  <data name="BtnNewAdd.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="BtnNewAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>643, 17</value>
  </data>
  <data name="BtnNewAdd.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 7, 5, 7</value>
  </data>
  <data name="BtnNewAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 53</value>
  </data>
  <data name="BtnNewAdd.TabIndex" type="System.Int32, mscorlib">
    <value>105</value>
  </data>
  <data name="BtnNewAdd.Text" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="BtnNewAdd.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;BtnNewAdd.Name" xml:space="preserve">
    <value>BtnNewAdd</value>
  </data>
  <data name="&gt;&gt;BtnNewAdd.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;BtnNewAdd.Parent" xml:space="preserve">
    <value>Panel3</value>
  </data>
  <data name="&gt;&gt;BtnNewAdd.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="BtnEdit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="BtnEdit.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="BtnEdit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt, style=Bold</value>
  </data>
  <data name="BtnEdit.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="BtnEdit.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 17</value>
  </data>
  <data name="BtnEdit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 7, 5, 7</value>
  </data>
  <data name="BtnEdit.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 53</value>
  </data>
  <data name="BtnEdit.TabIndex" type="System.Int32, mscorlib">
    <value>107</value>
  </data>
  <data name="BtnEdit.Text" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="BtnEdit.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;BtnEdit.Name" xml:space="preserve">
    <value>BtnEdit</value>
  </data>
  <data name="&gt;&gt;BtnEdit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;BtnEdit.Parent" xml:space="preserve">
    <value>Panel3</value>
  </data>
  <data name="&gt;&gt;BtnEdit.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="Panel3.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="Panel3.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="Panel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>776, 77</value>
  </data>
  <data name="Panel3.TabIndex" type="System.Int32, mscorlib">
    <value>109</value>
  </data>
  <data name="&gt;&gt;Panel3.Name" xml:space="preserve">
    <value>Panel3</value>
  </data>
  <data name="&gt;&gt;Panel3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Panel3.Parent" xml:space="preserve">
    <value>Panel1</value>
  </data>
  <data name="&gt;&gt;Panel3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="Panel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="Panel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 643</value>
  </data>
  <data name="Panel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>776, 77</value>
  </data>
  <data name="Panel1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;Panel1.Name" xml:space="preserve">
    <value>Panel1</value>
  </data>
  <data name="&gt;&gt;Panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Panel1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;Panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="PictureBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 9</value>
  </data>
  <data name="PictureBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 45</value>
  </data>
  <data name="PictureBox1.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>StretchImage</value>
  </data>
  <data name="PictureBox1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="PictureBox1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;PictureBox1.Name" xml:space="preserve">
    <value>PictureBox1</value>
  </data>
  <data name="&gt;&gt;PictureBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;PictureBox1.Parent" xml:space="preserve">
    <value>Panel2</value>
  </data>
  <data name="&gt;&gt;PictureBox1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="PictureBox2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="PictureBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>726, 4</value>
  </data>
  <data name="PictureBox2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="PictureBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 45</value>
  </data>
  <data name="PictureBox2.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>StretchImage</value>
  </data>
  <data name="PictureBox2.TabIndex" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="&gt;&gt;PictureBox2.Name" xml:space="preserve">
    <value>PictureBox2</value>
  </data>
  <data name="&gt;&gt;PictureBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;PictureBox2.Parent" xml:space="preserve">
    <value>Panel2</value>
  </data>
  <data name="&gt;&gt;PictureBox2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="Label1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="Label1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Cairo, 13.7999992pt, style=Bold</value>
  </data>
  <data name="Label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="Label1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>5, 0, 5, 0</value>
  </data>
  <data name="Label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>776, 57</value>
  </data>
  <data name="Label1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="Label1.Text" xml:space="preserve">
    <value>اضافة وحدة جديدة</value>
  </data>
  <data name="Label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;Label1.Name" xml:space="preserve">
    <value>Label1</value>
  </data>
  <data name="&gt;&gt;Label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Label1.Parent" xml:space="preserve">
    <value>Panel2</value>
  </data>
  <data name="&gt;&gt;Label1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="Panel2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="Panel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="Panel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>776, 57</value>
  </data>
  <data name="Panel2.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;Panel2.Name" xml:space="preserve">
    <value>Panel2</value>
  </data>
  <data name="&gt;&gt;Panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Panel2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;Panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="Txt_Unit_Machine.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="Txt_Unit_Machine.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt</value>
  </data>
  <data name="Txt_Unit_Machine.Items" xml:space="preserve">
    <value>D1</value>
  </data>
  <data name="Txt_Unit_Machine.Items1" xml:space="preserve">
    <value>D2</value>
  </data>
  <data name="Txt_Unit_Machine.Items2" xml:space="preserve">
    <value>F1</value>
  </data>
  <data name="Txt_Unit_Machine.Items3" xml:space="preserve">
    <value>F2</value>
  </data>
  <data name="Txt_Unit_Machine.Items4" xml:space="preserve">
    <value>H1</value>
  </data>
  <data name="Txt_Unit_Machine.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 125</value>
  </data>
  <data name="Txt_Unit_Machine.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 6, 4, 6</value>
  </data>
  <data name="Txt_Unit_Machine.Size" type="System.Drawing.Size, System.Drawing">
    <value>513, 35</value>
  </data>
  <data name="Txt_Unit_Machine.TabIndex" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Machine.Name" xml:space="preserve">
    <value>Txt_Unit_Machine</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Machine.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Machine.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Machine.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="Txt_Unit_ID.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="Txt_Unit_ID.Font" type="System.Drawing.Font, System.Drawing">
    <value>Cairo SemiBold, 12pt, style=Bold</value>
  </data>
  <data name="Txt_Unit_ID.Location" type="System.Drawing.Point, System.Drawing">
    <value>60, 12</value>
  </data>
  <data name="Txt_Unit_ID.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 6, 4, 6</value>
  </data>
  <data name="Txt_Unit_ID.Size" type="System.Drawing.Size, System.Drawing">
    <value>488, 45</value>
  </data>
  <data name="Txt_Unit_ID.TabIndex" type="System.Int32, mscorlib">
    <value>96</value>
  </data>
  <data name="Txt_Unit_ID.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_ID.Name" xml:space="preserve">
    <value>Txt_Unit_ID</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_ID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_ID.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_ID.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="Txt_Unit_Name.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="Txt_Unit_Name.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt</value>
  </data>
  <data name="Txt_Unit_Name.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 71</value>
  </data>
  <data name="Txt_Unit_Name.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 6, 4, 6</value>
  </data>
  <data name="Txt_Unit_Name.Size" type="System.Drawing.Size, System.Drawing">
    <value>513, 34</value>
  </data>
  <data name="Txt_Unit_Name.TabIndex" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="Txt_Unit_Name.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Name.Name" xml:space="preserve">
    <value>Txt_Unit_Name</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Name.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Name.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Txt_Unit_Name.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="Problem_Machinelbl.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="Problem_Machinelbl.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt</value>
  </data>
  <data name="Problem_Machinelbl.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="Problem_Machinelbl.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 123</value>
  </data>
  <data name="Problem_Machinelbl.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="Problem_Machinelbl.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 37</value>
  </data>
  <data name="Problem_Machinelbl.TabIndex" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="Problem_Machinelbl.Text" xml:space="preserve">
    <value>ماكينة الوحدة</value>
  </data>
  <data name="Problem_Machinelbl.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;Problem_Machinelbl.Name" xml:space="preserve">
    <value>Problem_Machinelbl</value>
  </data>
  <data name="&gt;&gt;Problem_Machinelbl.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Problem_Machinelbl.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Problem_Machinelbl.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="Label2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="Label2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt</value>
  </data>
  <data name="Label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="Label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 71</value>
  </data>
  <data name="Label2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="Label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 34</value>
  </data>
  <data name="Label2.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="Label2.Text" xml:space="preserve">
    <value>اسم الوحدة</value>
  </data>
  <data name="Label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;Label2.Name" xml:space="preserve">
    <value>Label2</value>
  </data>
  <data name="&gt;&gt;Label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Label2.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Label2.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="dgv_Unit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="dgv_Unit.ColumnHeadersHeight" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="dgv_Unit.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 234</value>
  </data>
  <data name="dgv_Unit.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 6, 4, 6</value>
  </data>
  <data name="dgv_Unit.RowHeadersWidth" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="dgv_Unit.Size" type="System.Drawing.Size, System.Drawing">
    <value>747, 400</value>
  </data>
  <data name="dgv_Unit.TabIndex" type="System.Int32, mscorlib">
    <value>99</value>
  </data>
  <data name="&gt;&gt;dgv_Unit.Name" xml:space="preserve">
    <value>dgv_Unit</value>
  </data>
  <data name="&gt;&gt;dgv_Unit.Type" xml:space="preserve">
    <value>System.Windows.Forms.DataGridView, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;dgv_Unit.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;dgv_Unit.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="Txt_Search.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="Txt_Search.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt</value>
  </data>
  <data name="Txt_Search.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 174</value>
  </data>
  <data name="Txt_Search.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 6, 4, 6</value>
  </data>
  <data name="Txt_Search.Size" type="System.Drawing.Size, System.Drawing">
    <value>513, 34</value>
  </data>
  <data name="Txt_Search.TabIndex" type="System.Int32, mscorlib">
    <value>95</value>
  </data>
  <data name="Txt_Search.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;Txt_Search.Name" xml:space="preserve">
    <value>Txt_Search</value>
  </data>
  <data name="&gt;&gt;Txt_Search.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Txt_Search.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Txt_Search.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="Problem_Namelbl.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="Problem_Namelbl.Font" type="System.Drawing.Font, System.Drawing">
    <value>Cairo SemiBold, 12pt, style=Bold</value>
  </data>
  <data name="Problem_Namelbl.Location" type="System.Drawing.Point, System.Drawing">
    <value>556, 12</value>
  </data>
  <data name="Problem_Namelbl.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="Problem_Namelbl.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 32</value>
  </data>
  <data name="Problem_Namelbl.TabIndex" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="Problem_Namelbl.Text" xml:space="preserve">
    <value>كود الوحدة</value>
  </data>
  <data name="Problem_Namelbl.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;Problem_Namelbl.Name" xml:space="preserve">
    <value>Problem_Namelbl</value>
  </data>
  <data name="&gt;&gt;Problem_Namelbl.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Problem_Namelbl.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Problem_Namelbl.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="Label3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="Label3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 10.2pt</value>
  </data>
  <data name="Label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="Label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 174</value>
  </data>
  <data name="Label3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="Label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 34</value>
  </data>
  <data name="Label3.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="Label3.Text" xml:space="preserve">
    <value>ابحث عن اسم الوحدة</value>
  </data>
  <data name="Label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;Label3.Name" xml:space="preserve">
    <value>Label3</value>
  </data>
  <data name="&gt;&gt;Label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Label3.Parent" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Label3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="Panel4.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="Panel4.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="Panel4.Size" type="System.Drawing.Size, System.Drawing">
    <value>776, 720</value>
  </data>
  <data name="Panel4.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;Panel4.Name" xml:space="preserve">
    <value>Panel4</value>
  </data>
  <data name="&gt;&gt;Panel4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;Panel4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;Panel4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>12, 32</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>776, 720</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Hacen Saudi Arabia, 12pt, style=Bold</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="$this.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>Frm_Add_Unit</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>