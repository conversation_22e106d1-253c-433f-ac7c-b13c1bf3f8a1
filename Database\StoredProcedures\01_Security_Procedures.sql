-- =====================================================
-- El Dawliya International System - Security Stored Procedures
-- Version: 2.0
-- Date: 2025-01-30
-- Description: Security and authentication procedures
-- =====================================================

-- =====================================================
-- User Authentication Procedures
-- =====================================================

-- Authenticate user with enhanced security
CREATE OR ALTER PROCEDURE Security.sp_AuthenticateUser
    @Username NVARCHAR(50),
    @PasswordHash NVARCHAR(255),
    @IPAddress NVARCHAR(45) = NULL,
    @UserAgent NVARCHAR(500) = NULL,
    @DeviceName NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserID INT = NULL
    DECLARE @IsLocked BIT = 0
    DECLARE @FailedAttempts INT = 0
    DECLARE @SessionID UNIQUEIDENTIFIER
    
    BEGIN TRY
        -- Check if user exists and get details
        SELECT 
            @UserID = UserID,
            @IsLocked = IsLocked,
            @FailedAttempts = FailedLoginAttempts
        FROM Security.Users 
        WHERE Username = @Username AND IsActive = 1
        
        -- Log security event
        INSERT INTO Audit.SecurityLog (EventType, Username, IPAddress, UserAgent, EventDescription, Severity)
        VALUES ('LOGIN_ATTEMPT', @Username, @IPAddress, @UserAgent, 'User login attempt', 'INFO')
        
        -- Check if user exists
        IF @UserID IS NULL
        BEGIN
            INSERT INTO Audit.SecurityLog (EventType, Username, IPAddress, UserAgent, EventDescription, Severity)
            VALUES ('FAILED_LOGIN', @Username, @IPAddress, @UserAgent, 'User not found', 'WARNING')
            
            SELECT 0 AS Success, 'Invalid username or password' AS Message, NULL AS UserID, NULL AS SessionID
            RETURN
        END
        
        -- Check if account is locked
        IF @IsLocked = 1
        BEGIN
            INSERT INTO Audit.SecurityLog (EventType, UserID, Username, IPAddress, UserAgent, EventDescription, Severity)
            VALUES ('FAILED_LOGIN', @UserID, @Username, @IPAddress, @UserAgent, 'Account locked', 'WARNING')
            
            SELECT 0 AS Success, 'Account is locked. Please contact administrator.' AS Message, NULL AS UserID, NULL AS SessionID
            RETURN
        END
        
        -- Verify password
        IF EXISTS (SELECT 1 FROM Security.Users WHERE UserID = @UserID AND PasswordHash = @PasswordHash)
        BEGIN
            -- Successful login
            SET @SessionID = NEWID()
            
            -- Update user login info
            UPDATE Security.Users 
            SET 
                LastLoginDate = GETDATE(),
                FailedLoginAttempts = 0
            WHERE UserID = @UserID
            
            -- Create session
            INSERT INTO Security.UserSessions (SessionID, UserID, IPAddress, UserAgent, DeviceName)
            VALUES (@SessionID, @UserID, @IPAddress, @UserAgent, @DeviceName)
            
            -- Log successful login
            INSERT INTO Audit.SecurityLog (EventType, UserID, Username, IPAddress, UserAgent, EventDescription, Severity)
            VALUES ('LOGIN_SUCCESS', @UserID, @Username, @IPAddress, @UserAgent, 'Successful login', 'INFO')
            
            SELECT 1 AS Success, 'Login successful' AS Message, @UserID AS UserID, @SessionID AS SessionID
        END
        ELSE
        BEGIN
            -- Failed login
            SET @FailedAttempts = @FailedAttempts + 1
            
            -- Update failed attempts
            UPDATE Security.Users 
            SET FailedLoginAttempts = @FailedAttempts,
                IsLocked = CASE WHEN @FailedAttempts >= 5 THEN 1 ELSE 0 END
            WHERE UserID = @UserID
            
            -- Log failed login
            INSERT INTO Audit.SecurityLog (EventType, UserID, Username, IPAddress, UserAgent, EventDescription, Severity)
            VALUES ('FAILED_LOGIN', @UserID, @Username, @IPAddress, @UserAgent, 
                    'Invalid password. Attempt ' + CAST(@FailedAttempts AS VARCHAR), 'WARNING')
            
            SELECT 0 AS Success, 'Invalid username or password' AS Message, NULL AS UserID, NULL AS SessionID
        END
        
    END TRY
    BEGIN CATCH
        -- Log error
        INSERT INTO Audit.SecurityLog (EventType, Username, IPAddress, UserAgent, EventDescription, Severity)
        VALUES ('LOGIN_ERROR', @Username, @IPAddress, @UserAgent, ERROR_MESSAGE(), 'ERROR')
        
        SELECT 0 AS Success, 'An error occurred during authentication' AS Message, NULL AS UserID, NULL AS SessionID
    END CATCH
END
GO

-- Logout user and end session
CREATE OR ALTER PROCEDURE Security.sp_LogoutUser
    @SessionID UNIQUEIDENTIFIER,
    @UserID INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Update session
        UPDATE Security.UserSessions 
        SET 
            LogoutTime = GETDATE(),
            IsActive = 0
        WHERE SessionID = @SessionID
        
        -- Get user info for logging
        DECLARE @Username NVARCHAR(50)
        SELECT @Username = Username FROM Security.Users WHERE UserID = @UserID
        
        -- Log logout
        INSERT INTO Audit.SecurityLog (EventType, UserID, Username, EventDescription, Severity)
        VALUES ('LOGOUT', @UserID, @Username, 'User logged out', 'INFO')
        
        SELECT 1 AS Success, 'Logout successful' AS Message
        
    END TRY
    BEGIN CATCH
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message
    END CATCH
END
GO

-- =====================================================
-- Permission Management Procedures
-- =====================================================

-- Get user permissions
CREATE OR ALTER PROCEDURE Security.sp_GetUserPermissions
    @UserID INT,
    @ModuleName NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT DISTINCT
        p.PermissionID,
        p.PermissionName,
        p.ModuleName,
        p.ResourceName,
        p.ActionName,
        CASE 
            WHEN up.IsGranted IS NOT NULL THEN up.IsGranted
            WHEN rp.PermissionID IS NOT NULL THEN 1
            ELSE 0
        END AS IsGranted
    FROM Security.Permissions p
    LEFT JOIN Security.UserPermissions up ON p.PermissionID = up.PermissionID AND up.UserID = @UserID
    LEFT JOIN Security.UserRoles ur ON ur.UserID = @UserID AND ur.IsActive = 1
    LEFT JOIN Security.RolePermissions rp ON rp.RoleID = ur.RoleID AND rp.PermissionID = p.PermissionID
    WHERE (@ModuleName IS NULL OR p.ModuleName = @ModuleName)
        AND (up.ExpiryDate IS NULL OR up.ExpiryDate > GETDATE())
        AND (ur.ExpiryDate IS NULL OR ur.ExpiryDate > GETDATE())
    ORDER BY p.ModuleName, p.ResourceName, p.ActionName
END
GO

-- Check specific permission
CREATE OR ALTER PROCEDURE Security.sp_CheckPermission
    @UserID INT,
    @PermissionName NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @HasPermission BIT = 0
    
    -- Check user-specific permissions first
    SELECT @HasPermission = IsGranted
    FROM Security.UserPermissions up
    INNER JOIN Security.Permissions p ON up.PermissionID = p.PermissionID
    WHERE up.UserID = @UserID 
        AND p.PermissionName = @PermissionName
        AND (up.ExpiryDate IS NULL OR up.ExpiryDate > GETDATE())
    
    -- If no user-specific permission, check role permissions
    IF @HasPermission IS NULL
    BEGIN
        SELECT @HasPermission = CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
        FROM Security.UserRoles ur
        INNER JOIN Security.RolePermissions rp ON ur.RoleID = rp.RoleID
        INNER JOIN Security.Permissions p ON rp.PermissionID = p.PermissionID
        WHERE ur.UserID = @UserID 
            AND ur.IsActive = 1
            AND p.PermissionName = @PermissionName
            AND (ur.ExpiryDate IS NULL OR ur.ExpiryDate > GETDATE())
    END
    
    SELECT COALESCE(@HasPermission, 0) AS HasPermission
END
GO

-- =====================================================
-- User Management Procedures
-- =====================================================

-- Create new user
CREATE OR ALTER PROCEDURE Security.sp_CreateUser
    @Username NVARCHAR(50),
    @Email NVARCHAR(100),
    @PasswordHash NVARCHAR(255),
    @PasswordSalt NVARCHAR(255),
    @FirstName NVARCHAR(50),
    @LastName NVARCHAR(50),
    @EmployeeID INT = NULL,
    @CreatedBy INT,
    @UserID INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION
        
        -- Check if username already exists
        IF EXISTS (SELECT 1 FROM Security.Users WHERE Username = @Username)
        BEGIN
            RAISERROR('Username already exists', 16, 1)
            RETURN
        END
        
        -- Check if email already exists
        IF EXISTS (SELECT 1 FROM Security.Users WHERE Email = @Email)
        BEGIN
            RAISERROR('Email already exists', 16, 1)
            RETURN
        END
        
        -- Insert new user
        INSERT INTO Security.Users (
            Username, Email, PasswordHash, PasswordSalt, FirstName, LastName,
            EmployeeID, CreatedBy, CreatedDate, LastPasswordChangeDate
        )
        VALUES (
            @Username, @Email, @PasswordHash, @PasswordSalt, @FirstName, @LastName,
            @EmployeeID, @CreatedBy, GETDATE(), GETDATE()
        )
        
        SET @UserID = SCOPE_IDENTITY()
        
        -- Log user creation
        INSERT INTO Audit.SecurityLog (EventType, UserID, Username, EventDescription, Severity)
        VALUES ('USER_CREATED', @CreatedBy, @Username, 'New user created: ' + @Username, 'INFO')
        
        COMMIT TRANSACTION
        
        SELECT 1 AS Success, 'User created successfully' AS Message, @UserID AS UserID
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        
        INSERT INTO Audit.SecurityLog (EventType, EventDescription, Severity)
        VALUES ('USER_CREATION_ERROR', ERROR_MESSAGE(), 'ERROR')
        
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message, NULL AS UserID
    END CATCH
END
GO

-- Update user password
CREATE OR ALTER PROCEDURE Security.sp_UpdateUserPassword
    @UserID INT,
    @NewPasswordHash NVARCHAR(255),
    @NewPasswordSalt NVARCHAR(255),
    @UpdatedBy INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION
        
        -- Store old password in history
        INSERT INTO Security.PasswordHistory (UserID, PasswordHash, PasswordSalt)
        SELECT UserID, PasswordHash, PasswordSalt
        FROM Security.Users
        WHERE UserID = @UserID
        
        -- Update password
        UPDATE Security.Users
        SET 
            PasswordHash = @NewPasswordHash,
            PasswordSalt = @NewPasswordSalt,
            LastPasswordChangeDate = GETDATE(),
            MustChangePassword = 0,
            ModifiedDate = GETDATE(),
            ModifiedBy = @UpdatedBy
        WHERE UserID = @UserID
        
        -- Log password change
        DECLARE @Username NVARCHAR(50)
        SELECT @Username = Username FROM Security.Users WHERE UserID = @UserID
        
        INSERT INTO Audit.SecurityLog (EventType, UserID, Username, EventDescription, Severity)
        VALUES ('PASSWORD_CHANGED', @UserID, @Username, 'Password changed', 'INFO')
        
        COMMIT TRANSACTION
        
        SELECT 1 AS Success, 'Password updated successfully' AS Message
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        SELECT 0 AS Success, ERROR_MESSAGE() AS Message
    END CATCH
END
GO

-- =====================================================
-- Session Management Procedures
-- =====================================================

-- Validate session
CREATE OR ALTER PROCEDURE Security.sp_ValidateSession
    @SessionID UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserID INT
    DECLARE @IsValid BIT = 0
    DECLARE @TimeoutMinutes INT = 30
    
    -- Get timeout setting
    SELECT @TimeoutMinutes = CAST(SettingValue AS INT)
    FROM dbo.SystemSettings
    WHERE SettingKey = 'SessionTimeoutMinutes'
    
    -- Check session validity
    SELECT @UserID = UserID
    FROM Security.UserSessions
    WHERE SessionID = @SessionID
        AND IsActive = 1
        AND DATEDIFF(MINUTE, LastActivityTime, GETDATE()) <= @TimeoutMinutes
    
    IF @UserID IS NOT NULL
    BEGIN
        SET @IsValid = 1
        
        -- Update last activity time
        UPDATE Security.UserSessions
        SET LastActivityTime = GETDATE()
        WHERE SessionID = @SessionID
    END
    
    SELECT @IsValid AS IsValid, @UserID AS UserID
END
GO
