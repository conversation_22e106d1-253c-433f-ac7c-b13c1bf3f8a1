//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

[assembly: System.Reflection.AssemblyVersion("*******")]
[assembly: System.Windows.Forms.AxHost.TypeLibraryTimeStamp("06/27/2024 15:22:40")]

namespace AxAcroPDFLib {
    
    
    [System.Windows.Forms.AxHost.ClsidAttribute("{ca8a9780-280d-11cf-a24d-444553540000}")]
    [System.ComponentModel.DesignTimeVisibleAttribute(true)]
    public class AxAcroPDF : System.Windows.Forms.AxHost {
        
        private AcroPDFLib.IAcroAXDocShim ocx;
        
        private AxAcroPDFEventMulticaster eventMulticaster;
        
        private System.Windows.Forms.AxHost.ConnectionPointCookie cookie;
        
        public AxAcroPDF() : 
                base("ca8a9780-280d-11cf-a24d-444553540000") {
        }
        
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        [System.Runtime.InteropServices.DispIdAttribute(1)]
        public virtual string src {
            get {
                if ((this.ocx == null)) {
                    throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("src", System.Windows.Forms.AxHost.ActiveXInvokeKind.PropertyGet);
                }
                return this.ocx.src;
            }
            set {
                if ((this.ocx == null)) {
                    throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("src", System.Windows.Forms.AxHost.ActiveXInvokeKind.PropertySet);
                }
                this.ocx.src = value;
            }
        }
        
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        [System.Runtime.InteropServices.DispIdAttribute(30)]
        public virtual object messageHandler {
            get {
                if ((this.ocx == null)) {
                    throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("messageHandler", System.Windows.Forms.AxHost.ActiveXInvokeKind.PropertyGet);
                }
                return this.ocx.messageHandler;
            }
            set {
                if ((this.ocx == null)) {
                    throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("messageHandler", System.Windows.Forms.AxHost.ActiveXInvokeKind.PropertySet);
                }
                this.ocx.messageHandler = value;
            }
        }
        
        public virtual bool LoadFile(string fileName) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("LoadFile", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            bool returnValue = ((bool)(this.ocx.LoadFile(fileName)));
            return returnValue;
        }
        
        public virtual void setShowToolbar(bool on) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setShowToolbar", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setShowToolbar(on);
        }
        
        public virtual void gotoFirstPage() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("gotoFirstPage", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.gotoFirstPage();
        }
        
        public virtual void gotoLastPage() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("gotoLastPage", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.gotoLastPage();
        }
        
        public virtual void gotoNextPage() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("gotoNextPage", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.gotoNextPage();
        }
        
        public virtual void gotoPreviousPage() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("gotoPreviousPage", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.gotoPreviousPage();
        }
        
        public virtual void setCurrentPage(int n) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setCurrentPage", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setCurrentPage(n);
        }
        
        public virtual void goForwardStack() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("goForwardStack", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.goForwardStack();
        }
        
        public virtual void goBackwardStack() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("goBackwardStack", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.goBackwardStack();
        }
        
        public virtual void setPageMode(string pageMode) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setPageMode", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setPageMode(pageMode);
        }
        
        public virtual void setLayoutMode(string layoutMode) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setLayoutMode", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setLayoutMode(layoutMode);
        }
        
        public virtual void setNamedDest(string namedDest) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setNamedDest", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setNamedDest(namedDest);
        }
        
        public virtual void Print() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("Print", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.Print();
        }
        
        public virtual void printWithDialog() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("printWithDialog", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.printWithDialog();
        }
        
        public virtual void setZoom(float percent) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setZoom", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setZoom(percent);
        }
        
        public virtual void setZoomScroll(float percent, float left, float top) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setZoomScroll", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setZoomScroll(percent, left, top);
        }
        
        public virtual void setView(string viewMode) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setView", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setView(viewMode);
        }
        
        public virtual void setViewScroll(string viewMode, float offset) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setViewScroll", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setViewScroll(viewMode, offset);
        }
        
        public virtual void setViewRect(float left, float top, float width, float height) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setViewRect", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setViewRect(left, top, width, height);
        }
        
        public virtual void printPages(int from, int to) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("printPages", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.printPages(from, to);
        }
        
        public virtual void printPagesFit(int from, int to, bool shrinkToFit) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("printPagesFit", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.printPagesFit(from, to, shrinkToFit);
        }
        
        public virtual void printAll() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("printAll", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.printAll();
        }
        
        public virtual void printAllFit(bool shrinkToFit) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("printAllFit", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.printAllFit(shrinkToFit);
        }
        
        public virtual void setShowScrollbars(bool on) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setShowScrollbars", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setShowScrollbars(on);
        }
        
        public virtual object GetVersions() {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("GetVersions", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            object returnValue = ((object)(this.ocx.GetVersions()));
            return returnValue;
        }
        
        public virtual void setCurrentHightlight(int a, int b, int c, int d) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setCurrentHightlight", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setCurrentHightlight(a, b, c, d);
        }
        
        public virtual void setCurrentHighlight(int a, int b, int c, int d) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("setCurrentHighlight", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.setCurrentHighlight(a, b, c, d);
        }
        
        public virtual void postMessage(object strArray) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("postMessage", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.postMessage(strArray);
        }
        
        public virtual void execCommand(object strArray) {
            if ((this.ocx == null)) {
                throw new System.Windows.Forms.AxHost.InvalidActiveXStateException("execCommand", System.Windows.Forms.AxHost.ActiveXInvokeKind.MethodInvoke);
            }
            this.ocx.execCommand(strArray);
        }
        
        protected override void CreateSink() {
            try {
                this.eventMulticaster = new AxAcroPDFEventMulticaster(this);
                this.cookie = new System.Windows.Forms.AxHost.ConnectionPointCookie(this.ocx, this.eventMulticaster, typeof(AcroPDFLib._IAcroPDFEvents));
            }
            catch (System.Exception ) {
            }
        }
        
        protected override void DetachSink() {
            try {
                this.cookie.Disconnect();
            }
            catch (System.Exception ) {
            }
        }
        
        protected override void AttachInterfaces() {
            try {
                this.ocx = ((AcroPDFLib.IAcroAXDocShim)(this.GetOcx()));
            }
            catch (System.Exception ) {
            }
        }
    }
    
    [System.Runtime.InteropServices.ClassInterface(System.Runtime.InteropServices.ClassInterfaceType.None)]
    public class AxAcroPDFEventMulticaster : AcroPDFLib._IAcroPDFEvents {
        
        private AxAcroPDF parent;
        
        public AxAcroPDFEventMulticaster(AxAcroPDF parent) {
            this.parent = parent;
        }
    }
}
