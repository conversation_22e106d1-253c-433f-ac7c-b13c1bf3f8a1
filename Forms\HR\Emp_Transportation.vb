﻿'Imports System.Data.SqlClient
'Imports DocumentFormat.OpenXml.Wordprocessing
'Public Class Emp_Transportation
'    Public ConStr As String = GetConnectionString()
'    Public Con As New SqlConnection(ConStr)
'    Dim currentID As Integer = -1
'    Dim table As New DataTable()
'    '================================================================================================
'    Sub ClearFields()
'        currentID = -1
'        Txt_Adress_Period.Clear()
'        cmbEmployees.SelectedIndex = -1
'        txtAmount.Clear()
'        txtRoute.Value = 1
'        txtNotes.Clear()
'        Txt_Total.Text = "0"
'        dtpTransportDate.Value = DateTime.Now
'    End Sub


'    Private Sub TransportForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
'        LoadEmployees()
'        LoadTransportData()
'        dtpPeriodFrom.Value = DateTime.Now
'        dtpPeriodTo.Value = DateTime.Now
'        ' تحديد ترتيب الأعمدة باستخدام الاسم النصي
'        Dim columnsOrder() As String = {"Transport_ID", "Emp_ID", "Emp_Full_Name"}
'        For i As Integer = 0 To columnsOrder.Length - 1
'            'table.Columns(columnsOrder(i)).SetOrdinal(i)
'            If table.Columns.Contains(columnsOrder(i)) Then
'                table.Columns(columnsOrder(i)).SetOrdinal(i)
'            End If
'        Next
'        dgvTransport.DataSource = table
'        With dgvTransport
'            DataGridViewHeaderText(dgvTransport)
'        End With
'    End Sub

'    Sub LoadEmployees()
'        Using conn As New SqlConnection(ConStr)
'            Dim query As String = "SELECT Emp_ID, Emp_Full_Name FROM Tbl_Employee Where Working_Condition = 'سارى'"
'        Dim adapter As New SqlDataAdapter(query, conn)
'            Dim table As New DataTable()
'            adapter.Fill(table)
'            cmbEmployees.DataSource = table
'            cmbEmployees.DisplayMember = "Emp_Full_Name"
'            cmbEmployees.ValueMember = "Emp_ID"
'        End Using
'    End Sub

'    Sub LoadTransportData()
'        Using conn As New SqlConnection(ConStr)
'            Dim query As String = "SELECT t.*, e.Emp_Full_Name 
'                                  FROM Transportation t
'                                  INNER JOIN Tbl_Employee e ON t.Emp_ID = e.Emp_ID"
'            Dim adapter As New SqlDataAdapter(query, conn)
'            Dim table As New DataTable()
'            adapter.Fill(table)
'            dgvTransport.DataSource = table
'        End Using
'    End Sub

'    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
'        Try
'            If Not ValidateInput() Then Return
'            Using conn As New SqlConnection(ConStr)
'                conn.Open()
'                Dim query As String = "INSERT INTO Transportation 
'                                    (Emp_ID, Transport_Date, Period_From, Period_To, Amount, Route,Adress_Period)
'                                    VALUES (@empID, @date, @from, @to, @amount, @route,@Adress_Period)"
'                Using cmd As New SqlCommand(query, conn)
'                    cmd.Parameters.AddWithValue("@empID", cmbEmployees.SelectedValue)
'                    cmd.Parameters.AddWithValue("@date", dtpTransportDate.Value)
'                    cmd.Parameters.AddWithValue("@from", dtpPeriodFrom.Value)
'                    cmd.Parameters.AddWithValue("@to", dtpPeriodTo.Value)
'                    cmd.Parameters.AddWithValue("@amount", Decimal.Parse(txtAmount.Text))
'                    cmd.Parameters.AddWithValue("@route", Decimal.Parse(txtRoute.Text))
'                    cmd.Parameters.AddWithValue("@Adress_Period", Txt_Adress_Period.Text)
'                    cmd.ExecuteNonQuery()
'                End Using
'            End Using
'            LoadTransportData()
'            ClearFields()
'        Catch ex As Exception
'            MessageBox.Show("Error: " & ex.Message)
'        End Try
'    End Sub

'    Private Sub dgvTransport_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvTransport.CellClick
'        If e.RowIndex >= 0 Then
'            currentID = Integer.Parse(dgvTransport.Rows(e.RowIndex).Cells("Transport_ID").Value.ToString())
'            cmbEmployees.SelectedValue = dgvTransport.Rows(e.RowIndex).Cells("Emp_ID").Value
'            dtpTransportDate.Value = DateTime.Parse(dgvTransport.Rows(e.RowIndex).Cells("Transport_Date").Value.ToString())
'            dtpPeriodFrom.Value = DateTime.Parse(dgvTransport.Rows(e.RowIndex).Cells("Period_From").Value.ToString())
'            dtpPeriodTo.Value = DateTime.Parse(dgvTransport.Rows(e.RowIndex).Cells("Period_To").Value.ToString())
'            txtAmount.Text = dgvTransport.Rows(e.RowIndex).Cells("Amount").Value.ToString()
'            txtRoute.Text = dgvTransport.Rows(e.RowIndex).Cells("Route").Value.ToString()
'            Txt_Adress_Period.Text = dgvTransport.Rows(e.RowIndex).Cells("Adress_Period").Value.ToString()
'            Txt_Total.Text = dgvTransport.Rows(e.RowIndex).Cells("Total").Value.ToString()
'            txtNotes.Text = dgvTransport.Rows(e.RowIndex).Cells("Notes").Value.ToString()
'        End If
'    End Sub

'    Private Function ValidateInput() As Boolean
'        If dtpPeriodFrom.Value > dtpPeriodTo.Value Then
'            MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
'            Return False
'        End If
'        If Not Decimal.TryParse(txtRoute.Text, Nothing) Then
'            MessageBox.Show("عدد الرحلات يجب أن يكون رقماً")
'            Return False
'        End If
'        Return True
'    End Function

'    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
'        If currentID = -1 Then Return

'        If MessageBox.Show("Are you sure you want to delete this record?", "Confirm Delete",
'                         MessageBoxButtons.YesNo) = DialogResult.Yes Then
'            Try
'                Using conn As New SqlConnection(ConStr)
'                    conn.Open()
'                    Dim query As String = "DELETE FROM Transportation WHERE Transport_ID = @id"
'                    Using cmd As New SqlCommand(query, conn)
'                        cmd.Parameters.AddWithValue("@id", currentID)
'                        cmd.ExecuteNonQuery()
'                    End Using
'                End Using
'                LoadTransportData()
'                ClearFields()
'            Catch ex As Exception
'                MessageBox.Show("Error: " & ex.Message)
'            End Try
'        End If
'    End Sub

'    Private Sub btnGenerateReport_Click(sender As Object, e As EventArgs) Handles btnGenerateReport.Click
'        Try
'            Using conn As New SqlConnection(ConStr)
'                Dim query As String = "SELECT e.Emp_Full_Name, SUM(t.Amount) AS Total 
'                                      FROM Transportation t
'                                      INNER JOIN Tbl_Employee e ON t.Emp_ID = e.Emp_ID
'                                      GROUP BY e.Emp_Full_Name"
'                Dim adapter As New SqlDataAdapter(query, conn)
'                Dim table As New DataTable()
'                adapter.Fill(table)
'                'ReportViewer.DataSource = table
'                'ReportViewer.Refresh()
'            End Using
'        Catch ex As Exception
'            MessageBox.Show("Error generating report: " & ex.Message)
'        End Try
'    End Sub

'    Private Sub Txt_Total_Click(sender As Object, e As EventArgs) Handles Txt_Total.Click
'        Me.Txt_Total.Text = (Val(Me.txtAmount.Text) * Val(Me.txtRoute.Text)).ToString
'    End Sub

'    Public Sub DataGridViewHeaderText(ByVal DGV As DataGridView)
'        If DGV.RowCount > 0 Then
'            With DGV
'                .Columns("Transport_ID").HeaderText = "كود المواصلات"
'                .Columns("Transport_ID").Visible = False
'                .Columns("Emp_ID").HeaderText = "كود الموظف"
'                .Columns("Emp_ID").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
'                .Columns("Adress_Period").HeaderText = "عنوان الفترة"
'                .Columns("Adress_Period").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
'                .Columns("Transport_Date").HeaderText = "تاريخ المواصلات"
'                .Columns("Transport_Date").Width = 100
'                .Columns("Period_From").HeaderText = "بداية الفترة"
'                .Columns("Period_From").Width = 100
'                .Columns("Period_To").HeaderText = "نهاية الفترة"
'                .Columns("Period_To").Width = 100
'                .Columns("Transport_Type").HeaderText = "نوع المواصلات"
'                .Columns("Transport_Type").Width = 100
'                .Columns("Amount").HeaderText = "القيمة"
'                .Columns("Amount").Width = 100
'                .Columns("Route").HeaderText = "العدد"
'                .Columns("Route").Width = 100
'                .Columns("Total").HeaderText = "المجموع"
'                .Columns("Total").Width = 100
'                .Columns("Notes").HeaderText = "ملاحظات"
'                .Columns("Notes").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
'                .Columns("Emp_Full_Name").HeaderText = "اسم الموظف"
'                .Columns("Emp_Full_Name").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill

'            End With
'        End If
'    End Sub



'End Class


























Imports System.Drawing
Imports System.Data.SqlClient
Imports DocumentFormat.OpenXml.Wordprocessing
Imports System.IO
Imports Microsoft.Reporting.WinForms

Public Class Emp_Transportation
    Public ConStr As String = GetConnectionString()
    Public Con As New SqlConnection(ConStr)
    Dim currentID As Integer = -1
    Dim table As New DataTable()
    Dim isEditMode As Boolean = False

    '================================================================================================
    ' تحسين دالة مسح الحقول
    Sub ClearFields()
        currentID = -1
        isEditMode = False
        Txt_Adress_Period.Clear()
        cmbEmployees.SelectedIndex = -1
        txtAmount.Clear()
        txtRoute.Value = 1
        txtNotes.Clear()
        Txt_Total.Text = "0"
        dtpTransportDate.Value = DateTime.Now
        dtpPeriodFrom.Value = DateTime.Now
        dtpPeriodTo.Value = DateTime.Now
        'cmbTransportType.SelectedIndex = -1
        chkPaid.Checked = False

        ' تحديث حالة الأزرار
        UpdateButtonStates()

        ' تحديث شريط الحالة
        UpdateStatusBar("جاهز لإدخال بيانات جديدة")
    End Sub

    ' دالة تحديث حالة الأزرار
    Sub UpdateButtonStates()
        btnAdd.Enabled = Not isEditMode
        btnUpdate.Enabled = isEditMode
        btnDelete.Enabled = isEditMode
        btnCancel.Enabled = isEditMode
    End Sub

    ' دالة تحديث شريط الحالة
    Sub UpdateStatusBar(message As String)
        If lblStatus IsNot Nothing Then
            lblStatus.Text = message & " - " & DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")
        End If
    End Sub

    Private Sub TransportForm_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            InitializeForm()
            LoadEmployees()
            LoadTransportTypes()
            LoadTransportData()
            SetupDataGridView()
            UpdateStatusBar("تم تحميل البيانات بنجاح")
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل الصفحة: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' إعداد الصفحة الأولي
    Sub InitializeForm()
        dtpPeriodFrom.Value = DateTime.Now.Date
        dtpPeriodTo.Value = DateTime.Now.Date
        dtpTransportDate.Value = DateTime.Now.Date

        ' إعداد التحقق من المدخلات
        SetupValidation()

        ' إعداد الاختصارات
        SetupShortcuts()

        UpdateButtonStates()
    End Sub

    ' إعداد التحقق من المدخلات
    Sub SetupValidation()
        ' منع إدخال أحرف في حقل المبلغ
        AddHandler txtAmount.KeyPress, AddressOf NumericTextBox_KeyPress
        AddHandler txtRoute.KeyPress, AddressOf NumericTextBox_KeyPress

        ' التحقق من التواريخ
        AddHandler dtpPeriodFrom.ValueChanged, AddressOf DateValidation
        AddHandler dtpPeriodTo.ValueChanged, AddressOf DateValidation
    End Sub

    ' إعداد اختصارات لوحة المفاتيح
    Sub SetupShortcuts()
        ' Ctrl+N للجديد
        Me.KeyPreview = True

        ' إضافة معالج الأحداث للوحة المفاتيح
        AddHandler Me.KeyDown, AddressOf Form_KeyDown
    End Sub

    ' معالج أحداث لوحة المفاتيح
    Private Sub Form_KeyDown(sender As Object, e As KeyEventArgs)
        Select Case e.KeyCode
            Case Keys.F1 ' مساعدة
                ShowHelp()
            Case Keys.F2 ' جديد
                If e.Control Then btnNew_Click(Nothing, Nothing)
            Case Keys.F3 ' حفظ
                If e.Control Then btnAdd_Click(Nothing, Nothing)
            Case Keys.F4 ' تعديل
                If e.Control Then btnUpdate_Click(Nothing, Nothing)
            Case Keys.Delete ' حذف
                If e.Control Then btnDelete_Click(Nothing, Nothing)
            Case Keys.Escape ' إلغاء
                btnCancel_Click(Nothing, Nothing)
        End Select
    End Sub

    ' دالة المساعدة
    Sub ShowHelp()
        Dim helpText As String = "اختصارات لوحة المفاتيح:" & vbCrLf &
                                "F1 - عرض المساعدة" & vbCrLf &
                                "Ctrl+F2 - جديد" & vbCrLf &
                                "Ctrl+F3 - حفظ" & vbCrLf &
                                "Ctrl+F4 - تعديل" & vbCrLf &
                                "Ctrl+Delete - حذف" & vbCrLf &
                                "Escape - إلغاء"

        MessageBox.Show(helpText, "المساعدة", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ' التحقق من إدخال الأرقام فقط
    Private Sub NumericTextBox_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Char.IsDigit(e.KeyChar) AndAlso Not Char.IsControl(e.KeyChar) AndAlso e.KeyChar <> "."c Then
            e.Handled = True
        End If
    End Sub

    ' التحقق من صحة التواريخ
    Private Sub DateValidation(sender As Object, e As EventArgs)
        If dtpPeriodFrom.Value > dtpPeriodTo.Value Then
            dtpPeriodTo.Value = dtpPeriodFrom.Value
        End If
    End Sub

    ' إعداد DataGridView
    Sub SetupDataGridView()
        With dgvTransport
            .AllowUserToAddRows = False
            .AllowUserToDeleteRows = False
            .ReadOnly = True
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect
            .MultiSelect = False
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill

            ' تنسيق الألوان
            .AlternatingRowsDefaultCellStyle.BackColor = System.Drawing.Color.LightGray
            .DefaultCellStyle.SelectionBackColor = System.Drawing.Color.DarkBlue
            .DefaultCellStyle.SelectionForeColor = System.Drawing.Color.White


            ' إضافة معالج النقر المزدوج
            AddHandler .CellDoubleClick, AddressOf dgvTransport_CellDoubleClick
        End With

        ' ترتيب الأعمدة
        If table.Columns.Count > 0 Then
            Dim columnsOrder() As String = {"Transport_ID", "Emp_ID", "Emp_Full_Name", "Transport_Date", "Period_From", "Period_To"}
            For i As Integer = 0 To Math.Min(columnsOrder.Length - 1, table.Columns.Count - 1)
                If table.Columns.Contains(columnsOrder(i)) Then
                    table.Columns(columnsOrder(i)).SetOrdinal(i)
                End If
            Next
        End If
    End Sub

    ' تحميل الموظفين مع تحسينات
    Sub LoadEmployees()
        Try
            Using conn As New SqlConnection(ConStr)
                Dim query As String = "SELECT Emp_ID, Emp_Full_Name, Dept_Name 
                                      FROM Tbl_Employee 
                                      WHERE Working_Condition = N'سارى' 
                                      ORDER BY Emp_Full_Name"

                Dim adapter As New SqlDataAdapter(query, conn)
                Dim dt As New DataTable()
                adapter.Fill(dt)

                ' إضافة صف فارغ في البداية
                Dim emptyRow As DataRow = dt.NewRow()
                emptyRow("Emp_ID") = -1
                emptyRow("Emp_Full_Name") = "-- اختر الموظف --"
                dt.Rows.InsertAt(emptyRow, 0)

                cmbEmployees.DataSource = dt
                cmbEmployees.DisplayMember = "Emp_Full_Name"
                cmbEmployees.ValueMember = "Emp_ID"
                cmbEmployees.SelectedIndex = 0
            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل بيانات الموظفين: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحميل أنواع المواصلات
    Sub LoadTransportTypes()
        Try
            ' يمكنك تعديل هذا ليتم التحميل من قاعدة البيانات
            Dim transportTypes As New List(Of String) From {
                "-- اختر نوع المواصلات --",
                "أتوبيس",
                "ميكروباص",
                "تاكسي",
                "مترو",
                "سيارة خاصة",
                "أخرى"
            }

            cmbTransportType.DataSource = transportTypes
            cmbTransportType.SelectedIndex = 0
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل أنواع المواصلات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحميل بيانات المواصلات مع تحسينات
    Sub LoadTransportData(Optional searchFilter As String = "")
        Try
            Using conn As New SqlConnection(ConStr)
                Dim query As String = "SELECT t.*, e.Emp_Full_Name, e.Dept_Name,
                                            CASE WHEN t.Is_Paid = 1 THEN N'مدفوع' ELSE N'غير مدفوع' END AS Payment_Status
                                      FROM Transportation t
                                      INNER JOIN Tbl_Employee e ON t.Emp_ID = e.Emp_ID"

                ' إضافة فلتر البحث إذا وجد
                If Not String.IsNullOrEmpty(searchFilter) Then
                    query += " WHERE e.Emp_Full_Name LIKE @search OR t.Notes LIKE @search"
                End If

                query += " ORDER BY t.Transport_Date DESC, t.Transport_ID DESC"

                Dim adapter As New SqlDataAdapter(query, conn)
                If Not String.IsNullOrEmpty(searchFilter) Then
                    adapter.SelectCommand.Parameters.AddWithValue("@search", "%" & searchFilter & "%")
                End If

                table = New DataTable()
                adapter.Fill(table)
                dgvTransport.DataSource = table

                ' تحديث عناوين الأعمدة
                If table.Rows.Count > 0 Then
                    DataGridViewHeaderText(dgvTransport)
                End If

                ' تحديث إحصائيات
                UpdateStatistics()

            End Using
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' تحديث الإحصائيات
    Sub UpdateStatistics()
        Try
            If table IsNot Nothing AndAlso table.Rows.Count > 0 Then
                Dim totalAmount As Decimal = 0
                Dim totalTrips As Integer = 0

                For Each row As DataRow In table.Rows
                    If Not IsDBNull(row("Total")) Then
                        totalAmount += Convert.ToDecimal(row("Total"))
                    End If
                    If Not IsDBNull(row("Route")) Then
                        totalTrips += Convert.ToInt32(row("Route"))
                    End If
                Next

                ' تحديث التسميات
                If lblTotalAmount IsNot Nothing Then lblTotalAmount.Text = "إجمالي المبلغ: " & totalAmount.ToString("N2")
                If lblTotalTrips IsNot Nothing Then lblTotalTrips.Text = "إجمالي الرحلات: " & totalTrips.ToString()
                If lblRecordCount IsNot Nothing Then lblRecordCount.Text = "عدد السجلات: " & table.Rows.Count.ToString()
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في الإحصائيات
        End Try
    End Sub

    ' زر جديد
    Private Sub btnNew_Click(sender As Object, e As EventArgs) Handles btnNew.Click
        ClearFields()
        cmbEmployees.Focus()
    End Sub

    ' زر الإضافة مع تحسينات
    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        Try
            If Not ValidateInput() Then Return

            ' التحقق من عدم التكرار
            If CheckDuplicateRecord() Then
                If MessageBox.Show("يوجد سجل مشابه بالفعل. هل تريد المتابعة؟", "تحذير",
                                 MessageBoxButtons.YesNo, MessageBoxIcon.Warning) = DialogResult.No Then
                    Return
                End If
            End If

            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Using transaction As SqlTransaction = conn.BeginTransaction()
                    Try
                        Dim query As String = "INSERT INTO Transportation 
                                            (Emp_ID, Transport_Date, Period_From, Period_To, Amount, Route, 
                                             Adress_Period, Transport_Type, Notes, Is_Paid, Created_Date, Created_By)
                                            VALUES (@empID, @date, @from, @to, @amount, @route, @address, 
                                                   @type, @notes, @paid, @created, @createdBy)"

                        Using cmd As New SqlCommand(query, conn, transaction)
                            AddParameters(cmd)
                            cmd.Parameters.AddWithValue("@created", DateTime.Now)
                            cmd.Parameters.AddWithValue("@createdBy", Environment.UserName)

                            cmd.ExecuteNonQuery()
                        End Using

                        transaction.Commit()

                        MessageBox.Show("تم حفظ البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                        LoadTransportData()
                        ClearFields()
                        UpdateStatusBar("تم إضافة سجل جديد")

                    Catch ex As Exception
                        transaction.Rollback()
                        Throw ex
                    End Try
                End Using
            End Using

        Catch ex As Exception
            MessageBox.Show("خطأ في حفظ البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' زر التعديل
    Private Sub btnUpdate_Click(sender As Object, e As EventArgs) Handles btnUpdate.Click
        Try
            If currentID = -1 Then
                MessageBox.Show("يرجى اختيار سجل للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If Not ValidateInput() Then Return

            Using conn As New SqlConnection(ConStr)
                conn.Open()
                Using transaction As SqlTransaction = conn.BeginTransaction()
                    Try
                        Dim query As String = "UPDATE Transportation SET 
                                            Emp_ID = @empID, Transport_Date = @date, Period_From = @from, 
                                            Period_To = @to, Amount = @amount, Route = @route, 
                                            Adress_Period = @address, Transport_Type = @type, 
                                            Notes = @notes, Is_Paid = @paid,
                                            Modified_Date = @modified, Modified_By = @modifiedBy
                                            WHERE Transport_ID = @id"

                        Using cmd As New SqlCommand(query, conn, transaction)
                            AddParameters(cmd)
                            cmd.Parameters.AddWithValue("@id", currentID)
                            cmd.Parameters.AddWithValue("@modified", DateTime.Now)
                            cmd.Parameters.AddWithValue("@modifiedBy", Environment.UserName)

                            cmd.ExecuteNonQuery()
                        End Using

                        transaction.Commit()

                        MessageBox.Show("تم تحديث البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                        LoadTransportData()
                        ClearFields()
                        UpdateStatusBar("تم تحديث السجل")

                    Catch ex As Exception
                        transaction.Rollback()
                        Throw ex
                    End Try
                End Using
            End Using

        Catch ex As Exception
            MessageBox.Show("خطأ في تحديث البيانات: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ' زر الإلغاء
    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        ClearFields()
        UpdateStatusBar("تم إلغاء العملية")
    End Sub

    ' إضافة المعاملات للاستعلام
    Sub AddParameters(cmd As SqlCommand)
        cmd.Parameters.AddWithValue("@empID", cmbEmployees.SelectedValue)
        cmd.Parameters.AddWithValue("@date", dtpTransportDate.Value.Date)
        cmd.Parameters.AddWithValue("@from", dtpPeriodFrom.Value.Date)
        cmd.Parameters.AddWithValue("@to", dtpPeriodTo.Value.Date)
        cmd.Parameters.AddWithValue("@amount", If(String.IsNullOrEmpty(txtAmount.Text), 0, Decimal.Parse(txtAmount.Text)))
        cmd.Parameters.AddWithValue("@route", If(String.IsNullOrEmpty(txtRoute.Text), 1, Decimal.Parse(txtRoute.Text)))
        cmd.Parameters.AddWithValue("@address", Txt_Adress_Period.Text.Trim())
        cmd.Parameters.AddWithValue("@type", If(cmbTransportType.SelectedIndex <= 0, "", cmbTransportType.Text))
        cmd.Parameters.AddWithValue("@notes", txtNotes.Text.Trim())
        cmd.Parameters.AddWithValue("@total", If(String.IsNullOrEmpty(Txt_Total.Text), 0, Decimal.Parse(Txt_Total.Text)))
        cmd.Parameters.AddWithValue("@paid", chkPaid.Checked)
    End Sub

    ' فحص التكرار
    Function CheckDuplicateRecord() As Boolean
        Try
            Using conn As New SqlConnection(ConStr)
                Dim query As String = "SELECT COUNT(*) FROM Transportation 
                                      WHERE Emp_ID = @empID AND Transport_Date = @date 
                                      AND Period_From = @from AND Period_To = @to"

                If currentID <> -1 Then
                    query += " AND Transport_ID <> @currentID"
                End If

                Using cmd As New SqlCommand(query, conn)
                    cmd.Parameters.AddWithValue("@empID", cmbEmployees.SelectedValue)
                    cmd.Parameters.AddWithValue("@date", dtpTransportDate.Value.Date)
                    cmd.Parameters.AddWithValue("@from", dtpPeriodFrom.Value.Date)
                    cmd.Parameters.AddWithValue("@to", dtpPeriodTo.Value.Date)

                    If currentID <> -1 Then
                        cmd.Parameters.AddWithValue("@currentID", currentID)
                    End If

                    conn.Open()
                    Return Convert.ToInt32(cmd.ExecuteScalar()) > 0
                End Using
            End Using
        Catch ex As Exception
            Return False
        End Try
    End Function

    ' النقر على الخلية
    Private Sub dgvTransport_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles dgvTransport.CellClick
        LoadSelectedRecord(e.RowIndex)
    End Sub

    ' النقر المزدوج للتعديل
    Private Sub dgvTransport_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If LoadSelectedRecord(e.RowIndex) Then
            isEditMode = True
            UpdateButtonStates()
            UpdateStatusBar("وضع التعديل")
        End If
    End Sub

    ' تحميل السجل المحدد
    Function LoadSelectedRecord(rowIndex As Integer) As Boolean
        Try
            If rowIndex >= 0 AndAlso rowIndex < dgvTransport.Rows.Count Then
                Dim row As DataGridViewRow = dgvTransport.Rows(rowIndex)

                currentID = If(IsDBNull(row.Cells("Transport_ID").Value), -1, Integer.Parse(row.Cells("Transport_ID").Value.ToString()))

                If currentID <> -1 Then
                    cmbEmployees.SelectedValue = row.Cells("Emp_ID").Value
                    dtpTransportDate.Value = If(IsDBNull(row.Cells("Transport_Date").Value), DateTime.Now, DateTime.Parse(row.Cells("Transport_Date").Value.ToString()))
                    dtpPeriodFrom.Value = If(IsDBNull(row.Cells("Period_From").Value), DateTime.Now, DateTime.Parse(row.Cells("Period_From").Value.ToString()))
                    dtpPeriodTo.Value = If(IsDBNull(row.Cells("Period_To").Value), DateTime.Now, DateTime.Parse(row.Cells("Period_To").Value.ToString()))
                    txtAmount.Text = If(IsDBNull(row.Cells("Amount").Value), "0", row.Cells("Amount").Value.ToString())
                    txtRoute.Text = If(IsDBNull(row.Cells("Route").Value), "1", row.Cells("Route").Value.ToString())
                    Txt_Adress_Period.Text = If(IsDBNull(row.Cells("Adress_Period").Value), "", row.Cells("Adress_Period").Value.ToString())
                    Txt_Total.Text = If(IsDBNull(row.Cells("Total").Value), "0", row.Cells("Total").Value.ToString())
                    txtNotes.Text = If(IsDBNull(row.Cells("Notes").Value), "", row.Cells("Notes").Value.ToString())

                    ' تحميل نوع المواصلات
                    If Not IsDBNull(row.Cells("Transport_Type").Value) Then
                        Dim transportType As String = row.Cells("Transport_Type").Value.ToString()
                        For i As Integer = 0 To cmbTransportType.Items.Count - 1
                            If cmbTransportType.Items(i).ToString() = transportType Then
                                cmbTransportType.SelectedIndex = i
                                Exit For
                            End If
                        Next
                    End If

                    ' تحميل حالة الدفع
                    If Not IsDBNull(row.Cells("Is_Paid").Value) Then
                        chkPaid.Checked = Convert.ToBoolean(row.Cells("Is_Paid").Value)
                    End If

                    Return True
                End If
            End If
            Return False
        Catch ex As Exception
            MessageBox.Show("خطأ في تحميل السجل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ' تحسين دالة التحقق
    Private Function ValidateInput() As Boolean
        ' التحقق من اختيار الموظف
        If cmbEmployees.SelectedIndex <= 0 OrElse cmbEmployees.SelectedValue Is Nothing OrElse
           Convert.ToInt32(cmbEmployees.SelectedValue) = -1 Then
            MessageBox.Show("يرجى اختيار الموظف", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            cmbEmployees.Focus()
            Return False
        End If

        ' التحقق من التواريخ
        If dtpPeriodFrom.Value > dtpPeriodTo.Value Then
            MessageBox.Show("تاريخ البداية يجب أن يكون قبل أو يساوي تاريخ النهاية", "خطأ في التاريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            dtpPeriodFrom.Focus()
            Return False
        End If

        ' التحقق من المبلغ
        If String.IsNullOrEmpty(txtAmount.Text) OrElse Not IsNumeric(txtAmount.Text) OrElse Decimal.Parse(txtAmount.Text) < 0 Then
            MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في المبلغ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtAmount.Focus()
            Return False
        End If

        ' التحقق من عدد الرحلات
        If String.IsNullOrEmpty(txtRoute.Text) OrElse Not IsNumeric(txtRoute.Text) OrElse Integer.Parse(txtRoute.Text) <= 0 Then
            MessageBox.Show("يرجى إدخال عدد رحلات صحيح", "خطأ في عدد الرحلات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            txtRoute.Focus()
            Return False
        End If

        ' التحقق من تاريخ المستقبل
        If dtpTransportDate.Value.Date > DateTime.Now.Date Then
            If MessageBox.Show("التاريخ المحدد في المستقبل. هل تريد المتابعة؟", "تأكيد التاريخ",
                             MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                dtpTransportDate.Focus()
                Return False
            End If
        End If

        Return True
    End Function

    ' تحسين دالة الحذف
    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click
        If currentID = -1 Then
            MessageBox.Show("يرجى اختيار سجل للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        ' رسالة تأكيد مفصلة
        Dim empName As String = cmbEmployees.Text
        Dim transportDate As String = dtpTransportDate.Value.ToString("yyyy/MM/dd")
        Dim confirmMessage As String = $"هل أنت متأكد من حذف سجل مواصلات الموظف: {empName}{vbCrLf}بتاريخ: {transportDate}؟{vbCrLf}{vbCrLf}هذا الإجراء لا يمكن التراجع عنه!"

        If MessageBox.Show(confirmMessage, "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            Try
                Using conn As New SqlConnection(ConStr)
                    conn.Open()
                    Using transaction As SqlTransaction = conn.BeginTransaction()
                        Try
                            ' يمكن إضافة جدول سجل العمليات هنا
                            Dim query As String = "DELETE FROM Transportation WHERE Transport_ID = @id"
                            Using cmd As New SqlCommand(query, conn, transaction)
                                cmd.Parameters.AddWithValue("@id", currentID)
                                cmd.ExecuteNonQuery()
                            End Using

                            transaction.Commit()

                            MessageBox.Show("تم حذف السجل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                            LoadTransportData()
                            ClearFields()
                            UpdateStatusBar("تم حذف السجل")

                        Catch ex As Exception
                            transaction.Rollback()
                            Throw ex
                        End Try
                    End Using
                End Using
            Catch ex As Exception
                MessageBox.Show("خطأ في حذف السجل: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End Try
        End If
    End Sub

    '' تحسين تقرير مفصل
    'Private Sub btnGenerateReport_Click(sender As Object, e As EventArgs) Handles btnGenerateReport.Click, btnCancel.Click, btnUpdate.Click
    '    Try
    '        ' فتح نموذج خيارات التقرير
    '        Dim reportForm As New TransportReportForm()
    '        If reportForm.ShowDialog() = DialogResult.OK Then
    '            GenerateSelectedReport(reportForm.SelectedReportType, reportForm.DateFrom, reportForm.DateTo, reportForm.SelectedEmployeeID)
    '        End If
    '    Catch ex As Exception
    '        MessageBox.Show("خطأ في إنشاء التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '    End Try
    'End Sub

    '' إنشاء التقرير المحدد
    'Sub GenerateSelectedReport(reportType As String, dateFrom As DateTime, dateTo As DateTime, Optional empID As Integer = -1)
    '    Try
    '        Dim query As String = ""

    '        Select Case reportType
    '            Case "summary"
    '                query = "SELECT e.Emp_Full_Name AS [اسم الموظف], 
    '                           COUNT(*) AS [عدد الرحلات],
    '                           SUM(t.Route) AS [إجمالي الرحلات],
    '                           SUM(t.Total) AS [إجمالي المبلغ],
    '                           AVG(t.Amount) AS [متوسط المبلغ للرحلة]
    '                    FROM Transportation t
    '                    INNER JOIN Tbl_Employee e ON t.Emp_ID = e.Emp_ID
    '                    WHERE t.Transport_Date BETWEEN @dateFrom AND @dateTo"

    '                If empID <> -1 Then
    '                    query &= " AND t.Emp_ID = @empID"
    '                End If

    '                query &= " GROUP BY e.Emp_Full_Name
    '                       ORDER BY e.Emp_Full_Name"

    '            Case "detailed"
    '                query = "SELECT t.Transport_ID AS [رقم السجل], 
    '                           e.Emp_Full_Name AS [اسم الموظف], 
    '                           t.Transport_Date AS [تاريخ المواصلة], 
    '                           t.Period_From AS [الفترة من], 
    '                           t.Period_To AS [الفترة إلى], 
    '                           t.Amount AS [المبلغ], 
    '                           t.Route AS [عدد الرحلات], 
    '                           t.Total AS [الإجمالي], 
    '                           t.Transport_Type AS [نوع المواصلة], 
    '                           t.Adress_Period AS [فترة العنوان], 
    '                           t.Notes AS [الملاحظات],
    '                           CASE WHEN t.Is_Paid = 1 THEN N'مدفوع' ELSE N'غير مدفوع' END AS [حالة الدفع]
    '                    FROM Transportation t
    '                    INNER JOIN Tbl_Employee e ON t.Emp_ID = e.Emp_ID
    '                    WHERE t.Transport_Date BETWEEN @dateFrom AND @dateTo"

    '                If empID <> -1 Then
    '                    query &= " AND t.Emp_ID = @empID"
    '                End If

    '                query &= " ORDER BY t.Transport_Date DESC, t.Transport_ID DESC"

    '            Case Else
    '                MessageBox.Show("نوع التقرير غير مدعوم", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning)
    '                Return
    '        End Select

    '        ' إعداد مصدر البيانات
    '        Using conn As New SqlConnection(ConStr)
    '            Dim adapter As New SqlDataAdapter(query, conn)
    '            adapter.SelectCommand.Parameters.AddWithValue("@dateFrom", dateFrom.Date)
    '            adapter.SelectCommand.Parameters.AddWithValue("@dateTo", dateTo.Date)

    '            If empID <> -1 Then
    '                adapter.SelectCommand.Parameters.AddWithValue("@empID", empID)
    '            End If

    '            Dim reportData As New DataTable()
    '            adapter.Fill(reportData)

    '            ' إعداد التقرير
    '            Using reportViewer As New ReportViewer()
    '                reportViewer.ProcessingMode = ProcessingMode.Local

    '                ' تحديد مسار ملف التقرير بناءً على نوع التقرير
    '                Dim reportPath As String = If(reportType = "summary",
    '                    Path.Combine(Application.StartupPath, "Reports\TransportSummaryReport.rdlc"),
    '                    Path.Combine(Application.StartupPath, "Reports\TransportDetailedReport.rdlc"))

    '                reportViewer.LocalReport.ReportPath = reportPath

    '                ' إضافة مصدر البيانات للتقرير
    '                Dim reportDataSource As New ReportDataSource("TransportDataSet", reportData)
    '                reportViewer.LocalReport.DataSources.Clear()
    '                reportViewer.LocalReport.DataSources.Add(reportDataSource)

    '                ' إضافة معلمات التقرير
    '                Dim parameters As New List(Of ReportParameter) From {
    '                    New ReportParameter("DateFrom", dateFrom.ToString("yyyy/MM/dd")),
    '                    New ReportParameter("DateTo", dateTo.ToString("yyyy/MM/dd")),
    '                    New ReportParameter("ReportTitle", If(reportType = "summary",
    '                        "تقرير ملخص مواصلات الموظفين",
    '                        "تقرير تفصيلي لمواصلات الموظفين"))
    '                }

    '                If empID <> -1 Then
    '                    parameters.Add(New ReportParameter("EmployeeName",
    '                        cmbEmployees.Text))
    '                Else
    '                    parameters.Add(New ReportParameter("EmployeeName", "الكل"))
    '                End If

    '                reportViewer.LocalReport.SetParameters(parameters)

    '                ' تحديث التقرير
    '                reportViewer.RefreshReport()

    '                ' عرض التقرير في نموذج جديد
    '                Dim reportForm As New Form()
    '                reportForm.Text = If(reportType = "summary",
    '                    "تقرير ملخص مواصلات",
    '                    "تقرير تفصيلي للمواصلات")
    '                reportForm.Size = New Size(1000, 600)
    '                reportForm.Controls.Add(reportViewer)
    '                reportViewer.Dock = DockStyle.Fill
    '                reportForm.ShowDialog()
    '            End Using
    '        End Using

    '        UpdateStatusBar("تم إنشاء التقرير بنجاح")

    '    Catch ex As Exception
    '        MessageBox.Show("خطأ في إنشاء التقرير: " & ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    '    End Try
    'End Sub

    Public Sub DataGridViewHeaderText(ByVal DGV As DataGridView)
        If DGV.RowCount > 0 Then
            With DGV
                .Columns("Transport_ID").HeaderText = "كود المواصلات"
                .Columns("Transport_ID").Visible = False
                .Columns("Emp_ID").HeaderText = "كود الموظف"
                .Columns("Emp_ID").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                .Columns("Adress_Period").HeaderText = "عنوان الفترة"
                .Columns("Adress_Period").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                .Columns("Transport_Date").HeaderText = "تاريخ المواصلات"
                .Columns("Transport_Date").Width = 100
                .Columns("Period_From").HeaderText = "بداية الفترة"
                .Columns("Period_From").Width = 100
                .Columns("Period_To").HeaderText = "نهاية الفترة"
                .Columns("Period_To").Width = 100
                .Columns("Transport_Type").HeaderText = "نوع المواصلات"
                .Columns("Transport_Type").Width = 100
                .Columns("Amount").HeaderText = "القيمة"
                .Columns("Amount").Width = 100
                .Columns("Route").HeaderText = "العدد"
                .Columns("Route").Width = 100
                .Columns("Total").HeaderText = "المجموع"
                .Columns("Total").Width = 100
                .Columns("Notes").HeaderText = "ملاحظات"
                .Columns("Notes").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                .Columns("Emp_Full_Name").HeaderText = "اسم الموظف"
                .Columns("Emp_Full_Name").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                .Columns("Dept_Name").HeaderText = "القسم"
                .Columns("Dept_Name").AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
                .Columns("Is_Paid").HeaderText = "حالة الدفع"
                .Columns("Is_Paid").Width = 80
                .Columns("Created_Date").HeaderText = "تاريخ الإنشاء"
                .Columns("Created_Date").Width = 100
                .Columns("Created_By").HeaderText = "تم الإنشاء بواسطة"
                .Columns("Created_By").Width = 100
                .Columns("Modified_Date").HeaderText = "تاريخ التعديل"
                .Columns("Modified_Date").Width = 100
                .Columns("Modified_By").HeaderText = "تم التعديل بواسطة"
                .Columns("Modified_By").Width = 100

                ' إضافة تنسيق للأعمدة
                .Columns("Transport_Date").DefaultCellStyle.Format = "yyyy/MM/dd"
                .Columns("Period_From").DefaultCellStyle.Format = "yyyy/MM/dd"
                .Columns("Period_To").DefaultCellStyle.Format = "yyyy/MM/dd"
                .Columns("Created_Date").DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss"
                .Columns("Modified_Date").DefaultCellStyle.Format = "yyyy/MM/dd HH:mm:ss"
                .Columns("Is_Paid").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
                .Columns("Is_Paid").DefaultCellStyle.NullValue = "غير مدفوع"
                .Columns("Is_Paid").DefaultCellStyle.ForeColor = System.Drawing.Color.Red
                .Columns("Is_Paid").DefaultCellStyle.BackColor = System.Drawing.Color.LightGray
                .Columns("Is_Paid").DefaultCellStyle.SelectionForeColor = System.Drawing.Color.White
                .Columns("Is_Paid").DefaultCellStyle.SelectionBackColor = System.Drawing.Color.DarkBlue

                ' إضافة تنسيق للأعمدة المالية
                .Columns("Amount").DefaultCellStyle.Format = "N2"
                .Columns("Route").DefaultCellStyle.Format = "N0"
                .Columns("Total").DefaultCellStyle.Format = "N2"
                .Columns("Amount").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                .Columns("Route").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                .Columns("Total").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight
                .Columns("Amount").DefaultCellStyle.ForeColor = System.Drawing.Color.Green
                .Columns("Route").DefaultCellStyle.ForeColor = System.Drawing.Color.Blue
                .Columns("Total").DefaultCellStyle.ForeColor = System.Drawing.Color.DarkOrange
                .Columns("Notes").DefaultCellStyle.WrapMode = DataGridViewTriState.True
                .AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells)

                ' إضافة تنسيق للأعمدة النصية
                .Columns("Emp_Full_Name").DefaultCellStyle.WrapMode = DataGridViewTriState.True
                .Columns("Dept_Name").DefaultCellStyle.WrapMode = DataGridViewTriState.True
                .Columns("Notes").DefaultCellStyle.WrapMode = DataGridViewTriState.True
                .Columns("Emp_Full_Name").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft
                .Columns("Dept_Name").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft
                .Columns("Notes").DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft



            End With
        End If
    End Sub



End Class