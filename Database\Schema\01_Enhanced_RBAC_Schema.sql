-- =====================================================
-- El Dawliya International System - Enhanced RBAC Schema
-- Version: 2.0
-- Date: 2025-01-30
-- Description: Enhanced Role-Based Access Control Schema
-- =====================================================

-- Create new schema for security
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Security')
    EXEC('CREATE SCHEMA Security')
GO

-- Create new schema for HR
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'HR')
    EXEC('CREATE SCHEMA HR')
GO

-- Create new schema for Audit
IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = 'Audit')
    EXEC('CREATE SCHEMA Audit')
GO

-- =====================================================
-- Enhanced User Management Tables
-- =====================================================

-- Enhanced Users table with security features
CREATE TABLE Security.Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Email NVARCHAR(100) UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    PasswordSalt NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    IsLocked BIT NOT NULL DEFAULT 0,
    FailedLoginAttempts INT NOT NULL DEFAULT 0,
    LastLoginDate DATETIME2,
    LastPasswordChangeDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    PasswordExpiryDate DATETIME2,
    MustChangePassword BIT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    EmployeeID INT, -- Link to employee record
    CONSTRAINT FK_Users_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_Users_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Security.Users(UserID)
)
GO

-- Roles table for hierarchical role management
CREATE TABLE Security.Roles (
    RoleID INT IDENTITY(1,1) PRIMARY KEY,
    RoleName NVARCHAR(50) NOT NULL UNIQUE,
    RoleDescription NVARCHAR(255),
    ParentRoleID INT,
    IsSystemRole BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    CONSTRAINT FK_Roles_ParentRole FOREIGN KEY (ParentRoleID) REFERENCES Security.Roles(RoleID),
    CONSTRAINT FK_Roles_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_Roles_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Security.Users(UserID)
)
GO

-- User-Role assignments with time-based access
CREATE TABLE Security.UserRoles (
    UserRoleID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL,
    RoleID INT NOT NULL,
    AssignedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ExpiryDate DATETIME2,
    IsActive BIT NOT NULL DEFAULT 1,
    AssignedBy INT,
    CONSTRAINT FK_UserRoles_User FOREIGN KEY (UserID) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_UserRoles_Role FOREIGN KEY (RoleID) REFERENCES Security.Roles(RoleID),
    CONSTRAINT FK_UserRoles_AssignedBy FOREIGN KEY (AssignedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT UQ_UserRoles_UserRole UNIQUE (UserID, RoleID)
)
GO

-- Permissions table for granular access control
CREATE TABLE Security.Permissions (
    PermissionID INT IDENTITY(1,1) PRIMARY KEY,
    PermissionName NVARCHAR(100) NOT NULL UNIQUE,
    PermissionDescription NVARCHAR(255),
    ModuleName NVARCHAR(50) NOT NULL,
    ResourceName NVARCHAR(100),
    ActionName NVARCHAR(50) NOT NULL,
    IsSystemPermission BIT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_Permissions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Security.Users(UserID)
)
GO

-- Role-Permission assignments
CREATE TABLE Security.RolePermissions (
    RolePermissionID INT IDENTITY(1,1) PRIMARY KEY,
    RoleID INT NOT NULL,
    PermissionID INT NOT NULL,
    GrantedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    GrantedBy INT,
    CONSTRAINT FK_RolePermissions_Role FOREIGN KEY (RoleID) REFERENCES Security.Roles(RoleID),
    CONSTRAINT FK_RolePermissions_Permission FOREIGN KEY (PermissionID) REFERENCES Security.Permissions(PermissionID),
    CONSTRAINT FK_RolePermissions_GrantedBy FOREIGN KEY (GrantedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT UQ_RolePermissions_RolePermission UNIQUE (RoleID, PermissionID)
)
GO

-- User-specific permission overrides
CREATE TABLE Security.UserPermissions (
    UserPermissionID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL,
    PermissionID INT NOT NULL,
    IsGranted BIT NOT NULL, -- TRUE for grant, FALSE for deny
    GrantedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ExpiryDate DATETIME2,
    GrantedBy INT,
    Reason NVARCHAR(255),
    CONSTRAINT FK_UserPermissions_User FOREIGN KEY (UserID) REFERENCES Security.Users(UserID),
    CONSTRAINT FK_UserPermissions_Permission FOREIGN KEY (PermissionID) REFERENCES Security.Permissions(PermissionID),
    CONSTRAINT FK_UserPermissions_GrantedBy FOREIGN KEY (GrantedBy) REFERENCES Security.Users(UserID),
    CONSTRAINT UQ_UserPermissions_UserPermission UNIQUE (UserID, PermissionID)
)
GO

-- Session management for security tracking
CREATE TABLE Security.UserSessions (
    SessionID UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserID INT NOT NULL,
    LoginTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    LogoutTime DATETIME2,
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    DeviceName NVARCHAR(100),
    IsActive BIT NOT NULL DEFAULT 1,
    LastActivityTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_UserSessions_User FOREIGN KEY (UserID) REFERENCES Security.Users(UserID)
)
GO

-- Password history for password policy enforcement
CREATE TABLE Security.PasswordHistory (
    PasswordHistoryID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL,
    PasswordHash NVARCHAR(255) NOT NULL,
    PasswordSalt NVARCHAR(255) NOT NULL,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_PasswordHistory_User FOREIGN KEY (UserID) REFERENCES Security.Users(UserID)
)
GO

-- =====================================================
-- System Configuration Tables
-- =====================================================

-- System settings for configurable parameters
CREATE TABLE dbo.SystemSettings (
    SettingID INT IDENTITY(1,1) PRIMARY KEY,
    SettingKey NVARCHAR(100) NOT NULL UNIQUE,
    SettingValue NVARCHAR(MAX),
    SettingDescription NVARCHAR(255),
    DataType NVARCHAR(20) NOT NULL DEFAULT 'String',
    IsEncrypted BIT NOT NULL DEFAULT 0,
    Category NVARCHAR(50),
    IsSystemSetting BIT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    CONSTRAINT FK_SystemSettings_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Security.Users(UserID)
)
GO

-- =====================================================
-- Audit and Logging Tables
-- =====================================================

-- Comprehensive audit log
CREATE TABLE Audit.AuditLog (
    AuditID BIGINT IDENTITY(1,1) PRIMARY KEY,
    TableName NVARCHAR(100) NOT NULL,
    RecordID NVARCHAR(50),
    Action NVARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE, SELECT
    OldValues NVARCHAR(MAX),
    NewValues NVARCHAR(MAX),
    ChangedColumns NVARCHAR(MAX),
    UserID INT,
    Username NVARCHAR(50),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    ApplicationName NVARCHAR(100),
    Timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_AuditLog_User FOREIGN KEY (UserID) REFERENCES Security.Users(UserID)
)
GO

-- Security events log
CREATE TABLE Audit.SecurityLog (
    SecurityLogID BIGINT IDENTITY(1,1) PRIMARY KEY,
    EventType NVARCHAR(50) NOT NULL, -- LOGIN, LOGOUT, FAILED_LOGIN, PERMISSION_DENIED, etc.
    UserID INT,
    Username NVARCHAR(50),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    EventDescription NVARCHAR(500),
    Severity NVARCHAR(20) NOT NULL DEFAULT 'INFO', -- INFO, WARNING, ERROR, CRITICAL
    AdditionalData NVARCHAR(MAX),
    Timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_SecurityLog_User FOREIGN KEY (UserID) REFERENCES Security.Users(UserID)
)
GO

-- System activity log
CREATE TABLE Audit.ActivityLog (
    ActivityID BIGINT IDENTITY(1,1) PRIMARY KEY,
    UserID INT,
    ActivityType NVARCHAR(50) NOT NULL,
    ModuleName NVARCHAR(50),
    FormName NVARCHAR(100),
    ActionName NVARCHAR(100),
    Description NVARCHAR(500),
    Parameters NVARCHAR(MAX),
    ExecutionTime INT, -- in milliseconds
    Success BIT NOT NULL DEFAULT 1,
    ErrorMessage NVARCHAR(MAX),
    Timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_ActivityLog_User FOREIGN KEY (UserID) REFERENCES Security.Users(UserID)
)
GO

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- Users table indexes
CREATE NONCLUSTERED INDEX IX_Users_Username ON Security.Users(Username)
CREATE NONCLUSTERED INDEX IX_Users_Email ON Security.Users(Email)
CREATE NONCLUSTERED INDEX IX_Users_IsActive ON Security.Users(IsActive)
CREATE NONCLUSTERED INDEX IX_Users_EmployeeID ON Security.Users(EmployeeID)

-- UserRoles indexes
CREATE NONCLUSTERED INDEX IX_UserRoles_UserID ON Security.UserRoles(UserID)
CREATE NONCLUSTERED INDEX IX_UserRoles_RoleID ON Security.UserRoles(RoleID)
CREATE NONCLUSTERED INDEX IX_UserRoles_IsActive ON Security.UserRoles(IsActive)

-- Permissions indexes
CREATE NONCLUSTERED INDEX IX_Permissions_ModuleName ON Security.Permissions(ModuleName)
CREATE NONCLUSTERED INDEX IX_Permissions_ResourceName ON Security.Permissions(ResourceName)

-- Audit indexes
CREATE NONCLUSTERED INDEX IX_AuditLog_TableName ON Audit.AuditLog(TableName)
CREATE NONCLUSTERED INDEX IX_AuditLog_UserID ON Audit.AuditLog(UserID)
CREATE NONCLUSTERED INDEX IX_AuditLog_Timestamp ON Audit.AuditLog(Timestamp)

CREATE NONCLUSTERED INDEX IX_SecurityLog_EventType ON Audit.SecurityLog(EventType)
CREATE NONCLUSTERED INDEX IX_SecurityLog_UserID ON Audit.SecurityLog(UserID)
CREATE NONCLUSTERED INDEX IX_SecurityLog_Timestamp ON Audit.SecurityLog(Timestamp)

CREATE NONCLUSTERED INDEX IX_ActivityLog_UserID ON Audit.ActivityLog(UserID)
CREATE NONCLUSTERED INDEX IX_ActivityLog_ModuleName ON Audit.ActivityLog(ModuleName)
CREATE NONCLUSTERED INDEX IX_ActivityLog_Timestamp ON Audit.ActivityLog(Timestamp)

GO
