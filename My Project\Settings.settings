﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="My" GeneratedClassName="MySettings" UseMySettingsClassName="true">
  <Profiles />
  <Settings>
    <Setting Name="server" Type="System.String" Scope="User">
      <Value Profile="(Default)">DESKTOP-H361157\SQLEXPRESS</Value>
    </Setting>
    <Setting Name="database" Type="System.String" Scope="User">
      <Value Profile="(Default)">El_Dawliya_International</Value>
    </Setting>
    <Setting Name="model" Type="System.String" Scope="User">
      <Value Profile="(Default)">SQL</Value>
    </Setting>
    <Setting Name="id" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Password" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="User_Name" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="User_Passowrd" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Entry_Number" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="color" Type="System.String" Scope="User">
      <Value Profile="(Default)">MacOS.ssk</Value>
    </Setting>
    <Setting Name="Company_Name" Type="System.String" Scope="User">
      <Value Profile="(Default)">الدولية انترناشونال للطباعة والمنتجات الصحية</Value>
    </Setting>
  </Settings>
</SettingsFile>