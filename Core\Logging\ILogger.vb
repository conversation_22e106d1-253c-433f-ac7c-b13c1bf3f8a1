''' <summary>
''' Logger interface for consistent logging across the application
''' Supports different log levels and structured logging
''' </summary>
Public Interface ILogger
    ''' <summary>
    ''' Log information message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Sub LogInfo(message As String)
    
    ''' <summary>
    ''' Log information message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Sub LogInfo(message As String, ParamArray args() As Object)
    
    ''' <summary>
    ''' Log warning message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Sub LogWarning(message As String)
    
    ''' <summary>
    ''' Log warning message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Sub LogWarning(message As String, ParamArray args() As Object)
    
    ''' <summary>
    ''' Log error message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Sub LogError(message As String)
    
    ''' <summary>
    ''' Log error message with exception
    ''' </summary>
    ''' <param name="message">Log message</param>
    ''' <param name="exception">Exception details</param>
    Sub LogError(message As String, exception As Exception)
    
    ''' <summary>
    ''' Log error message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Sub LogError(message As String, ParamArray args() As Object)
    
    ''' <summary>
    ''' Log debug message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Sub LogDebug(message As String)
    
    ''' <summary>
    ''' Log debug message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Sub LogDebug(message As String, ParamArray args() As Object)
    
    ''' <summary>
    ''' Log critical message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Sub LogCritical(message As String)
    
    ''' <summary>
    ''' Log critical message with exception
    ''' </summary>
    ''' <param name="message">Log message</param>
    ''' <param name="exception">Exception details</param>
    Sub LogCritical(message As String, exception As Exception)
    
    ''' <summary>
    ''' Check if log level is enabled
    ''' </summary>
    ''' <param name="level">Log level to check</param>
    ''' <returns>True if level is enabled</returns>
    Function IsEnabled(level As LogLevel) As Boolean
End Interface

''' <summary>
''' Log levels enumeration
''' </summary>
Public Enum LogLevel
    Debug = 0
    Info = 1
    Warning = 2
    [Error] = 3
    Critical = 4
End Enum

''' <summary>
''' Logger factory for creating logger instances
''' </summary>
Public Class LoggerFactory
    Private Shared _loggers As New Dictionary(Of Type, ILogger)()
    Private Shared _defaultLogger As ILogger
    Private Shared ReadOnly _lock As New Object()
    
    ''' <summary>
    ''' Initialize logger factory with default logger
    ''' </summary>
    ''' <param name="defaultLogger">Default logger implementation</param>
    Public Shared Sub Initialize(defaultLogger As ILogger)
        SyncLock _lock
            _defaultLogger = defaultLogger
        End SyncLock
    End Sub
    
    ''' <summary>
    ''' Get logger for specific type
    ''' </summary>
    ''' <param name="type">Type to get logger for</param>
    ''' <returns>Logger instance</returns>
    Public Shared Function GetLogger(type As Type) As ILogger
        SyncLock _lock
            If Not _loggers.ContainsKey(type) Then
                If _defaultLogger Is Nothing Then
                    _defaultLogger = New FileLogger()
                End If
                _loggers(type) = New TypedLogger(_defaultLogger, type)
            End If
            Return _loggers(type)
        End SyncLock
    End Function
    
    ''' <summary>
    ''' Get logger for specific type
    ''' </summary>
    ''' <typeparam name="T">Type to get logger for</typeparam>
    ''' <returns>Logger instance</returns>
    Public Shared Function GetLogger(Of T)() As ILogger
        Return GetLogger(GetType(T))
    End Function
End Class

''' <summary>
''' Typed logger wrapper that includes type information in log messages
''' </summary>
Public Class TypedLogger
    Implements ILogger
    
    Private ReadOnly _innerLogger As ILogger
    Private ReadOnly _typeName As String
    
    Public Sub New(innerLogger As ILogger, type As Type)
        _innerLogger = innerLogger
        _typeName = type.Name
    End Sub
    
    Public Sub LogInfo(message As String) Implements ILogger.LogInfo
        _innerLogger.LogInfo($"[{_typeName}] {message}")
    End Sub
    
    Public Sub LogInfo(message As String, ParamArray args() As Object) Implements ILogger.LogInfo
        _innerLogger.LogInfo($"[{_typeName}] {message}", args)
    End Sub
    
    Public Sub LogWarning(message As String) Implements ILogger.LogWarning
        _innerLogger.LogWarning($"[{_typeName}] {message}")
    End Sub
    
    Public Sub LogWarning(message As String, ParamArray args() As Object) Implements ILogger.LogWarning
        _innerLogger.LogWarning($"[{_typeName}] {message}", args)
    End Sub
    
    Public Sub LogError(message As String) Implements ILogger.LogError
        _innerLogger.LogError($"[{_typeName}] {message}")
    End Sub
    
    Public Sub LogError(message As String, exception As Exception) Implements ILogger.LogError
        _innerLogger.LogError($"[{_typeName}] {message}", exception)
    End Sub
    
    Public Sub LogError(message As String, ParamArray args() As Object) Implements ILogger.LogError
        _innerLogger.LogError($"[{_typeName}] {message}", args)
    End Sub
    
    Public Sub LogDebug(message As String) Implements ILogger.LogDebug
        _innerLogger.LogDebug($"[{_typeName}] {message}")
    End Sub
    
    Public Sub LogDebug(message As String, ParamArray args() As Object) Implements ILogger.LogDebug
        _innerLogger.LogDebug($"[{_typeName}] {message}", args)
    End Sub
    
    Public Sub LogCritical(message As String) Implements ILogger.LogCritical
        _innerLogger.LogCritical($"[{_typeName}] {message}")
    End Sub
    
    Public Sub LogCritical(message As String, exception As Exception) Implements ILogger.LogCritical
        _innerLogger.LogCritical($"[{_typeName}] {message}", exception)
    End Sub
    
    Public Function IsEnabled(level As LogLevel) As Boolean Implements ILogger.IsEnabled
        Return _innerLogger.IsEnabled(level)
    End Function
End Class
