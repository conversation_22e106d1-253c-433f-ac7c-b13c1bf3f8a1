﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="PictureBox2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGPC/xhBQAABUxJREFUaEPt
        2TtMG3ccwHGGdAhbpGQIjSA4CQmh4MfxMH5xGD+wfXe84mDTVkVthkpZMmRI0kZKApFolQGJDlnaplUU
        daqyd0iVqMnAkjEoLdj4/T7fgYXUSL/qZ/Wq6G98dzYGSxVfiQUJ/Lnjd3/+f7ul5bDDDmtIJc/0qS2v
        f170zT4WvP6o4PWD4PGDMH4JhPEZ4F0z0aJr6pHgnJ4v0dOnyJ9vSju+OY3IBFYEZnZNZGZB9M2C4L0M
        JL7onoGiaxqKTvyaAt4xCfzY5FrBzi2XaM/BXwywbOsWG1gS2SCIbADqwAM/NgG8nYM8zZV4ml0Eim0l
        X2df2mY/Zra4wGYj8IVRDgo0CwWagbyN2cxbvAz5eg0L2ODxrYm5n7c4hDcWXxjBC/BB3uaFvMXzk0DR
        x8nX31Oix39iiwuu7jveWr4AyJrHV0W95wTpqCvw+4+K3Nyrg8LnzOOQM7khN+x+CRT1AempOXEi+GsT
        8JAzuiA75PiB9NTUNhf4snl4J2QHHZAdsM+TLlXhQytyQaG5+DHIDNiLot5a+/MgssGHavBZ5xRERhlI
        2TnV+LjZBaFhJ6QQLY+HTL8dMgb6IemTbdv9yUmRDZSU8BnHJPz1+VXgn/8BkfsPIEYziviI2QWbd5eA
        f/YC3s5dgcSQQx5PjULaMFLK6M1tpLNqIhNcVsLjnUf8O0EEqcjiA4iN+BTxUu8EAd4Gv4DUkKMqPmOg
        Ia0fgbTWtkQ6dw1o+ojABrJyeJx5HJvi85f/YaQii99C1OpVxEvhX2KdouXxOhuk+qzrqpZVkQnQSnh8
        YHHmQzfvkJ5ykQW8iHFFPLZ+7SZEqVFZfFprxQuAZJ/ZSHorEpnLd5Tw0mqDM49js1uRe99AxOyWxYdv
        34cw3n0V+FSvBZI95muktyLRO/tUDV5abXDmcWx2a/PeUsPwqY/MkOwx/UJ6KxI8/jdq8dJqgzOPY6O2
        evCpHhMkLxpfk96KBK8/WQteWm1w5nFslKofPwzJ7qEk6a1I8FzaqRUvrTY48zg21dob3giJC8Yd0luR
        MD6zUw8eV5uwqfoDi5UvwDBSFx6/4ucHVVyAezpZDz5kcsripcK3FyGkt9WMT1wYgkTXoPII8a7p1/uF
        lwp/tQAhrbU2/PlBiHcNKD/ERdfU00bhyyNz+z757XKhW/dgo9esGp/oGoDE2X7lZZR3TN1oGJ6iyzOP
        Y7NboVt3Yb3HpA5/rh/iGoOKf2SOSVoNHrfEG9e/Jk3lyNUGZx7HZrf+vHodwt1GZfzZfkif1ulIb0W4
        YeLHJtbl8HgYwf08//sL0lOBl1YbnHkcG7L8b8/g7YVBRXxcQ4WhpeUI6d21gp1bkMPjSQoPI7iff387
        XQ0vrTY48zg2Un8LArzh5iD6L7wq/gwF8U79Aums2raZa8N3zKrhpWMgHkZwP49bYiW8tNrgzG/cuFO+
        84jfxLuvgI91GkqZju6TpFO2As18J4eXjoF4GMH9vNotMT6wOPM4NqruvMYA0dO6ZdKnmGj1nMjbfEU5
        vNwxsBpe1WrzHj7Wqc/E27rqe6euYPN91mQ8xNu1n5KumspZvT82Cx/r0H5PemoOhoeP5kzulweNj57W
        voIWFWdgNfEWy7HssGv1oPCxDt0q3957jHTsKfwAIjPofHIA+CfxNmr/PuzI9496MwOjkUbjo526SKJD
        5yVfb18CimpNU/RSo/D4X3Zf73q1Ur0WTVprXUlrLWu14mMaai3WqV9JtfdqyN/blHJ9Q6eSPeYrqYum
        x8luY7QS3x9KnKMeJc4Y5nMf9h38J5OHHfY/7R8Cuig7fJQgogAAAABJRU5ErkJggg==
</value>
  </data>
</root>