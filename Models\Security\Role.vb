Imports System.ComponentModel.DataAnnotations

''' <summary>
''' Role entity for role-based access control with hierarchical support
''' </summary>
Public Class Role
    ''' <summary>
    ''' Unique role identifier
    ''' </summary>
    Public Property RoleID As Integer

    ''' <summary>
    ''' Unique role name
    ''' </summary>
    <Required>
    <StringLength(50)>
    Public Property RoleName As String

    ''' <summary>
    ''' Role description
    ''' </summary>
    <StringLength(255)>
    Public Property RoleDescription As String

    ''' <summary>
    ''' Parent role ID for hierarchical roles
    ''' </summary>
    Public Property ParentRoleID As Integer?

    ''' <summary>
    ''' Whether this is a system-defined role
    ''' </summary>
    Public Property IsSystemRole As Boolean = False

    ''' <summary>
    ''' Whether the role is active
    ''' </summary>
    Public Property IsActive As Boolean = True

    ''' <summary>
    ''' Date when role was created
    ''' </summary>
    Public Property CreatedDate As DateTime = DateTime.Now

    ''' <summary>
    ''' ID of user who created this role
    ''' </summary>
    Public Property CreatedBy As Integer?

    ''' <summary>
    ''' Date when role was last modified
    ''' </summary>
    Public Property ModifiedDate As DateTime?

    ''' <summary>
    ''' ID of user who last modified this role
    ''' </summary>
    Public Property ModifiedBy As Integer?

    ''' <summary>
    ''' Navigation property to parent role
    ''' </summary>
    Public Property ParentRole As Role

    ''' <summary>
    ''' Navigation property to child roles
    ''' </summary>
    Public Property ChildRoles As List(Of Role) = New List(Of Role)()

    ''' <summary>
    ''' Navigation property to user roles
    ''' </summary>
    Public Property UserRoles As List(Of UserRole) = New List(Of UserRole)()

    ''' <summary>
    ''' Navigation property to role permissions
    ''' </summary>
    Public Property RolePermissions As List(Of RolePermission) = New List(Of RolePermission)()

    ''' <summary>
    ''' Get all permissions for this role (including inherited from parent roles)
    ''' </summary>
    ''' <returns>List of permissions</returns>
    Public Function GetAllPermissions() As List(Of Permission)
        Dim permissions As New HashSet(Of Permission)()

        ' Add direct permissions
        For Each rolePermission In RolePermissions
            permissions.Add(rolePermission.Permission)
        Next

        ' Add inherited permissions from parent roles
        If ParentRole IsNot Nothing Then
            For Each permission In ParentRole.GetAllPermissions()
                permissions.Add(permission)
            Next
        End If

        Return permissions.ToList()
    End Function

    ''' <summary>
    ''' Check if role has specific permission
    ''' </summary>
    ''' <param name="permissionName">Permission name to check</param>
    ''' <returns>True if role has permission</returns>
    Public Function HasPermission(permissionName As String) As Boolean
        Return GetAllPermissions().Any(Function(p) p.PermissionName.Equals(permissionName, StringComparison.OrdinalIgnoreCase))
    End Function

    ''' <summary>
    ''' Get all users assigned to this role
    ''' </summary>
    ''' <returns>List of users</returns>
    Public Function GetUsers() As List(Of User)
        Return UserRoles.Where(Function(ur) ur.IsActive AndAlso (Not ur.ExpiryDate.HasValue OrElse ur.ExpiryDate.Value > DateTime.Now)) _
                       .Select(Function(ur) ur.User) _
                       .ToList()
    End Function

    ''' <summary>
    ''' Get role hierarchy path
    ''' </summary>
    ''' <returns>Hierarchy path as string</returns>
    Public Function GetHierarchyPath() As String
        Dim path As New List(Of String)()
        Dim currentRole As Role = Me

        While currentRole IsNot Nothing
            path.Insert(0, currentRole.RoleName)
            currentRole = currentRole.ParentRole
        End While

        Return String.Join(" > ", path)
    End Function

    ''' <summary>
    ''' Check if this role is ancestor of another role
    ''' </summary>
    ''' <param name="role">Role to check</param>
    ''' <returns>True if this role is ancestor</returns>
    Public Function IsAncestorOf(role As Role) As Boolean
        Dim currentRole As Role = role.ParentRole

        While currentRole IsNot Nothing
            If currentRole.RoleID = Me.RoleID Then
                Return True
            End If
            currentRole = currentRole.ParentRole
        End While

        Return False
    End Function

    ''' <summary>
    ''' Check if this role is descendant of another role
    ''' </summary>
    ''' <param name="role">Role to check</param>
    ''' <returns>True if this role is descendant</returns>
    Public Function IsDescendantOf(role As Role) As Boolean
        Return role.IsAncestorOf(Me)
    End Function

    ''' <summary>
    ''' Validate role data
    ''' </summary>
    ''' <returns>List of validation errors</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)()

        If String.IsNullOrWhiteSpace(RoleName) Then
            errors.Add("Role name is required")
        End If

        ' Check for circular reference in hierarchy
        If ParentRoleID.HasValue AndAlso ParentRoleID.Value = RoleID Then
            errors.Add("Role cannot be its own parent")
        End If

        Return errors
    End Function

    ''' <summary>
    ''' String representation of role
    ''' </summary>
    ''' <returns>Role string representation</returns>
    Public Overrides Function ToString() As String
        Return RoleName
    End Function
End Class

''' <summary>
''' Permission entity for granular access control
''' </summary>
Public Class Permission
    ''' <summary>
    ''' Unique permission identifier
    ''' </summary>
    Public Property PermissionID As Integer

    ''' <summary>
    ''' Unique permission name
    ''' </summary>
    <Required>
    <StringLength(100)>
    Public Property PermissionName As String

    ''' <summary>
    ''' Permission description
    ''' </summary>
    <StringLength(255)>
    Public Property PermissionDescription As String

    ''' <summary>
    ''' Module name this permission belongs to
    ''' </summary>
    <Required>
    <StringLength(50)>
    Public Property ModuleName As String

    ''' <summary>
    ''' Resource name this permission applies to
    ''' </summary>
    <StringLength(100)>
    Public Property ResourceName As String

    ''' <summary>
    ''' Action name this permission allows
    ''' </summary>
    <Required>
    <StringLength(50)>
    Public Property ActionName As String

    ''' <summary>
    ''' Whether this is a system-defined permission
    ''' </summary>
    Public Property IsSystemPermission As Boolean = False

    ''' <summary>
    ''' Date when permission was created
    ''' </summary>
    Public Property CreatedDate As DateTime = DateTime.Now

    ''' <summary>
    ''' ID of user who created this permission
    ''' </summary>
    Public Property CreatedBy As Integer?

    ''' <summary>
    ''' Navigation property to role permissions
    ''' </summary>
    Public Property RolePermissions As List(Of RolePermission) = New List(Of RolePermission)()

    ''' <summary>
    ''' Navigation property to user permissions
    ''' </summary>
    Public Property UserPermissions As List(Of UserPermission) = New List(Of UserPermission)()

    ''' <summary>
    ''' Get full permission identifier
    ''' </summary>
    ''' <returns>Full permission identifier</returns>
    Public Function GetFullIdentifier() As String
        If String.IsNullOrWhiteSpace(ResourceName) Then
            Return $"{ModuleName}.{ActionName}"
        Else
            Return $"{ModuleName}.{ResourceName}.{ActionName}"
        End If
    End Function

    ''' <summary>
    ''' Validate permission data
    ''' </summary>
    ''' <returns>List of validation errors</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)()

        If String.IsNullOrWhiteSpace(PermissionName) Then
            errors.Add("Permission name is required")
        End If

        If String.IsNullOrWhiteSpace(ModuleName) Then
            errors.Add("Module name is required")
        End If

        If String.IsNullOrWhiteSpace(ActionName) Then
            errors.Add("Action name is required")
        End If

        Return errors
    End Function

    ''' <summary>
    ''' String representation of permission
    ''' </summary>
    ''' <returns>Permission string representation</returns>
    Public Overrides Function ToString() As String
        Return PermissionName
    End Function
End Class
