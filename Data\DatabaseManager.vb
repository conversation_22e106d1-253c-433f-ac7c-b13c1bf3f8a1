Imports System.Data.SqlClient
Imports System.Configuration
Imports System.Threading.Tasks
Imports System.Data.Common

''' <summary>
''' Enhanced Database Manager with connection pooling, async support, and proper resource management
''' Implements Singleton pattern with thread safety and SOLID principles
''' </summary>
Public NotInheritable Class DatabaseManager
    Implements IDisposable

    Private Shared ReadOnly _lock As New Object()
    Private Shared _instance As DatabaseManager
    Private ReadOnly _connectionString As String
    Private ReadOnly _logger As ILogger
    Private _disposed As Boolean = False

    ''' <summary>
    ''' Private constructor to enforce singleton pattern
    ''' </summary>
    Private Sub New()
        _connectionString = GetConnectionString()
        _logger = LoggerFactory.GetLogger(GetType(DatabaseManager))

        ' Configure connection pooling
        ConfigureConnectionPooling()
    End Sub

    ''' <summary>
    ''' Thread-safe singleton instance
    ''' </summary>
    Public Shared ReadOnly Property Instance As DatabaseManager
        Get
            If _instance Is Nothing Then
                SyncLock _lock
                    If _instance Is Nothing Then
                        _instance = New DatabaseManager()
                    End If
                End SyncLock
            End If
            Return _instance
        End Get
    End Property

    ''' <summary>
    ''' Get a new SQL connection with proper configuration
    ''' </summary>
    ''' <returns>Configured SqlConnection</returns>
    Public Function GetConnection() As SqlConnection
        ThrowIfDisposed()

        Dim connection As New SqlConnection(_connectionString)

        ' Configure connection settings
        connection.StatisticsEnabled = True

        Return connection
    End Function

    ''' <summary>
    ''' Get a new SQL connection asynchronously
    ''' </summary>
    ''' <returns>Task containing configured SqlConnection</returns>
    Public Async Function GetConnectionAsync() As Task(Of SqlConnection)
        ThrowIfDisposed()

        Dim connection As New SqlConnection(_connectionString)
        connection.StatisticsEnabled = True

        Await connection.OpenAsync()
        Return connection
    End Function

    ''' <summary>
    ''' Execute a query and return a single result
    ''' </summary>
    ''' <typeparam name="T">Return type</typeparam>
    ''' <param name="query">SQL query</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <param name="dataReader">Data reader function</param>
    ''' <returns>Query result</returns>
    Public Function ExecuteQuery(Of T)(query As String, parameterSetup As Action(Of SqlCommand), dataReader As Func(Of SqlDataReader, T)) As T
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using command As New SqlCommand(query, connection)
                    If parameterSetup IsNot Nothing Then
                        parameterSetup(command)
                    End If

                    connection.Open()
                    Using reader As SqlDataReader = command.ExecuteReader()
                        Return dataReader(reader)
                    End Using
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing query: {query}", ex)
            Throw New DatabaseException("Error executing query", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute a query asynchronously and return a single result
    ''' </summary>
    ''' <typeparam name="T">Return type</typeparam>
    ''' <param name="query">SQL query</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <param name="dataReader">Data reader function</param>
    ''' <returns>Task containing query result</returns>
    Public Async Function ExecuteQueryAsync(Of T)(query As String, parameterSetup As Action(Of SqlCommand), dataReader As Func(Of SqlDataReader, Task(Of T))) As Task(Of T)
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using command As New SqlCommand(query, connection)
                    If parameterSetup IsNot Nothing Then
                        parameterSetup(command)
                    End If

                    Await connection.OpenAsync()
                    Using reader As SqlDataReader = Await command.ExecuteReaderAsync()
                        Return Await dataReader(reader)
                    End Using
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing async query: {query}", ex)
            Throw New DatabaseException("Error executing async query", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute a non-query command (INSERT, UPDATE, DELETE)
    ''' </summary>
    ''' <param name="command">SQL command</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <returns>Number of affected rows</returns>
    Public Function ExecuteNonQuery(command As String, parameterSetup As Action(Of SqlCommand)) As Integer
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using cmd As New SqlCommand(command, connection)
                    If parameterSetup IsNot Nothing Then
                        parameterSetup(cmd)
                    End If

                    connection.Open()
                    Return cmd.ExecuteNonQuery()
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing non-query: {command}", ex)
            Throw New DatabaseException("Error executing non-query", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute a non-query command asynchronously
    ''' </summary>
    ''' <param name="command">SQL command</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <returns>Task containing number of affected rows</returns>
    Public Async Function ExecuteNonQueryAsync(command As String, parameterSetup As Action(Of SqlCommand)) As Task(Of Integer)
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using cmd As New SqlCommand(command, connection)
                    If parameterSetup IsNot Nothing Then
                        parameterSetup(cmd)
                    End If

                    Await connection.OpenAsync()
                    Return Await cmd.ExecuteNonQueryAsync()
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing async non-query: {command}", ex)
            Throw New DatabaseException("Error executing async non-query", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute a scalar command and return single value
    ''' </summary>
    ''' <typeparam name="T">Return type</typeparam>
    ''' <param name="command">SQL command</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <returns>Scalar result</returns>
    Public Function ExecuteScalar(Of T)(command As String, parameterSetup As Action(Of SqlCommand)) As T
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using cmd As New SqlCommand(command, connection)
                    If parameterSetup IsNot Nothing Then
                        parameterSetup(cmd)
                    End If

                    connection.Open()
                    Dim result = cmd.ExecuteScalar()

                    If result Is Nothing OrElse result Is DBNull.Value Then
                        Return Nothing
                    End If

                    Return DirectCast(result, T)
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing scalar: {command}", ex)
            Throw New DatabaseException("Error executing scalar", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute a scalar command asynchronously
    ''' </summary>
    ''' <typeparam name="T">Return type</typeparam>
    ''' <param name="command">SQL command</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <returns>Task containing scalar result</returns>
    Public Async Function ExecuteScalarAsync(Of T)(command As String, parameterSetup As Action(Of SqlCommand)) As Task(Of T)
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using cmd As New SqlCommand(command, connection)
                    If parameterSetup IsNot Nothing Then
                        parameterSetup(cmd)
                    End If

                    Await connection.OpenAsync()
                    Dim result = Await cmd.ExecuteScalarAsync()

                    If result Is Nothing OrElse result Is DBNull.Value Then
                        Return Nothing
                    End If

                    Return DirectCast(result, T)
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing async scalar: {command}", ex)
            Throw New DatabaseException("Error executing async scalar", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute stored procedure
    ''' </summary>
    ''' <param name="procedureName">Stored procedure name</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <returns>Number of affected rows</returns>
    Public Function ExecuteStoredProcedure(procedureName As String, parameterSetup As Action(Of SqlCommand)) As Integer
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using cmd As New SqlCommand(procedureName, connection)
                    cmd.CommandType = CommandType.StoredProcedure

                    If parameterSetup IsNot Nothing Then
                        parameterSetup(cmd)
                    End If

                    connection.Open()
                    Return cmd.ExecuteNonQuery()
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing stored procedure: {procedureName}", ex)
            Throw New DatabaseException("Error executing stored procedure", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute stored procedure asynchronously
    ''' </summary>
    ''' <param name="procedureName">Stored procedure name</param>
    ''' <param name="parameterSetup">Parameter setup action</param>
    ''' <returns>Task containing number of affected rows</returns>
    Public Async Function ExecuteStoredProcedureAsync(procedureName As String, parameterSetup As Action(Of SqlCommand)) As Task(Of Integer)
        ThrowIfDisposed()

        Try
            Using connection As SqlConnection = GetConnection()
                Using cmd As New SqlCommand(procedureName, connection)
                    cmd.CommandType = CommandType.StoredProcedure

                    If parameterSetup IsNot Nothing Then
                        parameterSetup(cmd)
                    End If

                    Await connection.OpenAsync()
                    Return Await cmd.ExecuteNonQueryAsync()
                End Using
            End Using
        Catch ex As Exception
            _logger.LogError($"Error executing async stored procedure: {procedureName}", ex)
            Throw New DatabaseException("Error executing async stored procedure", ex)
        End Try
    End Function

    ''' <summary>
    ''' Execute multiple commands in a transaction
    ''' </summary>
    ''' <param name="commands">List of commands to execute</param>
    ''' <returns>True if all commands succeeded</returns>
    Public Function ExecuteTransaction(commands As List(Of TransactionCommand)) As Boolean
        ThrowIfDisposed()

        Using connection As SqlConnection = GetConnection()
            connection.Open()
            Using transaction As SqlTransaction = connection.BeginTransaction()
                Try
                    For Each cmd In commands
                        Using sqlCmd As New SqlCommand(cmd.CommandText, connection, transaction)
                            sqlCmd.CommandType = cmd.CommandType

                            If cmd.ParameterSetup IsNot Nothing Then
                                cmd.ParameterSetup(sqlCmd)
                            End If

                            sqlCmd.ExecuteNonQuery()
                        End Using
                    Next

                    transaction.Commit()
                    Return True

                Catch ex As Exception
                    transaction.Rollback()
                    _logger.LogError("Error executing transaction", ex)
                    Throw New DatabaseException("Error executing transaction", ex)
                End Try
            End Using
        End Using
    End Function

    ''' <summary>
    ''' Execute multiple commands in a transaction asynchronously
    ''' </summary>
    ''' <param name="commands">List of commands to execute</param>
    ''' <returns>Task containing true if all commands succeeded</returns>
    Public Async Function ExecuteTransactionAsync(commands As List(Of TransactionCommand)) As Task(Of Boolean)
        ThrowIfDisposed()

        Using connection As SqlConnection = GetConnection()
            Await connection.OpenAsync()
            Using transaction As SqlTransaction = connection.BeginTransaction()
                Try
                    For Each cmd In commands
                        Using sqlCmd As New SqlCommand(cmd.CommandText, connection, transaction)
                            sqlCmd.CommandType = cmd.CommandType

                            If cmd.ParameterSetup IsNot Nothing Then
                                cmd.ParameterSetup(sqlCmd)
                            End If

                            Await sqlCmd.ExecuteNonQueryAsync()
                        End Using
                    Next

                    transaction.Commit()
                    Return True

                Catch ex As Exception
                    transaction.Rollback()
                    _logger.LogError("Error executing async transaction", ex)
                    Throw New DatabaseException("Error executing async transaction", ex)
                End Try
            End Using
        End Using
    End Function

    ''' <summary>
    ''' Test database connection
    ''' </summary>
    ''' <returns>True if connection is successful</returns>
    Public Function TestConnection() As Boolean
        Try
            Using connection As SqlConnection = GetConnection()
                connection.Open()
                Return connection.State = ConnectionState.Open
            End Using
        Catch ex As Exception
            _logger.LogError("Database connection test failed", ex)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Test database connection asynchronously
    ''' </summary>
    ''' <returns>Task containing true if connection is successful</returns>
    Public Async Function TestConnectionAsync() As Task(Of Boolean)
        Try
            Using connection As SqlConnection = GetConnection()
                Await connection.OpenAsync()
                Return connection.State = ConnectionState.Open
            End Using
        Catch ex As Exception
            _logger.LogError("Async database connection test failed", ex)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' Configure connection pooling settings
    ''' </summary>
    Private Sub ConfigureConnectionPooling()
        ' Connection pooling is configured via connection string
        ' Default settings are usually sufficient, but can be customized here
        _logger.LogInfo("Database connection pooling configured")
    End Sub

    ''' <summary>
    ''' Check if object is disposed
    ''' </summary>
    Private Sub ThrowIfDisposed()
        If _disposed Then
            Throw New ObjectDisposedException(GetType(DatabaseManager).Name)
        End If
    End Sub

    ''' <summary>
    ''' Dispose resources
    ''' </summary>
    Public Sub Dispose() Implements IDisposable.Dispose
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    ''' <summary>
    ''' Protected dispose method
    ''' </summary>
    ''' <param name="disposing">True if disposing managed resources</param>
    Protected Sub Dispose(disposing As Boolean)
        If Not _disposed Then
            If disposing Then
                ' Dispose managed resources
                _logger?.LogInfo("DatabaseManager disposed")
            End If
            _disposed = True
        End If
    End Sub

    ''' <summary>
    ''' Finalizer
    ''' </summary>
    Protected Overrides Sub Finalize()
        Dispose(False)
        MyBase.Finalize()
    End Sub
End Class

''' <summary>
''' Transaction command container
''' </summary>
Public Class TransactionCommand
    Public Property CommandText As String
    Public Property CommandType As CommandType = CommandType.Text
    Public Property ParameterSetup As Action(Of SqlCommand)

    Public Sub New(commandText As String, Optional parameterSetup As Action(Of SqlCommand) = Nothing)
        Me.CommandText = commandText
        Me.ParameterSetup = parameterSetup
    End Sub

    Public Sub New(commandText As String, commandType As CommandType, Optional parameterSetup As Action(Of SqlCommand) = Nothing)
        Me.CommandText = commandText
        Me.CommandType = commandType
        Me.ParameterSetup = parameterSetup
    End Sub
End Class

''' <summary>
''' Custom database exception
''' </summary>
Public Class DatabaseException
    Inherits Exception

    Public Sub New()
        MyBase.New()
    End Sub

    Public Sub New(message As String)
        MyBase.New(message)
    End Sub

    Public Sub New(message As String, innerException As Exception)
        MyBase.New(message, innerException)
    End Sub
End Class

    Public Function ExecuteNonQuery(query As String, parameterSetup As Action(Of SqlCommand)) As Integer
        Using connection As SqlConnection = GetConnection()
            Using command As New SqlCommand(query, connection)
                If parameterSetup IsNot Nothing Then
                    parameterSetup(command)
                End If

                connection.Open()
                Return command.ExecuteNonQuery()
            End Using
        End Using
    End Function

    Public Function ExecuteScalar(Of T)(query As String, parameterSetup As Action(Of SqlCommand)) As T
        Using connection As SqlConnection = GetConnection()
            Using command As New SqlCommand(query, connection)
                If parameterSetup IsNot Nothing Then
                    parameterSetup(command)
                End If

                connection.Open()
                Dim result = command.ExecuteScalar()
                If result Is DBNull.Value Then
                    Return Nothing
                End If
                Return DirectCast(result, T)
            End Using
        End Using
    End Function

    Public Function BeginTransaction() As (Connection As SqlConnection, Transaction As SqlTransaction)
        Dim connection = GetConnection()
        connection.Open()
        Dim transaction = connection.BeginTransaction()
        Return (connection, transaction)
    End Function
End Class
