﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class Frm_Quiry_ForEmp
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Frm_Quiry_ForEmp))
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.PictureBoxClose = New System.Windows.Forms.PictureBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.dgv_Data = New System.Windows.Forms.DataGridView()
        Me.Label6 = New System.Windows.Forms.Label()
        Me.Combo_Dept = New System.Windows.Forms.ComboBox()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.Combo_Machine = New System.Windows.Forms.ComboBox()
        Me.Label3 = New System.Windows.Forms.Label()
        Me.Label4 = New System.Windows.Forms.Label()
        Me.Txt_StartDate = New System.Windows.Forms.DateTimePicker()
        Me.Txt_EndDate = New System.Windows.Forms.DateTimePicker()
        Me.Btn_Search = New System.Windows.Forms.Button()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.Label27 = New System.Windows.Forms.Label()
        Me.Combo_Shift = New System.Windows.Forms.ComboBox()
        Me.Label20 = New System.Windows.Forms.Label()
        Me.Lbl_Total_Time_All_Proplem = New System.Windows.Forms.Label()
        Me.Lbl_Count_Proplem = New System.Windows.Forms.Label()
        Me.Label15 = New System.Windows.Forms.Label()
        Me.Check_AllMachine = New System.Windows.Forms.CheckBox()
        Me.Label5 = New System.Windows.Forms.Label()
        Me.Txt_StopMachineTime = New System.Windows.Forms.TextBox()
        Me.Txt_Who_identified_problem = New System.Windows.Forms.TextBox()
        Me.Label7 = New System.Windows.Forms.Label()
        Me.Txt_Who_fixed_problem = New System.Windows.Forms.TextBox()
        Me.Label8 = New System.Windows.Forms.Label()
        Me.Txt_Technician_name = New System.Windows.Forms.TextBox()
        Me.Label9 = New System.Windows.Forms.Label()
        Me.Txt_Unity_problem = New System.Windows.Forms.TextBox()
        Me.Label10 = New System.Windows.Forms.Label()
        Me.Txt_Time_fix_problem = New System.Windows.Forms.TextBox()
        Me.Label11 = New System.Windows.Forms.Label()
        Me.Txt_Technician_attendance_time = New System.Windows.Forms.TextBox()
        Me.Label12 = New System.Windows.Forms.Label()
        Me.Txt_Time_identify_problem = New System.Windows.Forms.TextBox()
        Me.Label13 = New System.Windows.Forms.Label()
        Me.Txt_Time_write_notes = New System.Windows.Forms.TextBox()
        Me.Label14 = New System.Windows.Forms.Label()
        Me.Txt_Notes = New System.Windows.Forms.TextBox()
        Me.Label16 = New System.Windows.Forms.Label()
        Me.Txt_Part_problem = New System.Windows.Forms.TextBox()
        Me.Label17 = New System.Windows.Forms.Label()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.Label21 = New System.Windows.Forms.Label()
        Me.Txt_Proplem_Code = New System.Windows.Forms.TextBox()
        Me.Label22 = New System.Windows.Forms.Label()
        Me.Txt_Who_Note_writer = New System.Windows.Forms.TextBox()
        Me.Txt_Department_Proplem = New System.Windows.Forms.TextBox()
        Me.Label19 = New System.Windows.Forms.Label()
        Me.Txt_Machine_running_time = New System.Windows.Forms.TextBox()
        Me.Txt_Problem_status = New System.Windows.Forms.TextBox()
        Me.Label23 = New System.Windows.Forms.Label()
        Me.Lbl_Who_Note_writer = New System.Windows.Forms.Label()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Label25 = New System.Windows.Forms.Label()
        Me.Txt_Total_time_problem = New System.Windows.Forms.TextBox()
        Me.Label26 = New System.Windows.Forms.Label()
        Me.Txt_Total_time_solve_problem = New System.Windows.Forms.TextBox()
        Me.Txt_Total_response_time = New System.Windows.Forms.TextBox()
        Me.Label24 = New System.Windows.Forms.Label()
        Me.Label18 = New System.Windows.Forms.Label()
        Me.Txt_User = New System.Windows.Forms.TextBox()
        Me.Usernamelbl = New System.Windows.Forms.Label()
        Me.Txt_UserID = New System.Windows.Forms.TextBox()
        Me.Panel1.SuspendLayout()
        CType(Me.PictureBoxClose, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Panel2.SuspendLayout()
        CType(Me.dgv_Data, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.TableLayoutPanel2.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel3.SuspendLayout()
        Me.SuspendLayout()
        '
        'Panel1
        '
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(1, Byte), Integer), CType(CType(17, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.Panel1.Controls.Add(Me.PictureBoxClose)
        Me.Panel1.Controls.Add(Me.Label1)
        Me.Panel1.Dock = System.Windows.Forms.DockStyle.Top
        Me.Panel1.Location = New System.Drawing.Point(0, 0)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1434, 51)
        Me.Panel1.TabIndex = 84
        '
        'PictureBoxClose
        '
        Me.PictureBoxClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBoxClose.BackColor = System.Drawing.Color.Transparent
        Me.PictureBoxClose.Image = CType(resources.GetObject("PictureBoxClose.Image"), System.Drawing.Image)
        Me.PictureBoxClose.Location = New System.Drawing.Point(1378, 6)
        Me.PictureBoxClose.Name = "PictureBoxClose"
        Me.PictureBoxClose.Size = New System.Drawing.Size(44, 35)
        Me.PictureBoxClose.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.PictureBoxClose.TabIndex = 85
        Me.PictureBoxClose.TabStop = False
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(119, Byte), Integer))
        Me.Label1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label1.Font = New System.Drawing.Font("Cairo", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.White
        Me.Label1.Location = New System.Drawing.Point(0, 0)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(1434, 51)
        Me.Label1.TabIndex = 7
        Me.Label1.Text = "شاشة الإستعلام عن الأعطال"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Panel2
        '
        Me.Panel2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Panel2.Controls.Add(Me.dgv_Data)
        Me.Panel2.Location = New System.Drawing.Point(13, 425)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1414, 310)
        Me.Panel2.TabIndex = 91
        '
        'dgv_Data
        '
        Me.dgv_Data.AllowUserToAddRows = False
        Me.dgv_Data.AllowUserToDeleteRows = False
        DataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle1.Font = New System.Drawing.Font("Calibri", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dgv_Data.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.dgv_Data.BackgroundColor = System.Drawing.Color.White
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(0, Byte), Integer))
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Hacen Saudi Arabia", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(0, Byte), Integer))
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_Data.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.dgv_Data.ColumnHeadersHeight = 50
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle3.BackColor = System.Drawing.Color.Linen
        DataGridViewCellStyle3.Font = New System.Drawing.Font("Hacen Saudi Arabia", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(0, Byte), Integer))
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.dgv_Data.DefaultCellStyle = DataGridViewCellStyle3
        Me.dgv_Data.Dock = System.Windows.Forms.DockStyle.Fill
        Me.dgv_Data.EnableHeadersVisualStyles = False
        Me.dgv_Data.Location = New System.Drawing.Point(0, 0)
        Me.dgv_Data.Margin = New System.Windows.Forms.Padding(5, 7, 5, 7)
        Me.dgv_Data.Name = "dgv_Data"
        Me.dgv_Data.ReadOnly = True
        DataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle4.Font = New System.Drawing.Font("Hacen Saudi Arabia", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle4.ForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Black
        DataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.dgv_Data.RowHeadersDefaultCellStyle = DataGridViewCellStyle4
        Me.dgv_Data.RowHeadersVisible = False
        Me.dgv_Data.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.AutoSizeToDisplayedHeaders
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Calibri", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.dgv_Data.RowsDefaultCellStyle = DataGridViewCellStyle5
        Me.dgv_Data.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.dgv_Data.Size = New System.Drawing.Size(1412, 308)
        Me.dgv_Data.TabIndex = 114
        '
        'Label6
        '
        Me.Label6.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label6.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label6.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label6.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label6.ForeColor = System.Drawing.Color.Black
        Me.Label6.Location = New System.Drawing.Point(144, 23)
        Me.Label6.Name = "Label6"
        Me.Label6.Size = New System.Drawing.Size(149, 37)
        Me.Label6.TabIndex = 101
        Me.Label6.Text = "القسم"
        Me.Label6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Combo_Dept
        '
        Me.Combo_Dept.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Combo_Dept.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.Suggest
        Me.Combo_Dept.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems
        Me.Combo_Dept.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Combo_Dept.Location = New System.Drawing.Point(7, 23)
        Me.Combo_Dept.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Combo_Dept.Name = "Combo_Dept"
        Me.Combo_Dept.Size = New System.Drawing.Size(131, 32)
        Me.Combo_Dept.TabIndex = 102
        '
        'Label2
        '
        Me.Label2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label2.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label2.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label2.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label2.ForeColor = System.Drawing.Color.Black
        Me.Label2.Location = New System.Drawing.Point(144, 70)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(149, 34)
        Me.Label2.TabIndex = 103
        Me.Label2.Text = "الماكينة"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Combo_Machine
        '
        Me.Combo_Machine.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Combo_Machine.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Combo_Machine.Location = New System.Drawing.Point(7, 70)
        Me.Combo_Machine.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Combo_Machine.Name = "Combo_Machine"
        Me.Combo_Machine.Size = New System.Drawing.Size(131, 32)
        Me.Combo_Machine.TabIndex = 104
        '
        'Label3
        '
        Me.Label3.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label3.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label3.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label3.ForeColor = System.Drawing.Color.Black
        Me.Label3.Location = New System.Drawing.Point(207, 361)
        Me.Label3.Name = "Label3"
        Me.Label3.Size = New System.Drawing.Size(102, 24)
        Me.Label3.TabIndex = 106
        Me.Label3.Text = "التاريخ إلى"
        Me.Label3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label3.Visible = False
        '
        'Label4
        '
        Me.Label4.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label4.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label4.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label4.ForeColor = System.Drawing.Color.Black
        Me.Label4.Location = New System.Drawing.Point(207, 327)
        Me.Label4.Name = "Label4"
        Me.Label4.Size = New System.Drawing.Size(102, 23)
        Me.Label4.TabIndex = 105
        Me.Label4.Text = "التاريخ من"
        Me.Label4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label4.Visible = False
        '
        'Txt_StartDate
        '
        Me.Txt_StartDate.CustomFormat = "dd-MM-yyyy hh:mm tt"
        Me.Txt_StartDate.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_StartDate.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.Txt_StartDate.Location = New System.Drawing.Point(12, 327)
        Me.Txt_StartDate.Name = "Txt_StartDate"
        Me.Txt_StartDate.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.Txt_StartDate.Size = New System.Drawing.Size(189, 28)
        Me.Txt_StartDate.TabIndex = 107
        Me.Txt_StartDate.Visible = False
        '
        'Txt_EndDate
        '
        Me.Txt_EndDate.CustomFormat = "dd-MM-yyyy hh:mm tt"
        Me.Txt_EndDate.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_EndDate.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.Txt_EndDate.Location = New System.Drawing.Point(12, 361)
        Me.Txt_EndDate.Name = "Txt_EndDate"
        Me.Txt_EndDate.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.Txt_EndDate.Size = New System.Drawing.Size(189, 28)
        Me.Txt_EndDate.TabIndex = 108
        Me.Txt_EndDate.Visible = False
        '
        'Btn_Search
        '
        Me.Btn_Search.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Btn_Search.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Btn_Search.FlatAppearance.BorderSize = 0
        Me.Btn_Search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.DarkOrange
        Me.Btn_Search.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.Btn_Search.Font = New System.Drawing.Font("Hacen Saudi Arabia", 13.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Btn_Search.ForeColor = System.Drawing.Color.Black
        Me.Btn_Search.Image = Global.El_Dawliya_International_System.My.Resources.Resources.Binoculars
        Me.Btn_Search.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.Btn_Search.Location = New System.Drawing.Point(7, 150)
        Me.Btn_Search.Name = "Btn_Search"
        Me.Btn_Search.Size = New System.Drawing.Size(286, 41)
        Me.Btn_Search.TabIndex = 109
        Me.Btn_Search.Text = "بـــــحــــــــــث"
        Me.Btn_Search.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Btn_Search.UseVisualStyleBackColor = False
        '
        'GroupBox1
        '
        Me.GroupBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(119, Byte), Integer))
        Me.GroupBox1.Controls.Add(Me.Label27)
        Me.GroupBox1.Controls.Add(Me.Combo_Shift)
        Me.GroupBox1.Controls.Add(Me.Label6)
        Me.GroupBox1.Controls.Add(Me.Btn_Search)
        Me.GroupBox1.Controls.Add(Me.Combo_Dept)
        Me.GroupBox1.Controls.Add(Me.Label20)
        Me.GroupBox1.Controls.Add(Me.Lbl_Total_Time_All_Proplem)
        Me.GroupBox1.Controls.Add(Me.Label2)
        Me.GroupBox1.Controls.Add(Me.Combo_Machine)
        Me.GroupBox1.Controls.Add(Me.Lbl_Count_Proplem)
        Me.GroupBox1.Controls.Add(Me.Label15)
        Me.GroupBox1.FlatStyle = System.Windows.Forms.FlatStyle.Popup
        Me.GroupBox1.Location = New System.Drawing.Point(13, 57)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupBox1.Size = New System.Drawing.Size(299, 264)
        Me.GroupBox1.TabIndex = 111
        Me.GroupBox1.TabStop = False
        '
        'Label27
        '
        Me.Label27.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label27.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label27.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label27.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label27.ForeColor = System.Drawing.Color.Black
        Me.Label27.Location = New System.Drawing.Point(144, 109)
        Me.Label27.Name = "Label27"
        Me.Label27.Size = New System.Drawing.Size(149, 34)
        Me.Label27.TabIndex = 140
        Me.Label27.Text = "الوردية"
        Me.Label27.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Combo_Shift
        '
        Me.Combo_Shift.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Combo_Shift.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Combo_Shift.Items.AddRange(New Object() {"الاولى", "الثانية", "الثالثة"})
        Me.Combo_Shift.Location = New System.Drawing.Point(7, 109)
        Me.Combo_Shift.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Combo_Shift.Name = "Combo_Shift"
        Me.Combo_Shift.Size = New System.Drawing.Size(131, 32)
        Me.Combo_Shift.TabIndex = 141
        '
        'Label20
        '
        Me.Label20.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label20.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label20.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label20.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label20.ForeColor = System.Drawing.Color.Black
        Me.Label20.Location = New System.Drawing.Point(144, 232)
        Me.Label20.Name = "Label20"
        Me.Label20.Size = New System.Drawing.Size(149, 29)
        Me.Label20.TabIndex = 138
        Me.Label20.Text = "مجموع وقت المشاكل"
        Me.Label20.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Lbl_Total_Time_All_Proplem
        '
        Me.Lbl_Total_Time_All_Proplem.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Lbl_Total_Time_All_Proplem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Lbl_Total_Time_All_Proplem.ForeColor = System.Drawing.Color.Black
        Me.Lbl_Total_Time_All_Proplem.Location = New System.Drawing.Point(6, 232)
        Me.Lbl_Total_Time_All_Proplem.Name = "Lbl_Total_Time_All_Proplem"
        Me.Lbl_Total_Time_All_Proplem.Size = New System.Drawing.Size(132, 29)
        Me.Lbl_Total_Time_All_Proplem.TabIndex = 139
        Me.Lbl_Total_Time_All_Proplem.Text = "0"
        Me.Lbl_Total_Time_All_Proplem.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Lbl_Count_Proplem
        '
        Me.Lbl_Count_Proplem.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Lbl_Count_Proplem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Lbl_Count_Proplem.ForeColor = System.Drawing.Color.Black
        Me.Lbl_Count_Proplem.Location = New System.Drawing.Point(7, 194)
        Me.Lbl_Count_Proplem.Name = "Lbl_Count_Proplem"
        Me.Lbl_Count_Proplem.Size = New System.Drawing.Size(131, 29)
        Me.Lbl_Count_Proplem.TabIndex = 137
        Me.Lbl_Count_Proplem.Text = "0"
        Me.Lbl_Count_Proplem.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Label15
        '
        Me.Label15.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label15.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label15.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label15.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label15.ForeColor = System.Drawing.Color.Black
        Me.Label15.Location = New System.Drawing.Point(144, 194)
        Me.Label15.Name = "Label15"
        Me.Label15.Size = New System.Drawing.Size(149, 29)
        Me.Label15.TabIndex = 132
        Me.Label15.Text = "عدد المشاكل"
        Me.Label15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Check_AllMachine
        '
        Me.Check_AllMachine.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Check_AllMachine.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Check_AllMachine.Location = New System.Drawing.Point(12, 388)
        Me.Check_AllMachine.Name = "Check_AllMachine"
        Me.Check_AllMachine.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Check_AllMachine.Size = New System.Drawing.Size(297, 23)
        Me.Check_AllMachine.TabIndex = 110
        Me.Check_AllMachine.Text = "كل الماكينات"
        Me.Check_AllMachine.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.Check_AllMachine.UseVisualStyleBackColor = True
        Me.Check_AllMachine.Visible = False
        '
        'Label5
        '
        Me.Label5.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label5.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label5.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label5.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label5.ForeColor = System.Drawing.Color.Black
        Me.Label5.Location = New System.Drawing.Point(816, 49)
        Me.Label5.Name = "Label5"
        Me.Label5.Size = New System.Drawing.Size(154, 49)
        Me.Label5.TabIndex = 112
        Me.Label5.Text = "وقت ايقاف الماكينة"
        Me.Label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_StopMachineTime
        '
        Me.Txt_StopMachineTime.BackColor = System.Drawing.Color.Red
        Me.Txt_StopMachineTime.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_StopMachineTime.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_StopMachineTime.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_StopMachineTime.ForeColor = System.Drawing.Color.Black
        Me.Txt_StopMachineTime.Location = New System.Drawing.Point(426, 53)
        Me.Txt_StopMachineTime.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_StopMachineTime.Multiline = True
        Me.Txt_StopMachineTime.Name = "Txt_StopMachineTime"
        Me.Txt_StopMachineTime.ReadOnly = True
        Me.Txt_StopMachineTime.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_StopMachineTime.Size = New System.Drawing.Size(384, 41)
        Me.Txt_StopMachineTime.TabIndex = 113
        Me.Txt_StopMachineTime.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Txt_Who_identified_problem
        '
        Me.Txt_Who_identified_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Who_identified_problem.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Who_identified_problem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Who_identified_problem.ForeColor = System.Drawing.Color.Black
        Me.Txt_Who_identified_problem.Location = New System.Drawing.Point(426, 102)
        Me.Txt_Who_identified_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Who_identified_problem.Multiline = True
        Me.Txt_Who_identified_problem.Name = "Txt_Who_identified_problem"
        Me.Txt_Who_identified_problem.ReadOnly = True
        Me.Txt_Who_identified_problem.Size = New System.Drawing.Size(384, 41)
        Me.Txt_Who_identified_problem.TabIndex = 115
        Me.Txt_Who_identified_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label7
        '
        Me.Label7.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label7.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label7.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label7.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label7.ForeColor = System.Drawing.Color.Black
        Me.Label7.Location = New System.Drawing.Point(816, 98)
        Me.Label7.Name = "Label7"
        Me.Label7.Size = New System.Drawing.Size(154, 49)
        Me.Label7.TabIndex = 114
        Me.Label7.Text = "من قام بتحديد المشكلة"
        Me.Label7.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Who_fixed_problem
        '
        Me.Txt_Who_fixed_problem.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_Who_fixed_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Who_fixed_problem.Font = New System.Drawing.Font("Questv1", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Txt_Who_fixed_problem.ForeColor = System.Drawing.Color.Red
        Me.Txt_Who_fixed_problem.Location = New System.Drawing.Point(6, 21)
        Me.Txt_Who_fixed_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Who_fixed_problem.Name = "Txt_Who_fixed_problem"
        Me.Txt_Who_fixed_problem.ReadOnly = True
        Me.Txt_Who_fixed_problem.Size = New System.Drawing.Size(59, 41)
        Me.Txt_Who_fixed_problem.TabIndex = 119
        Me.Txt_Who_fixed_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_Who_fixed_problem.Visible = False
        '
        'Label8
        '
        Me.Label8.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label8.BackColor = System.Drawing.Color.FromArgb(CType(CType(1, Byte), Integer), CType(CType(17, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.Label8.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label8.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Bold)
        Me.Label8.ForeColor = System.Drawing.Color.White
        Me.Label8.Location = New System.Drawing.Point(71, 24)
        Me.Label8.Name = "Label8"
        Me.Label8.Size = New System.Drawing.Size(48, 32)
        Me.Label8.TabIndex = 118
        Me.Label8.Text = "من اصلح العطل"
        Me.Label8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label8.Visible = False
        '
        'Txt_Technician_name
        '
        Me.Txt_Technician_name.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_Technician_name.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Technician_name.Font = New System.Drawing.Font("Questv1", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Txt_Technician_name.ForeColor = System.Drawing.Color.Red
        Me.Txt_Technician_name.Location = New System.Drawing.Point(6, 64)
        Me.Txt_Technician_name.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Technician_name.Name = "Txt_Technician_name"
        Me.Txt_Technician_name.ReadOnly = True
        Me.Txt_Technician_name.Size = New System.Drawing.Size(59, 41)
        Me.Txt_Technician_name.TabIndex = 117
        Me.Txt_Technician_name.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_Technician_name.Visible = False
        '
        'Label9
        '
        Me.Label9.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label9.BackColor = System.Drawing.Color.FromArgb(CType(CType(1, Byte), Integer), CType(CType(17, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.Label9.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label9.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Bold)
        Me.Label9.ForeColor = System.Drawing.Color.White
        Me.Label9.Location = New System.Drawing.Point(71, 67)
        Me.Label9.Name = "Label9"
        Me.Label9.Size = New System.Drawing.Size(48, 32)
        Me.Label9.TabIndex = 116
        Me.Label9.Text = "اسم الفنى"
        Me.Label9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label9.Visible = False
        '
        'Txt_Unity_problem
        '
        Me.Txt_Unity_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Unity_problem.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Unity_problem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Unity_problem.ForeColor = System.Drawing.Color.Black
        Me.Txt_Unity_problem.Location = New System.Drawing.Point(3, 4)
        Me.Txt_Unity_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Unity_problem.Multiline = True
        Me.Txt_Unity_problem.Name = "Txt_Unity_problem"
        Me.Txt_Unity_problem.ReadOnly = True
        Me.Txt_Unity_problem.Size = New System.Drawing.Size(281, 41)
        Me.Txt_Unity_problem.TabIndex = 127
        Me.Txt_Unity_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label10
        '
        Me.Label10.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label10.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label10.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label10.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label10.ForeColor = System.Drawing.Color.Black
        Me.Label10.Location = New System.Drawing.Point(290, 0)
        Me.Label10.Name = "Label10"
        Me.Label10.Size = New System.Drawing.Size(130, 49)
        Me.Label10.TabIndex = 126
        Me.Label10.Text = "وحدة العطل"
        Me.Label10.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Time_fix_problem
        '
        Me.Txt_Time_fix_problem.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_Time_fix_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Time_fix_problem.Font = New System.Drawing.Font("Questv1", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Txt_Time_fix_problem.ForeColor = System.Drawing.Color.Red
        Me.Txt_Time_fix_problem.Location = New System.Drawing.Point(6, 191)
        Me.Txt_Time_fix_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Time_fix_problem.Name = "Txt_Time_fix_problem"
        Me.Txt_Time_fix_problem.ReadOnly = True
        Me.Txt_Time_fix_problem.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Time_fix_problem.Size = New System.Drawing.Size(59, 41)
        Me.Txt_Time_fix_problem.TabIndex = 125
        Me.Txt_Time_fix_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_Time_fix_problem.Visible = False
        '
        'Label11
        '
        Me.Label11.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label11.BackColor = System.Drawing.Color.FromArgb(CType(CType(1, Byte), Integer), CType(CType(17, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.Label11.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label11.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Bold)
        Me.Label11.ForeColor = System.Drawing.Color.White
        Me.Label11.Location = New System.Drawing.Point(71, 196)
        Me.Label11.Name = "Label11"
        Me.Label11.Size = New System.Drawing.Size(48, 32)
        Me.Label11.TabIndex = 124
        Me.Label11.Text = "وقت اصلاح العطل"
        Me.Label11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label11.Visible = False
        '
        'Txt_Technician_attendance_time
        '
        Me.Txt_Technician_attendance_time.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_Technician_attendance_time.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Technician_attendance_time.Font = New System.Drawing.Font("Questv1", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Txt_Technician_attendance_time.ForeColor = System.Drawing.Color.Red
        Me.Txt_Technician_attendance_time.Location = New System.Drawing.Point(6, 106)
        Me.Txt_Technician_attendance_time.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Technician_attendance_time.Name = "Txt_Technician_attendance_time"
        Me.Txt_Technician_attendance_time.ReadOnly = True
        Me.Txt_Technician_attendance_time.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Technician_attendance_time.Size = New System.Drawing.Size(59, 41)
        Me.Txt_Technician_attendance_time.TabIndex = 123
        Me.Txt_Technician_attendance_time.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_Technician_attendance_time.Visible = False
        '
        'Label12
        '
        Me.Label12.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label12.BackColor = System.Drawing.Color.FromArgb(CType(CType(1, Byte), Integer), CType(CType(17, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.Label12.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label12.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Bold)
        Me.Label12.ForeColor = System.Drawing.Color.White
        Me.Label12.Location = New System.Drawing.Point(71, 109)
        Me.Label12.Name = "Label12"
        Me.Label12.Size = New System.Drawing.Size(48, 32)
        Me.Label12.TabIndex = 122
        Me.Label12.Text = "وقت حضور الفنى"
        Me.Label12.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label12.Visible = False
        '
        'Txt_Time_identify_problem
        '
        Me.Txt_Time_identify_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Time_identify_problem.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Time_identify_problem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Time_identify_problem.ForeColor = System.Drawing.Color.Black
        Me.Txt_Time_identify_problem.Location = New System.Drawing.Point(426, 151)
        Me.Txt_Time_identify_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Time_identify_problem.Multiline = True
        Me.Txt_Time_identify_problem.Name = "Txt_Time_identify_problem"
        Me.Txt_Time_identify_problem.ReadOnly = True
        Me.Txt_Time_identify_problem.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Time_identify_problem.Size = New System.Drawing.Size(384, 41)
        Me.Txt_Time_identify_problem.TabIndex = 121
        Me.Txt_Time_identify_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label13
        '
        Me.Label13.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label13.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label13.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label13.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label13.ForeColor = System.Drawing.Color.Black
        Me.Label13.Location = New System.Drawing.Point(816, 147)
        Me.Label13.Name = "Label13"
        Me.Label13.Size = New System.Drawing.Size(154, 49)
        Me.Label13.TabIndex = 120
        Me.Label13.Text = "وقت تحديد المشكلة"
        Me.Label13.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Time_write_notes
        '
        Me.Txt_Time_write_notes.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_Time_write_notes.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Time_write_notes.Font = New System.Drawing.Font("Questv1", 12.0!, System.Drawing.FontStyle.Bold)
        Me.Txt_Time_write_notes.ForeColor = System.Drawing.Color.Red
        Me.Txt_Time_write_notes.Location = New System.Drawing.Point(6, 148)
        Me.Txt_Time_write_notes.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Time_write_notes.Name = "Txt_Time_write_notes"
        Me.Txt_Time_write_notes.ReadOnly = True
        Me.Txt_Time_write_notes.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Time_write_notes.Size = New System.Drawing.Size(59, 41)
        Me.Txt_Time_write_notes.TabIndex = 135
        Me.Txt_Time_write_notes.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_Time_write_notes.Visible = False
        '
        'Label14
        '
        Me.Label14.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label14.BackColor = System.Drawing.Color.FromArgb(CType(CType(1, Byte), Integer), CType(CType(17, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.Label14.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label14.Font = New System.Drawing.Font("Hacen Saudi Arabia", 7.8!, System.Drawing.FontStyle.Bold)
        Me.Label14.ForeColor = System.Drawing.Color.White
        Me.Label14.Location = New System.Drawing.Point(71, 148)
        Me.Label14.Name = "Label14"
        Me.Label14.Size = New System.Drawing.Size(48, 32)
        Me.Label14.TabIndex = 134
        Me.Label14.Text = "وقت كتابة الملاحظات"
        Me.Label14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label14.Visible = False
        '
        'Txt_Notes
        '
        Me.Txt_Notes.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Notes.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Notes.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Notes.ForeColor = System.Drawing.Color.Black
        Me.Txt_Notes.Location = New System.Drawing.Point(3, 4)
        Me.Txt_Notes.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Notes.Multiline = True
        Me.Txt_Notes.Name = "Txt_Notes"
        Me.Txt_Notes.ReadOnly = True
        Me.Txt_Notes.Size = New System.Drawing.Size(809, 40)
        Me.Txt_Notes.TabIndex = 131
        Me.Txt_Notes.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label16
        '
        Me.Label16.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label16.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label16.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label16.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label16.ForeColor = System.Drawing.Color.Black
        Me.Label16.Location = New System.Drawing.Point(818, 0)
        Me.Label16.Name = "Label16"
        Me.Label16.Size = New System.Drawing.Size(152, 48)
        Me.Label16.TabIndex = 130
        Me.Label16.Text = "ملاحظات العطل"
        Me.Label16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Part_problem
        '
        Me.Txt_Part_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Part_problem.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Part_problem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Part_problem.ForeColor = System.Drawing.Color.Black
        Me.Txt_Part_problem.Location = New System.Drawing.Point(3, 53)
        Me.Txt_Part_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Part_problem.Multiline = True
        Me.Txt_Part_problem.Name = "Txt_Part_problem"
        Me.Txt_Part_problem.ReadOnly = True
        Me.Txt_Part_problem.Size = New System.Drawing.Size(281, 41)
        Me.Txt_Part_problem.TabIndex = 129
        Me.Txt_Part_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label17
        '
        Me.Label17.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label17.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label17.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label17.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label17.ForeColor = System.Drawing.Color.Black
        Me.Label17.Location = New System.Drawing.Point(290, 49)
        Me.Label17.Name = "Label17"
        Me.Label17.Size = New System.Drawing.Size(130, 49)
        Me.Label17.TabIndex = 128
        Me.Label17.Text = "جزء العطل"
        Me.Label17.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'GroupBox2
        '
        Me.GroupBox2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupBox2.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(119, Byte), Integer))
        Me.GroupBox2.Controls.Add(Me.TableLayoutPanel2)
        Me.GroupBox2.Controls.Add(Me.TableLayoutPanel1)
        Me.GroupBox2.Controls.Add(Me.Panel3)
        Me.GroupBox2.Controls.Add(Me.Label9)
        Me.GroupBox2.Controls.Add(Me.Txt_Technician_name)
        Me.GroupBox2.Controls.Add(Me.Label11)
        Me.GroupBox2.Controls.Add(Me.Txt_Time_fix_problem)
        Me.GroupBox2.Controls.Add(Me.Label8)
        Me.GroupBox2.Controls.Add(Me.Txt_Who_fixed_problem)
        Me.GroupBox2.Controls.Add(Me.Label12)
        Me.GroupBox2.Controls.Add(Me.Txt_Technician_attendance_time)
        Me.GroupBox2.Controls.Add(Me.Label14)
        Me.GroupBox2.Controls.Add(Me.Txt_Time_write_notes)
        Me.GroupBox2.FlatStyle = System.Windows.Forms.FlatStyle.System
        Me.GroupBox2.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupBox2.Location = New System.Drawing.Point(318, 57)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.GroupBox2.Size = New System.Drawing.Size(1104, 365)
        Me.GroupBox2.TabIndex = 136
        Me.GroupBox2.TabStop = False
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel2.ColumnCount = 2
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 16.34121!))
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 83.65879!))
        Me.TableLayoutPanel2.Controls.Add(Me.Label16, 0, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.Txt_Notes, 1, 0)
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(125, 264)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 1
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(973, 48)
        Me.TableLayoutPanel2.TabIndex = 150
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.ColumnCount = 4
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 16.50485!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 40.12945!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 14.02373!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 29.34196!))
        Me.TableLayoutPanel1.Controls.Add(Me.Label21, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Proplem_Code, 1, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Label5, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label7, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label13, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Label22, 0, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_StopMachineTime, 1, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Who_identified_problem, 1, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Who_Note_writer, 3, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Time_identify_problem, 1, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Department_Proplem, 1, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Label19, 2, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Machine_running_time, 3, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Problem_status, 3, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Label10, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Label17, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.Label23, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.Lbl_Who_Note_writer, 2, 4)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Unity_problem, 3, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.Txt_Part_problem, 3, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(125, 15)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 5
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(973, 246)
        Me.TableLayoutPanel1.TabIndex = 149
        '
        'Label21
        '
        Me.Label21.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label21.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label21.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label21.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label21.ForeColor = System.Drawing.Color.Black
        Me.Label21.Location = New System.Drawing.Point(816, 0)
        Me.Label21.Name = "Label21"
        Me.Label21.Size = New System.Drawing.Size(154, 49)
        Me.Label21.TabIndex = 142
        Me.Label21.Text = "كود العطل"
        Me.Label21.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Proplem_Code
        '
        Me.Txt_Proplem_Code.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Proplem_Code.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Proplem_Code.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Proplem_Code.ForeColor = System.Drawing.Color.Black
        Me.Txt_Proplem_Code.Location = New System.Drawing.Point(426, 4)
        Me.Txt_Proplem_Code.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Proplem_Code.Multiline = True
        Me.Txt_Proplem_Code.Name = "Txt_Proplem_Code"
        Me.Txt_Proplem_Code.ReadOnly = True
        Me.Txt_Proplem_Code.Size = New System.Drawing.Size(384, 41)
        Me.Txt_Proplem_Code.TabIndex = 143
        Me.Txt_Proplem_Code.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label22
        '
        Me.Label22.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label22.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label22.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label22.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label22.ForeColor = System.Drawing.Color.Black
        Me.Label22.Location = New System.Drawing.Point(816, 196)
        Me.Label22.Name = "Label22"
        Me.Label22.Size = New System.Drawing.Size(154, 50)
        Me.Label22.TabIndex = 138
        Me.Label22.Text = "قسم المشكلة"
        Me.Label22.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Who_Note_writer
        '
        Me.Txt_Who_Note_writer.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Who_Note_writer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Who_Note_writer.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Who_Note_writer.ForeColor = System.Drawing.Color.Black
        Me.Txt_Who_Note_writer.Location = New System.Drawing.Point(3, 200)
        Me.Txt_Who_Note_writer.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Who_Note_writer.Multiline = True
        Me.Txt_Who_Note_writer.Name = "Txt_Who_Note_writer"
        Me.Txt_Who_Note_writer.ReadOnly = True
        Me.Txt_Who_Note_writer.Size = New System.Drawing.Size(281, 42)
        Me.Txt_Who_Note_writer.TabIndex = 137
        Me.Txt_Who_Note_writer.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Txt_Department_Proplem
        '
        Me.Txt_Department_Proplem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Department_Proplem.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Department_Proplem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Department_Proplem.ForeColor = System.Drawing.Color.Black
        Me.Txt_Department_Proplem.Location = New System.Drawing.Point(426, 200)
        Me.Txt_Department_Proplem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Department_Proplem.Multiline = True
        Me.Txt_Department_Proplem.Name = "Txt_Department_Proplem"
        Me.Txt_Department_Proplem.ReadOnly = True
        Me.Txt_Department_Proplem.Size = New System.Drawing.Size(384, 42)
        Me.Txt_Department_Proplem.TabIndex = 139
        Me.Txt_Department_Proplem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label19
        '
        Me.Label19.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label19.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label19.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label19.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label19.ForeColor = System.Drawing.Color.Black
        Me.Label19.Location = New System.Drawing.Point(290, 147)
        Me.Label19.Name = "Label19"
        Me.Label19.Size = New System.Drawing.Size(130, 49)
        Me.Label19.TabIndex = 144
        Me.Label19.Text = "وقت تشغيل الماكينة"
        Me.Label19.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Machine_running_time
        '
        Me.Txt_Machine_running_time.BackColor = System.Drawing.Color.Lime
        Me.Txt_Machine_running_time.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Machine_running_time.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Machine_running_time.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Machine_running_time.ForeColor = System.Drawing.Color.Black
        Me.Txt_Machine_running_time.Location = New System.Drawing.Point(3, 151)
        Me.Txt_Machine_running_time.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Machine_running_time.Multiline = True
        Me.Txt_Machine_running_time.Name = "Txt_Machine_running_time"
        Me.Txt_Machine_running_time.ReadOnly = True
        Me.Txt_Machine_running_time.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Machine_running_time.Size = New System.Drawing.Size(281, 41)
        Me.Txt_Machine_running_time.TabIndex = 145
        Me.Txt_Machine_running_time.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Txt_Problem_status
        '
        Me.Txt_Problem_status.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Problem_status.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Txt_Problem_status.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Problem_status.ForeColor = System.Drawing.Color.Black
        Me.Txt_Problem_status.Location = New System.Drawing.Point(3, 102)
        Me.Txt_Problem_status.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Problem_status.Multiline = True
        Me.Txt_Problem_status.Name = "Txt_Problem_status"
        Me.Txt_Problem_status.ReadOnly = True
        Me.Txt_Problem_status.Size = New System.Drawing.Size(281, 41)
        Me.Txt_Problem_status.TabIndex = 141
        Me.Txt_Problem_status.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label23
        '
        Me.Label23.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label23.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label23.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Label23.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label23.ForeColor = System.Drawing.Color.Black
        Me.Label23.Location = New System.Drawing.Point(290, 98)
        Me.Label23.Name = "Label23"
        Me.Label23.Size = New System.Drawing.Size(130, 49)
        Me.Label23.TabIndex = 140
        Me.Label23.Text = "حالة العطل"
        Me.Label23.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Lbl_Who_Note_writer
        '
        Me.Lbl_Who_Note_writer.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Lbl_Who_Note_writer.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Lbl_Who_Note_writer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Lbl_Who_Note_writer.Font = New System.Drawing.Font("Calibri", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Lbl_Who_Note_writer.ForeColor = System.Drawing.Color.Black
        Me.Lbl_Who_Note_writer.Location = New System.Drawing.Point(290, 196)
        Me.Lbl_Who_Note_writer.Name = "Lbl_Who_Note_writer"
        Me.Lbl_Who_Note_writer.Size = New System.Drawing.Size(130, 50)
        Me.Lbl_Who_Note_writer.TabIndex = 136
        Me.Lbl_Who_Note_writer.Text = "كاتب الملاحظات"
        Me.Lbl_Who_Note_writer.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel3.Controls.Add(Me.Label25)
        Me.Panel3.Controls.Add(Me.Txt_Total_time_problem)
        Me.Panel3.Controls.Add(Me.Label26)
        Me.Panel3.Controls.Add(Me.Txt_Total_time_solve_problem)
        Me.Panel3.Controls.Add(Me.Txt_Total_response_time)
        Me.Panel3.Controls.Add(Me.Label24)
        Me.Panel3.Location = New System.Drawing.Point(122, 315)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(976, 47)
        Me.Panel3.TabIndex = 148
        '
        'Label25
        '
        Me.Label25.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label25.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label25.Font = New System.Drawing.Font("Calibri", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label25.ForeColor = System.Drawing.Color.Black
        Me.Label25.Location = New System.Drawing.Point(198, 5)
        Me.Label25.Name = "Label25"
        Me.Label25.Size = New System.Drawing.Size(170, 32)
        Me.Label25.TabIndex = 150
        Me.Label25.Text = "الوقت الكلى للمشكلة"
        Me.Label25.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Txt_Total_time_problem
        '
        Me.Txt_Total_time_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Total_time_problem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Total_time_problem.ForeColor = System.Drawing.Color.Red
        Me.Txt_Total_time_problem.Location = New System.Drawing.Point(3, 4)
        Me.Txt_Total_time_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Total_time_problem.Name = "Txt_Total_time_problem"
        Me.Txt_Total_time_problem.ReadOnly = True
        Me.Txt_Total_time_problem.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Total_time_problem.Size = New System.Drawing.Size(189, 32)
        Me.Txt_Total_time_problem.TabIndex = 151
        Me.Txt_Total_time_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        '
        'Label26
        '
        Me.Label26.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label26.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label26.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label26.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label26.ForeColor = System.Drawing.Color.Black
        Me.Label26.Location = New System.Drawing.Point(532, 5)
        Me.Label26.Name = "Label26"
        Me.Label26.Size = New System.Drawing.Size(135, 32)
        Me.Label26.TabIndex = 148
        Me.Label26.Text = "الوقت الكلى لحل العطل"
        Me.Label26.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label26.Visible = False
        '
        'Txt_Total_time_solve_problem
        '
        Me.Txt_Total_time_solve_problem.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_Total_time_solve_problem.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Total_time_solve_problem.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Total_time_solve_problem.Location = New System.Drawing.Point(380, 4)
        Me.Txt_Total_time_solve_problem.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Total_time_solve_problem.Name = "Txt_Total_time_solve_problem"
        Me.Txt_Total_time_solve_problem.ReadOnly = True
        Me.Txt_Total_time_solve_problem.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Total_time_solve_problem.Size = New System.Drawing.Size(146, 32)
        Me.Txt_Total_time_solve_problem.TabIndex = 149
        Me.Txt_Total_time_solve_problem.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_Total_time_solve_problem.Visible = False
        '
        'Txt_Total_response_time
        '
        Me.Txt_Total_response_time.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_Total_response_time.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.Txt_Total_response_time.Font = New System.Drawing.Font("Calibri", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_Total_response_time.Location = New System.Drawing.Point(673, 5)
        Me.Txt_Total_response_time.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_Total_response_time.Name = "Txt_Total_response_time"
        Me.Txt_Total_response_time.ReadOnly = True
        Me.Txt_Total_response_time.RightToLeft = System.Windows.Forms.RightToLeft.No
        Me.Txt_Total_response_time.Size = New System.Drawing.Size(143, 32)
        Me.Txt_Total_response_time.TabIndex = 132
        Me.Txt_Total_response_time.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_Total_response_time.Visible = False
        Me.Txt_Total_response_time.WordWrap = False
        '
        'Label24
        '
        Me.Label24.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label24.BackColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(190, Byte), Integer))
        Me.Label24.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D
        Me.Label24.Font = New System.Drawing.Font("Calibri", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label24.ForeColor = System.Drawing.Color.Black
        Me.Label24.Location = New System.Drawing.Point(822, 5)
        Me.Label24.Name = "Label24"
        Me.Label24.Size = New System.Drawing.Size(151, 32)
        Me.Label24.TabIndex = 131
        Me.Label24.Text = "الوقت الكلى للإستجابة"
        Me.Label24.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label24.Visible = False
        '
        'Label18
        '
        Me.Label18.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Label18.Font = New System.Drawing.Font("Calibri", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Label18.Location = New System.Drawing.Point(1325, 741)
        Me.Label18.Name = "Label18"
        Me.Label18.Size = New System.Drawing.Size(101, 23)
        Me.Label18.TabIndex = 142
        Me.Label18.Text = "كود المستخدم الحالى"
        Me.Label18.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Label18.Visible = False
        '
        'Txt_User
        '
        Me.Txt_User.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_User.Enabled = False
        Me.Txt_User.Font = New System.Drawing.Font("Calibri", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_User.Location = New System.Drawing.Point(1120, 735)
        Me.Txt_User.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_User.Name = "Txt_User"
        Me.Txt_User.ReadOnly = True
        Me.Txt_User.Size = New System.Drawing.Size(182, 28)
        Me.Txt_User.TabIndex = 143
        Me.Txt_User.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_User.Visible = False
        '
        'Usernamelbl
        '
        Me.Usernamelbl.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Usernamelbl.Font = New System.Drawing.Font("Calibri", 10.2!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Usernamelbl.Location = New System.Drawing.Point(628, 741)
        Me.Usernamelbl.Name = "Usernamelbl"
        Me.Usernamelbl.Size = New System.Drawing.Size(147, 23)
        Me.Usernamelbl.TabIndex = 140
        Me.Usernamelbl.Text = "اسم المستخدم الحالى"
        Me.Usernamelbl.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.Usernamelbl.Visible = False
        '
        'Txt_UserID
        '
        Me.Txt_UserID.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Txt_UserID.Enabled = False
        Me.Txt_UserID.Font = New System.Drawing.Font("Calibri", 7.8!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Txt_UserID.Location = New System.Drawing.Point(440, 741)
        Me.Txt_UserID.Margin = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.Txt_UserID.Name = "Txt_UserID"
        Me.Txt_UserID.ReadOnly = True
        Me.Txt_UserID.Size = New System.Drawing.Size(182, 23)
        Me.Txt_UserID.TabIndex = 141
        Me.Txt_UserID.TextAlign = System.Windows.Forms.HorizontalAlignment.Center
        Me.Txt_UserID.Visible = False
        '
        'Frm_Quiry_ForEmp
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(12.0!, 32.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(109, Byte), Integer), CType(CType(119, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(1434, 769)
        Me.ControlBox = False
        Me.Controls.Add(Me.Check_AllMachine)
        Me.Controls.Add(Me.Usernamelbl)
        Me.Controls.Add(Me.Label18)
        Me.Controls.Add(Me.Txt_User)
        Me.Controls.Add(Me.Txt_UserID)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.Txt_EndDate)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.Controls.Add(Me.Label4)
        Me.Controls.Add(Me.Label3)
        Me.Controls.Add(Me.Txt_StartDate)
        Me.Font = New System.Drawing.Font("Hacen Saudi Arabia", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Margin = New System.Windows.Forms.Padding(4, 5, 4, 5)
        Me.MinimizeBox = False
        Me.Name = "Frm_Quiry_ForEmp"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.ShowIcon = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        Me.Panel1.ResumeLayout(False)
        CType(Me.PictureBoxClose, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Panel2.ResumeLayout(False)
        CType(Me.dgv_Data, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.TableLayoutPanel2.ResumeLayout(False)
        Me.TableLayoutPanel2.PerformLayout()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TableLayoutPanel1.PerformLayout()
        Me.Panel3.ResumeLayout(False)
        Me.Panel3.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents Panel1 As Panel
    Friend WithEvents Label1 As Label
    Friend WithEvents PictureBoxClose As PictureBox
    Friend WithEvents Panel2 As Panel
    Friend WithEvents Label6 As Label
    Friend WithEvents Combo_Dept As ComboBox
    Friend WithEvents Label2 As Label
    Friend WithEvents Combo_Machine As ComboBox
    Friend WithEvents Label3 As Label
    Friend WithEvents Label4 As Label
    Friend WithEvents Txt_StartDate As DateTimePicker
    Friend WithEvents Txt_EndDate As DateTimePicker
    Friend WithEvents Btn_Search As Button
    Friend WithEvents GroupBox1 As GroupBox
    Friend WithEvents Label5 As Label
    Friend WithEvents Txt_StopMachineTime As TextBox
    Friend WithEvents Txt_Who_identified_problem As TextBox
    Friend WithEvents Label7 As Label
    Friend WithEvents Txt_Who_fixed_problem As TextBox
    Friend WithEvents Label8 As Label
    Friend WithEvents Txt_Technician_name As TextBox
    Friend WithEvents Label9 As Label
    Friend WithEvents Txt_Unity_problem As TextBox
    Friend WithEvents Label10 As Label
    Friend WithEvents Txt_Time_fix_problem As TextBox
    Friend WithEvents Label11 As Label
    Friend WithEvents Txt_Technician_attendance_time As TextBox
    Friend WithEvents Label12 As Label
    Friend WithEvents Txt_Time_identify_problem As TextBox
    Friend WithEvents Label13 As Label
    Friend WithEvents Txt_Time_write_notes As TextBox
    Friend WithEvents Label14 As Label
    Friend WithEvents Label15 As Label
    Friend WithEvents Txt_Notes As TextBox
    Friend WithEvents Label16 As Label
    Friend WithEvents Txt_Part_problem As TextBox
    Friend WithEvents Label17 As Label
    Friend WithEvents GroupBox2 As GroupBox
    Friend WithEvents Panel3 As Panel
    Friend WithEvents Txt_Who_Note_writer As TextBox
    Friend WithEvents Label19 As Label
    Friend WithEvents Txt_Department_Proplem As TextBox
    Friend WithEvents Txt_Problem_status As TextBox
    Friend WithEvents Txt_Machine_running_time As TextBox
    Friend WithEvents Txt_Proplem_Code As TextBox
    Friend WithEvents Lbl_Who_Note_writer As Label
    Friend WithEvents Label21 As Label
    Friend WithEvents Label22 As Label
    Friend WithEvents Label23 As Label
    Friend WithEvents Label25 As Label
    Friend WithEvents Txt_Total_time_problem As TextBox
    Friend WithEvents Label26 As Label
    Friend WithEvents Txt_Total_time_solve_problem As TextBox
    Friend WithEvents Txt_Total_response_time As TextBox
    Friend WithEvents Label24 As Label
    Friend WithEvents Lbl_Count_Proplem As Label
    Friend WithEvents Lbl_Total_Time_All_Proplem As Label
    Friend WithEvents Label20 As Label
    Friend WithEvents Check_AllMachine As CheckBox
    Friend WithEvents Label18 As Label
    Friend WithEvents Txt_User As TextBox
    Friend WithEvents Usernamelbl As Label
    Friend WithEvents Txt_UserID As TextBox
    Friend WithEvents Label27 As Label
    Friend WithEvents Combo_Shift As ComboBox
    Friend WithEvents dgv_Data As DataGridView
    Friend WithEvents TableLayoutPanel1 As TableLayoutPanel
    Friend WithEvents TableLayoutPanel2 As TableLayoutPanel
End Class
