Imports System.IO
Imports System.Threading

''' <summary>
''' File-based logger implementation with thread-safe operations
''' Supports log rotation and configurable log levels
''' </summary>
Public Class FileLogger
    Implements ILogger
    Implements IDisposable

    Private ReadOnly _logFilePath As String
    Private ReadOnly _maxFileSize As Long = 10 * 1024 * 1024 ' 10MB
    Private ReadOnly _maxBackupFiles As Integer = 5
    Private ReadOnly _minLogLevel As LogLevel = LogLevel.Info
    Private ReadOnly _lock As New ReaderWriterLockSlim()
    Private _disposed As Boolean = False

    ''' <summary>
    ''' Constructor with default log file path
    ''' </summary>
    Public Sub New()
        Me.New(Path.Combine(Application.StartupPath, "Logs", "application.log"))
    End Sub

    ''' <summary>
    ''' Constructor with custom log file path
    ''' </summary>
    ''' <param name="logFilePath">Path to log file</param>
    Public Sub New(logFilePath As String)
        _logFilePath = logFilePath
        EnsureLogDirectoryExists()
    End Sub

    ''' <summary>
    ''' Constructor with custom settings
    ''' </summary>
    ''' <param name="logFilePath">Path to log file</param>
    ''' <param name="maxFileSize">Maximum file size before rotation</param>
    ''' <param name="maxBackupFiles">Maximum number of backup files</param>
    ''' <param name="minLogLevel">Minimum log level to write</param>
    Public Sub New(logFilePath As String, maxFileSize As Long, maxBackupFiles As Integer, minLogLevel As LogLevel)
        _logFilePath = logFilePath
        _maxFileSize = maxFileSize
        _maxBackupFiles = maxBackupFiles
        _minLogLevel = minLogLevel
        EnsureLogDirectoryExists()
    End Sub

    ''' <summary>
    ''' Log information message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Public Sub LogInfo(message As String) Implements ILogger.LogInfo
        WriteLog(LogLevel.Info, message)
    End Sub

    ''' <summary>
    ''' Log information message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Public Sub LogInfo(message As String, ParamArray args() As Object) Implements ILogger.LogInfo
        WriteLog(LogLevel.Info, String.Format(message, args))
    End Sub

    ''' <summary>
    ''' Log warning message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Public Sub LogWarning(message As String) Implements ILogger.LogWarning
        WriteLog(LogLevel.Warning, message)
    End Sub

    ''' <summary>
    ''' Log warning message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Public Sub LogWarning(message As String, ParamArray args() As Object) Implements ILogger.LogWarning
        WriteLog(LogLevel.Warning, String.Format(message, args))
    End Sub

    ''' <summary>
    ''' Log error message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Public Sub LogError(message As String) Implements ILogger.LogError
        WriteLog(LogLevel.Error, message)
    End Sub

    ''' <summary>
    ''' Log error message with exception
    ''' </summary>
    ''' <param name="message">Log message</param>
    ''' <param name="exception">Exception details</param>
    Public Sub LogError(message As String, exception As Exception) Implements ILogger.LogError
        Dim fullMessage As String = $"{message}{Environment.NewLine}Exception: {exception.ToString()}"
        WriteLog(LogLevel.Error, fullMessage)
    End Sub

    ''' <summary>
    ''' Log error message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Public Sub LogError(message As String, ParamArray args() As Object) Implements ILogger.LogError
        WriteLog(LogLevel.Error, String.Format(message, args))
    End Sub

    ''' <summary>
    ''' Log debug message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Public Sub LogDebug(message As String) Implements ILogger.LogDebug
        WriteLog(LogLevel.Debug, message)
    End Sub

    ''' <summary>
    ''' Log debug message with parameters
    ''' </summary>
    ''' <param name="message">Log message template</param>
    ''' <param name="args">Message parameters</param>
    Public Sub LogDebug(message As String, ParamArray args() As Object) Implements ILogger.LogDebug
        WriteLog(LogLevel.Debug, String.Format(message, args))
    End Sub

    ''' <summary>
    ''' Log critical message
    ''' </summary>
    ''' <param name="message">Log message</param>
    Public Sub LogCritical(message As String) Implements ILogger.LogCritical
        WriteLog(LogLevel.Critical, message)
    End Sub

    ''' <summary>
    ''' Log critical message with exception
    ''' </summary>
    ''' <param name="message">Log message</param>
    ''' <param name="exception">Exception details</param>
    Public Sub LogCritical(message As String, exception As Exception) Implements ILogger.LogCritical
        Dim fullMessage As String = $"{message}{Environment.NewLine}Exception: {exception.ToString()}"
        WriteLog(LogLevel.Critical, fullMessage)
    End Sub

    ''' <summary>
    ''' Check if log level is enabled
    ''' </summary>
    ''' <param name="level">Log level to check</param>
    ''' <returns>True if level is enabled</returns>
    Public Function IsEnabled(level As LogLevel) As Boolean Implements ILogger.IsEnabled
        Return level >= _minLogLevel
    End Function

    ''' <summary>
    ''' Write log entry to file
    ''' </summary>
    ''' <param name="level">Log level</param>
    ''' <param name="message">Log message</param>
    Private Sub WriteLog(level As LogLevel, message As String)
        If Not IsEnabled(level) OrElse _disposed Then
            Return
        End If

        Try
            _lock.EnterWriteLock()

            ' Check if log rotation is needed
            If File.Exists(_logFilePath) AndAlso New FileInfo(_logFilePath).Length > _maxFileSize Then
                RotateLogFiles()
            End If

            ' Format log entry
            Dim logEntry As String = FormatLogEntry(level, message)

            ' Write to file
            File.AppendAllText(_logFilePath, logEntry)

        Catch ex As Exception
            ' Fail silently to avoid infinite logging loops
            Console.WriteLine($"Failed to write to log file: {ex.Message}")
        Finally
            _lock.ExitWriteLock()
        End Try
    End Sub

    ''' <summary>
    ''' Format log entry with timestamp and level
    ''' </summary>
    ''' <param name="level">Log level</param>
    ''' <param name="message">Log message</param>
    ''' <returns>Formatted log entry</returns>
    Private Function FormatLogEntry(level As LogLevel, message As String) As String
        Dim timestamp As String = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")
        Dim levelString As String = level.ToString().ToUpper().PadRight(8)
        Dim threadId As String = Thread.CurrentThread.ManagedThreadId.ToString().PadLeft(3)
        
        Return $"{timestamp} [{levelString}] [T{threadId}] {message}{Environment.NewLine}"
    End Function

    ''' <summary>
    ''' Rotate log files when maximum size is reached
    ''' </summary>
    Private Sub RotateLogFiles()
        Try
            ' Remove oldest backup if we've reached the limit
            Dim oldestBackup As String = $"{_logFilePath}.{_maxBackupFiles}"
            If File.Exists(oldestBackup) Then
                File.Delete(oldestBackup)
            End If

            ' Shift existing backups
            For i As Integer = _maxBackupFiles - 1 To 1 Step -1
                Dim currentBackup As String = $"{_logFilePath}.{i}"
                Dim nextBackup As String = $"{_logFilePath}.{i + 1}"
                
                If File.Exists(currentBackup) Then
                    File.Move(currentBackup, nextBackup)
                End If
            Next

            ' Move current log to first backup
            If File.Exists(_logFilePath) Then
                File.Move(_logFilePath, $"{_logFilePath}.1")
            End If

        Catch ex As Exception
            ' Fail silently to avoid infinite logging loops
            Console.WriteLine($"Failed to rotate log files: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' Ensure log directory exists
    ''' </summary>
    Private Sub EnsureLogDirectoryExists()
        Try
            Dim directory As String = Path.GetDirectoryName(_logFilePath)
            If Not String.IsNullOrEmpty(directory) AndAlso Not Directory.Exists(directory) Then
                Directory.CreateDirectory(directory)
            End If
        Catch ex As Exception
            ' Fail silently
            Console.WriteLine($"Failed to create log directory: {ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' Dispose resources
    ''' </summary>
    Public Sub Dispose() Implements IDisposable.Dispose
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    ''' <summary>
    ''' Protected dispose method
    ''' </summary>
    ''' <param name="disposing">True if disposing managed resources</param>
    Protected Sub Dispose(disposing As Boolean)
        If Not _disposed Then
            If disposing Then
                _lock?.Dispose()
            End If
            _disposed = True
        End If
    End Sub

    ''' <summary>
    ''' Finalizer
    ''' </summary>
    Protected Overrides Sub Finalize()
        Dispose(False)
        MyBase.Finalize()
    End Sub
End Class
